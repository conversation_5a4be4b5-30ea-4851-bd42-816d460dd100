import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';
import '../../../services/product_service.dart';
import '../widgets/product_card.dart';
import '../widgets/product_search_bar.dart';
import '../widgets/product_actions_dialog.dart';
import '../widgets/add_product_dialog.dart';
import '../sample_data.dart';

class ProductsListScreen extends StatefulWidget {
  const ProductsListScreen({super.key});

  @override
  State<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends State<ProductsListScreen>
    with TickerProviderStateMixin {
  final ProductService _productService = ProductService();
  final TextEditingController _searchController = TextEditingController();

  List<ProductModel> _allProducts = [];
  List<ProductModel> _filteredProducts = [];
  bool _isLoading = true;
  bool _isSearching = false;
  bool _isGridView = false; // عرض الشبكة أو القائمة

  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  late AnimationController _listAnimationController;

  // متغيرات التصفية والترتيب
  String _selectedCategory = 'all';
  String _sortBy = 'name';
  bool _sortAscending = true;
  bool _showLowStockOnly = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSampleProducts(); // استخدام البيانات التجريبية
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fabAnimationController.forward();
    _listAnimationController.forward();
  }

  Future<void> _loadProducts() async {
    setState(() => _isLoading = true);

    try {
      // استخدام البيانات التجريبية بدلاً من قاعدة البيانات
      final products = SampleProductsData.getSampleProducts();
      setState(() {
        _allProducts = products;
        _filteredProducts = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل المنتجات: $e');
    }
  }

  /// تحميل البيانات التجريبية للمنتجات
  void _loadSampleProducts() {
    setState(() => _isLoading = true);

    try {
      // استخدام البيانات التجريبية
      final products = SampleProductsData.getSampleProducts();
      setState(() {
        _allProducts = products;
        _filteredProducts = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات التجريبية: $e');
    }
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts = _allProducts.where((product) {
        // تصفية حسب النص المدخل
        final searchQuery = _searchController.text.toLowerCase();
        final matchesSearch =
            searchQuery.isEmpty ||
            product.name.toLowerCase().contains(searchQuery) ||
            (product.barcode?.toLowerCase().contains(searchQuery) ?? false) ||
            product.code.toLowerCase().contains(searchQuery);

        // تصفية حسب الفئة
        final matchesCategory =
            _selectedCategory == 'all' || product.category == _selectedCategory;

        // تصفية حسب المخزون المنخفض
        final matchesStockFilter = !_showLowStockOnly || product.isLowStock;

        return matchesSearch && matchesCategory && matchesStockFilter;
      }).toList();

      // ترتيب المنتجات
      _sortProducts();
    });
  }

  void _sortProducts() {
    _filteredProducts.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'code':
          comparison = a.code.compareTo(b.code);
          break;
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'quantity':
          comparison = a.quantity.compareTo(b.quantity);
          break;
        case 'category':
          comparison = a.category.compareTo(b.category);
          break;
        default:
          comparison = a.name.compareTo(b.name);
      }

      return _sortAscending ? comparison : -comparison;
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _isSearching = query.isNotEmpty);
    _filterProducts();
  }

  void _onCategoryChanged(String category) {
    setState(() => _selectedCategory = category);
    _filterProducts();
  }

  void _onSortChanged(String sortBy) {
    setState(() => _sortBy = sortBy);
    _filterProducts();
  }

  void _onSortDirectionChanged() {
    setState(() => _sortAscending = !_sortAscending);
    _filterProducts();
  }

  void _onLowStockFilterChanged(bool value) {
    setState(() => _showLowStockOnly = value);
    _filterProducts();
  }

  Future<void> _showAddProductDialog() async {
    final result = await showDialog<ProductModel>(
      context: context,
      builder: (context) => const AddProductDialog(),
    );

    if (result != null) {
      // إضافة المنتج الجديد إلى البيانات التجريبية
      setState(() {
        _allProducts.add(result);
        _filterProducts();
      });
      _showSuccessSnackBar('تم إضافة المنتج بنجاح');
    }
  }

  Future<void> _onProductAction(ProductModel product, String action) async {
    switch (action) {
      case 'edit':
        await _editProduct(product);
        break;
      case 'delete':
        await _deleteProduct(product);
        break;
      case 'sell':
        await _sellProduct(product);
        break;
      case 'details':
        _showProductDetails(product);
        break;
      case 'adjust_quantity':
        _showQuantityAdjustDialog(product);
        break;
      case 'add_stock':
        await _addStockToProduct(product);
        break;
      case 'copy_code':
        await _copyProductCode(product);
        break;
      case 'copy_barcode':
        await _copyProductBarcode(product);
        break;
      case 'toggle_status':
        await _toggleProductStatus(product);
        break;
      default:
        print('إجراء غير معروف: $action');
    }
  }

  Future<void> _deleteProduct(ProductModel product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppStrings.deleteConfirmation),
        content: Text('هل أنت متأكد من حذف المنتج "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: Text(AppStrings.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // حذف المنتج من البيانات التجريبية
        setState(() {
          _allProducts.removeWhere((p) => p.id == product.id);
          _filterProducts();
        });
        _showSuccessSnackBar('تم حذف المنتج بنجاح');
      } catch (e) {
        _showErrorSnackBar('خطأ في حذف المنتج: $e');
      }
    }
  }

  /// تعديل المنتج
  Future<void> _editProduct(ProductModel product) async {
    // TODO: تنفيذ تعديل المنتج
    _showInfoSnackBar('سيتم إضافة ميزة تعديل المنتج قريباً');
  }

  /// بيع المنتج
  Future<void> _sellProduct(ProductModel product) async {
    if (product.quantity <= 0) {
      _showErrorSnackBar('لا يمكن بيع منتج نفذ مخزونه');
      return;
    }

    final quantity = await _showSellQuantityDialog(product);
    if (quantity != null && quantity > 0) {
      if (quantity > product.quantity) {
        _showErrorSnackBar('الكمية المطلوبة أكبر من المخزون المتوفر');
        return;
      }

      // TODO: تنفيذ عملية البيع
      _showSuccessSnackBar(
        'تم بيع $quantity ${product.unitDisplayName} من ${product.name}',
      );

      // تحديث الكمية في البيانات التجريبية
      setState(() {
        // إنشاء منتج جديد مع الكمية المحدثة
        final updatedProduct = product.copyWith(
          quantity: product.quantity - quantity,
        );
        final index = _allProducts.indexWhere((p) => p.id == product.id);
        if (index != -1) {
          _allProducts[index] = updatedProduct;
          _filterProducts();
        }
      });
    }
  }

  /// إضافة مخزون للمنتج
  Future<void> _addStockToProduct(ProductModel product) async {
    final quantity = await _showAddStockDialog(product);
    if (quantity != null && quantity > 0) {
      // TODO: تنفيذ إضافة المخزون
      setState(() {
        // إنشاء منتج جديد مع الكمية المحدثة
        final updatedProduct = product.copyWith(
          quantity: product.quantity + quantity,
        );
        final index = _allProducts.indexWhere((p) => p.id == product.id);
        if (index != -1) {
          _allProducts[index] = updatedProduct;
          _filterProducts();
        }
      });
      _showSuccessSnackBar(
        'تم إضافة $quantity ${product.unitDisplayName} إلى ${product.name}',
      );
    }
  }

  /// نسخ كود المنتج
  Future<void> _copyProductCode(ProductModel product) async {
    await Clipboard.setData(ClipboardData(text: product.code));
    _showSuccessSnackBar('تم نسخ كود المنتج: ${product.code}');
  }

  /// نسخ باركود المنتج
  Future<void> _copyProductBarcode(ProductModel product) async {
    if (product.barcode != null) {
      await Clipboard.setData(ClipboardData(text: product.barcode!));
      _showSuccessSnackBar('تم نسخ الباركود: ${product.barcode}');
    } else {
      _showErrorSnackBar('هذا المنتج لا يحتوي على باركود');
    }
  }

  /// تبديل حالة المنتج
  Future<void> _toggleProductStatus(ProductModel product) async {
    // TODO: تنفيذ تبديل حالة المنتج
    _showInfoSnackBar('سيتم إضافة ميزة تبديل حالة المنتج قريباً');
  }

  /// عرض نافذة بيع المنتج
  Future<int?> _showSellQuantityDialog(ProductModel product) async {
    final quantityController = TextEditingController();
    return showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('بيع ${product.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'المخزون المتوفر: ${product.quantity} ${product.unitDisplayName}',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الكمية المطلوبة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final quantity = int.tryParse(quantityController.text);
              Navigator.of(context).pop(quantity);
            },
            child: const Text('بيع'),
          ),
        ],
      ),
    );
  }

  /// عرض نافذة إضافة مخزون
  Future<int?> _showAddStockDialog(ProductModel product) async {
    final quantityController = TextEditingController();
    return showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة مخزون لـ ${product.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'المخزون الحالي: ${product.quantity} ${product.unitDisplayName}',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الكمية المضافة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final quantity = int.tryParse(quantityController.text);
              Navigator.of(context).pop(quantity);
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showProductDetails(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(product.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الكود', product.code),
              if (product.barcode != null)
                _buildDetailRow('الباركود', product.barcode!),
              _buildDetailRow('الفئة', product.categoryDisplayName),
              _buildDetailRow('الوصف', product.description),
              _buildDetailRow(
                'السعر',
                '${product.price} ${AppStrings.currencySymbol}',
              ),
              _buildDetailRow(
                'سعر التكلفة',
                '${product.cost} ${AppStrings.currencySymbol}',
              ),
              _buildDetailRow(
                'الكمية',
                '${product.quantity} ${product.unitDisplayName}',
              ),
              _buildDetailRow(
                'الحد الأدنى',
                '${product.minQuantity} ${product.unitDisplayName}',
              ),
              _buildDetailRow('حالة المخزون', product.stockStatus),
              _buildDetailRow(
                'هامش الربح',
                '${product.profitMargin.toStringAsFixed(1)}%',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  void _showQuantityAdjustDialog(ProductModel product) {
    final quantityController = TextEditingController(
      text: product.quantity.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل الكمية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('المنتج: ${product.name}'),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'الكمية الجديدة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppStrings.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: تنفيذ تعديل الكمية
              Navigator.of(context).pop();
              _showSuccessSnackBar('تم تعديل الكمية بنجاح');
            },
            child: Text(AppStrings.save),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _listAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryDark],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.inventory_2,
                color: AppColors.textOnPrimary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.products,
                  style: const TextStyle(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                if (_filteredProducts.isNotEmpty)
                  Text(
                    '${_filteredProducts.length} منتج',
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ],
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        centerTitle: false,
        actions: [
          // زر تبديل العرض مع تصميم محسن
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _isGridView = !_isGridView;
                });
                HapticFeedback.lightImpact();
              },
              icon: Icon(
                _isGridView ? Icons.view_list : Icons.grid_view,
                color: AppColors.primary,
                size: 22,
              ),
              tooltip: _isGridView ? 'عرض القائمة' : 'عرض الشبكة',
            ),
          ),
          // زر التحديث مع تصميم محسن
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.success.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () {
                _loadProducts();
                HapticFeedback.lightImpact();
              },
              icon: const Icon(
                Icons.refresh,
                color: AppColors.success,
                size: 22,
              ),
              tooltip: 'تحديث',
            ),
          ),
          const SizedBox(width: 12),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية
          ProductSearchBar(
            searchController: _searchController,
            selectedCategory: _selectedCategory,
            sortBy: _sortBy,
            sortAscending: _sortAscending,
            showLowStockOnly: _showLowStockOnly,
            onSearchChanged: _onSearchChanged,
            onCategoryChanged: _onCategoryChanged,
            onSortChanged: _onSortChanged,
            onSortDirectionChanged: _onSortDirectionChanged,
            onLowStockFilterChanged: _onLowStockFilterChanged,
          ),

          // إحصائيات سريعة
          _buildQuickStats(),

          // قائمة المنتجات
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _filteredProducts.isEmpty
                ? _buildEmptyState()
                : _buildProductsList(),
          ),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabScaleAnimation,
        child: FloatingActionButton.extended(
          onPressed: () {
            HapticFeedback.mediumImpact();
            _showAddProductDialog();
          },
          backgroundColor: AppColors.primary,
          elevation: 12,
          extendedPadding: const EdgeInsets.symmetric(horizontal: 20),
          icon: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.add,
              color: AppColors.textOnPrimary,
              size: 24,
            ),
          ),
          label: const Text(
            'إضافة منتج',
            style: TextStyle(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    if (_filteredProducts.isEmpty) return const SizedBox.shrink();

    final totalProducts = _filteredProducts.length;
    final lowStockProducts = _filteredProducts
        .where((p) => p.isLowStock)
        .length;
    final outOfStockProducts = _filteredProducts
        .where((p) => p.quantity == 0)
        .length;
    final totalValue = _filteredProducts.fold<double>(
      0,
      (sum, p) => sum + (p.price * p.quantity),
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.secondary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: AppColors.primary.withOpacity(0.2), width: 1),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'إجمالي المنتجات',
                totalProducts.toString(),
                Icons.inventory_2,
                AppColors.primary,
                onTap: () => _onCategoryChanged('all'),
              ),
              _buildStatItem(
                'مخزون منخفض',
                lowStockProducts.toString(),
                Icons.warning_amber,
                AppColors.warning,
                onTap: () => _onLowStockFilterChanged(true),
              ),
              _buildStatItem(
                'نفد المخزون',
                outOfStockProducts.toString(),
                Icons.error,
                AppColors.error,
                onTap: () => _showOutOfStockProducts(),
              ),
            ],
          ),
          if (totalValue > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.success.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: AppColors.success,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'إجمالي قيمة المخزون: ${totalValue.toStringAsFixed(0)} ${AppStrings.currencySymbol}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap != null
          ? () {
              HapticFeedback.lightImpact();
              onTap();
            }
          : null,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _showOutOfStockProducts() {
    setState(() {
      _filteredProducts = _allProducts.where((p) => p.quantity == 0).toList();
    });
    _showInfoSnackBar('عرض المنتجات التي نفد مخزونها');
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // مؤشر التحميل مع تأثير متحرك
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.primary.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جارٍ تحميل المنتجات...',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى الانتظار',
            style: const TextStyle(fontSize: 14, color: AppColors.textHint),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة متحركة
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 800),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: 0.8 + (0.2 * value),
                  child: Opacity(
                    opacity: value,
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        _isSearching
                            ? Icons.search_off
                            : Icons.inventory_2_outlined,
                        size: 64,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // النص الرئيسي
            Text(
              _isSearching ? 'لا توجد نتائج بحث' : 'لا توجد منتجات',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // النص الفرعي
            Text(
              _isSearching
                  ? 'جرب البحث بكلمات مختلفة أو امسح الفلاتر'
                  : 'ابدأ بإضافة منتجاتك لإدارة المخزون بكفاءة',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // أزرار الإجراءات
            if (_isSearching) ...[
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _onSearchChanged('');
                  setState(() {
                    _selectedCategory = 'all';
                    _showLowStockOnly = false;
                  });
                  _filterProducts();
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('مسح البحث والفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              ),
            ] else ...[
              ElevatedButton.icon(
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _showAddProductDialog();
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة منتج جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    return RefreshIndicator(
      onRefresh: _loadProducts,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _isGridView ? _buildGridView() : _buildListView(),
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      key: const ValueKey('list_view'),
      padding: const EdgeInsets.all(16),
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 50)),
          curve: Curves.easeOutBack,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: ProductCard(
              product: product,
              onAction: (action) => _onProductAction(product, action),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      key: const ValueKey('grid_view'),
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getGridCrossAxisCount(),
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 30)),
          curve: Curves.easeOutBack,
          child: ProductCard(
            product: product,
            onAction: (action) => _onProductAction(product, action),
          ),
        );
      },
    );
  }

  int _getGridCrossAxisCount() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4;
    if (screenWidth > 800) return 3;
    if (screenWidth > 600) return 2;
    return 1;
  }
}
