# نظام إدارة حالة الدفع للفواتير - Atlas Medical Supplies

## نظرة عامة
تم تطوير نظام شامل لإدارة حالة الدفع للفواتير مع ميزات متقدمة للمزامنة والتصدير.

## الميزات الرئيسية

### 1. قسم اختيار حالة الدفع المحسن

#### خيارات حالة الدفع:
- **مدفوع بالكامل**: عند دفع المبلغ الكلي للفاتورة
- **مدفوع جزئيًا**: مع إمكانية إدخال المبلغ المدفوع
- **غير مدفوع**: للفواتير المعلقة
- **متأخر**: للفواتير التي تجاوزت تاريخ الاستحقاق

#### حقول إضافية:
- **المبلغ المدفوع**: إدخال المبلغ المدفوع فعلياً
- **ملاحظات الدفع**: إضافة ملاحظات حول عملية الدفع
- **تاريخ آخر دفعة**: تحديث تلقائي عند إدخال دفعة جديدة
- **المبلغ المتبقي**: حساب تلقائي للمبلغ المتبقي

### 2. نظام المزامنة مع Firebase

#### المزامنة التلقائية:
- حفظ الفاتورة في قاعدة بيانات SQLite المحلية أولاً
- محاولة المزامنة التلقائية مع Firebase إذا كان الإنترنت متاح
- تسجيل أخطاء المزامنة للفواتير التي فشلت في المزامنة

#### مزامنة يدوية:
- زر مزامنة منفصل للمزامنة اليدوية
- عرض عدد الفواتير المتزامنة بنجاح
- تقرير مفصل عن أخطاء المزامنة

#### إدارة حالة المزامنة:
- تتبع حالة كل فاتورة (متزامنة/غير متزامنة)
- إعادة محاولة المزامنة للفواتير الفاشلة
- تحديث تلقائي لحالة المزامنة

### 3. تصدير الفاتورة PDF

#### تنسيق PDF احترافي:
- رأس الفاتورة مع شعار الشركة
- معلومات العميل المفصلة
- جدول المنتجات منسق
- ملخص الفاتورة مع الضرائب والخصومات
- معلومات الدفع وحالة المدفوعات
- قسم الملاحظات

#### خيارات التصدير:
- **حفظ PDF**: حفظ الفاتورة كملف PDF في الجهاز
- **طباعة مباشرة**: طباعة الفاتورة مباشرة
- **مشاركة عبر واتساب**: إرسال PDF عبر واتساب
- **إرسال عبر البريد الإلكتروني**: إرسال PDF كملحق

### 4. واجهة مستخدم محسنة

#### تصميم متجاوب:
- تخطيط أفقي للشاشات الواسعة
- تخطيط عمودي للشاشات الضيقة
- أزرار منظمة في AppBar

#### تجربة مستخدم محسنة:
- رسائل تأكيد واضحة
- مؤشرات حالة الدفع ملونة
- تحديث تلقائي للحسابات
- تحقق من صحة البيانات

## البنية التقنية

### النماذج المحدثة:
```dart
class InvoiceModel {
  // حقول جديدة
  final DateTime? lastPaymentDate;
  final String? paymentNotes;
  
  // getter جديد
  double get remainingAmount => totalAmount - paidAmount;
}
```

### الخدمات الجديدة:
```dart
class InvoiceService {
  // مزامنة Firebase
  Future<Map<String, dynamic>> syncInvoicesWithFirebase();
  Future<void> autoSyncInvoice(InvoiceModel invoice);
  
  // تصدير PDF
  Future<Uint8List> exportInvoiceToPDF(InvoiceModel invoice);
  Future<File> saveInvoicePDF(InvoiceModel invoice, String fileName);
  Future<void> printInvoice(InvoiceModel invoice);
}
```

### الواجهات المحسنة:
- قسم حالة الدفع في `InvoiceItemsSection`
- أزرار التصدير في `AddInvoiceScreen`
- حقول الدفع المحسنة في نموذج الفاتورة

## كيفية الاستخدام

### 1. إنشاء فاتورة جديدة:
1. إدخال بيانات العميل
2. إضافة المنتجات
3. تحديد حالة الدفع
4. إدخال المبلغ المدفوع (إن وجد)
5. إضافة ملاحظات الدفع
6. حفظ الفاتورة

### 2. تصدير الفاتورة:
1. الضغط على زر "تصدير PDF"
2. اختيار خيار التصدير المطلوب
3. تحديد مسار الحفظ (إن وجد)

### 3. المزامنة مع Firebase:
1. تحدث تلقائياً عند حفظ الفاتورة
2. يمكن المزامنة اليدوية من قائمة الفواتير
3. مراقبة حالة المزامنة من لوحة التحكم

## المتطلبات التقنية

### المكتبات المطلوبة:
```yaml
dependencies:
  pdf: ^3.11.1
  printing: ^5.13.2
  path_provider: ^2.1.4
```

### إعدادات Firebase:
- تكوين مشروع Firebase
- إعداد قواعد الأمان
- تكوين Authentication
- إعداد Firestore Database

## الأمان والخصوصية

### حماية البيانات:
- تشفير البيانات المحلية
- مصادقة المستخدمين
- صلاحيات محددة للوصول
- نسخ احتياطية منتظمة

### المزامنة الآمنة:
- تشفير البيانات المرسلة
- التحقق من صحة البيانات
- معالجة أخطاء الشبكة
- إعادة المحاولة التلقائية

## الدعم والصيانة

### المراقبة:
- سجلات المزامنة
- تقارير الأخطاء
- إحصائيات الاستخدام
- تنبيهات النظام

### التحديثات:
- تحديثات تلقائية للقواعد
- تحسينات الأداء
- إصلاحات الأمان
- ميزات جديدة

## الخلاصة

تم تطوير نظام شامل لإدارة الفواتير يتضمن:
- إدارة متقدمة لحالة الدفع
- مزامنة تلقائية مع Firebase
- تصدير PDF احترافي
- واجهة مستخدم محسنة
- أمان عالي للبيانات

هذا النظام يوفر حلول شاملة لإدارة الفواتير في بيئة الأعمال الحديثة مع التركيز على سهولة الاستخدام والأمان.
