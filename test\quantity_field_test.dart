import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:atlas_medical_supplies/features/products/screens/add_product_screen.dart';
import 'package:atlas_medical_supplies/models/product_model.dart';

void main() {
  group('AddProductScreen Quantity Field Tests', () {
    testWidgets('quantity field should be empty by default for new products', (WidgetTester tester) async {
      // Build the AddProductScreen without a product (new product mode)
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp(
              home: AddProductScreen(),
            );
          },
        ),
      );

      // Wait for the widget to be fully built
      await tester.pumpAndSettle();

      // Find the quantity text field
      final quantityField = find.byType(TextFormField).at(3); // Quantity field is typically the 4th field
      
      // Verify that the quantity field is empty
      final TextFormField quantityTextField = tester.widget(quantityField);
      expect(quantityTextField.controller?.text, isEmpty);
    });

    testWidgets('quantity field should show existing value when editing product', (WidgetTester tester) async {
      // Create a test product with quantity 5
      final testProduct = ProductModel(
        id: 'test_id',
        name: 'Test Product',
        description: 'Test Description',
        category: 'general_supplies',
        code: 'TEST001',
        price: 10.0,
        cost: 5.0,
        quantity: 5,
        minQuantity: 1,
        piecesPerCarton: 1,
        unit: 'قطعة',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Build the AddProductScreen with the test product (edit mode)
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp(
              home: AddProductScreen(product: testProduct),
            );
          },
        ),
      );

      // Wait for the widget to be fully built
      await tester.pumpAndSettle();

      // Find the quantity text field
      final quantityField = find.byType(TextFormField).at(3); // Quantity field is typically the 4th field
      
      // Verify that the quantity field shows the existing value
      final TextFormField quantityTextField = tester.widget(quantityField);
      expect(quantityTextField.controller?.text, '5');
    });
  });
}
