import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_colors.dart';
import '../../../models/user_model.dart';
import '../../../services/auth_service.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final AuthService _authService = AuthService();
  final CollectionService _collectionService = CollectionService();

  UserModel? _user;
  bool _isLoading = true;
  double _totalPendingCollections = 0.0; // إضافة متغير لمبالغ التحصيل المستحقة

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadPendingCollections();
  }

  Future<void> _loadUserData() async {
    try {
      final user = await _authService.getCurrentUserData();
      if (mounted) {
        setState(() {
          _user = user;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // إضافة دالة جديدة لحساب مبالغ التحصيل المستحقة
  Future<void> _loadPendingCollections() async {
    try {
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalAmount = 0.0;

      for (final invoice in pendingInvoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _totalPendingCollections = totalAmount;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مبالغ التحصيل: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الملف الشخصي',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 2,
        leading: CustomBackButton(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(Icons.edit, size: 20.sp),
            onPressed: _editProfile,
            tooltip: 'تعديل الملف الشخصي',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // صورة الملف الشخصي
                  _buildProfileImage(),
                  SizedBox(height: 24.h),

                  // بطاقة مبالغ التحصيل المستحقة
                  if (_totalPendingCollections > 0)
                    Container(
                      margin: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.shade50,
                            Colors.orange.shade100,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(color: Colors.orange.shade200),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.shade200.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(8.w),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade200,
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Icon(
                              Icons.payment,
                              color: Colors.orange.shade800,
                              size: 20.sp,
                            ),
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'مبالغ التحصيل المستحقة',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade800,
                                  ),
                                ),
                                Text(
                                  'تحتاج إلى متابعة',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 12.sp,
                                    color: Colors.orange.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 12.w,
                              vertical: 6.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade200,
                              borderRadius: BorderRadius.circular(16.r),
                              border: Border.all(color: Colors.orange.shade300),
                            ),
                            child: Text(
                              '${_totalPendingCollections.toStringAsFixed(2)} ر.س',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // معلومات المستخدم
                  _buildUserInfo(),
                  SizedBox(height: 24.h),

                  // إحصائيات النظام
                  _buildSystemStats(),
                  SizedBox(height: 24.h),

                  // إعدادات الحساب
                  _buildAccountSettings(),
                  SizedBox(height: 24.h),

                  // أزرار الإجراءات
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildProfileImage() {
    return Center(
      child: Stack(
        children: [
          Container(
            width: 120.w,
            height: 120.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border.all(color: AppColors.primary, width: 3.w),
            ),
            child: _user?.profileImage != null
                ? ClipOval(
                    child: Image.network(
                      _user!.profileImage!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          size: 60.sp,
                          color: AppColors.primary,
                        );
                      },
                    ),
                  )
                : Icon(Icons.person, size: 60.sp, color: AppColors.primary),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 36.w,
              height: 36.w,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2.w),
              ),
              child: Icon(Icons.camera_alt, size: 18.sp, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.person, color: AppColors.primary, size: 24.sp),
                SizedBox(width: 12.w),
                Text(
                  'معلومات المستخدم',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          _buildInfoTile('الاسم', _user?.name ?? 'غير محدد'),
          _buildInfoTile('البريد الإلكتروني', _user?.email ?? 'غير محدد'),
          _buildInfoTile('رقم الهاتف', _user?.phone ?? 'غير محدد'),
          _buildInfoTile('المنصب', _user?.role ?? 'MOHAMED FAYED'),
          _buildInfoTile('تاريخ الانضمام', '2024-01-01'),
        ],
      ),
    );
  }

  Widget _buildSystemStats() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.analytics, color: AppColors.info, size: 24.sp),
                SizedBox(width: 12.w),
                Text(
                  'إحصائيات النظام',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          _buildStatTile('إجمالي الفواتير', '1,234', Icons.receipt),
          _buildStatTile('إجمالي العملاء', '567', Icons.people),
          _buildStatTile('إجمالي المنتجات', '890', Icons.inventory),
          _buildStatTile('إجمالي المبيعات', '45,678 ر.س', Icons.trending_up),
        ],
      ),
    );
  }

  Widget _buildAccountSettings() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.settings, color: AppColors.warning, size: 24.sp),
                SizedBox(width: 12.w),
                Text(
                  'إعدادات الحساب',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          _buildSettingTile(
            'تغيير كلمة المرور',
            'تحديث كلمة المرور الحالية',
            Icons.lock,
            _changePassword,
          ),
          _buildSettingTile(
            'إعدادات الإشعارات',
            'تخصيص الإشعارات والتنبيهات',
            Icons.notifications,
            _notificationSettings,
          ),
          _buildSettingTile(
            'الأمان والخصوصية',
            'إعدادات الأمان والخصوصية',
            Icons.security,
            _securitySettings,
          ),
          _buildSettingTile(
            'النسخ الاحتياطي',
            'إدارة النسخ الاحتياطية',
            Icons.backup,
            _backupSettings,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر تحديث الملف الشخصي
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _editProfile,
            icon: Icon(Icons.edit, size: 20.sp),
            label: Text(
              'تحديث الملف الشخصي',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              elevation: 2,
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // زر تصدير البيانات
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _exportData,
            icon: Icon(Icons.download, size: 20.sp),
            label: Text(
              'تصدير البيانات',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.info,
              side: BorderSide(color: AppColors.info),
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // زر تسجيل الخروج
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _showLogoutDialog,
            icon: Icon(Icons.logout, size: 20.sp),
            label: Text(
              'تسجيل الخروج',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: BorderSide(color: AppColors.error),
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        value,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildStatTile(String title, String value, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: AppColors.info, size: 20.sp),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      trailing: Text(
        value,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.info,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildSettingTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.warning, size: 20.sp),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.textSecondary,
        size: 16.sp,
      ),
      onTap: onTap,
    );
  }

  void _editProfile() {
    // تنفيذ تعديل الملف الشخصي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة ميزة تعديل الملف الشخصي قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _changePassword() {
    // تنفيذ تغيير كلمة المرور
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة ميزة تغيير كلمة المرور قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _notificationSettings() {
    // تنفيذ إعدادات الإشعارات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة إعدادات الإشعارات قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _securitySettings() {
    // تنفيذ إعدادات الأمان
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة إعدادات الأمان قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _backupSettings() {
    // تنفيذ إعدادات النسخ الاحتياطي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة إعدادات النسخ الاحتياطي قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _exportData() {
    // تنفيذ تصدير البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'جاري تصدير البيانات...',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسجيل الخروج', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'هل أنت متأكد من تسجيل الخروج؟',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authService = Provider.of<AuthService>(
                  context,
                  listen: false,
                );
                await authService.signOut();
                if (context.mounted) {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'حدث خطأ أثناء تسجيل الخروج',
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: Text('تأكيد', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }
}
