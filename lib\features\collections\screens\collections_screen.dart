import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../models/invoice_model.dart';
import '../../../models/collection_model.dart';
import '../../../services/collection_service.dart';
import '../../../utils/screen_utils.dart';
import '../widgets/collection_dialog.dart';
import '../widgets/invoice_collection_card.dart';

class CollectionsScreen extends StatefulWidget {
  const CollectionsScreen({super.key});

  @override
  State<CollectionsScreen> createState() => _CollectionsScreenState();
}

class _CollectionsScreenState extends State<CollectionsScreen> {
  final CollectionService _collectionService = CollectionService();

  List<InvoiceModel> _pendingInvoices = [];
  bool _isLoading = true;
  String _searchQuery = '';
  double _totalPendingAmount = 0.0; // إضافة متغير لمجموع المبالغ المستحقة

  @override
  void initState() {
    super.initState();
    _loadPendingInvoices();
  }

  Future<void> _loadPendingInvoices() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final invoices = await _collectionService.getPendingInvoices();

      // حساب مجموع المبالغ المستحقة
      double totalAmount = 0.0;
      for (final invoice in invoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _pendingInvoices = invoices;
          _totalPendingAmount = totalAmount;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('خطأ في تحميل الفواتير المستحقة');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Cairo', color: Colors.white),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Cairo', color: Colors.white),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }

  Future<void> _showCollectionDialog(InvoiceModel invoice) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CollectionDialog(invoice: invoice),
    );

    if (result != null && mounted) {
      final collectionAmount = result['amount'] as double;
      final notes = result['notes'] as String?;

      try {
        final collection = await _collectionService.createCollection(
          invoiceId: invoice.id,
          collectionAmount: collectionAmount,
          notes: notes,
          createdBy: 'admin', // يمكن تغييرها حسب المستخدم الحالي
        );

        if (collection != null) {
          _showSuccessSnackBar(
            'تم تحصيل ${NumberFormat.currency(locale: 'ar_SA', symbol: 'ر.س ', decimalDigits: 2).format(collectionAmount)} بنجاح',
          );

          // إعادة تحميل الفواتير
          await _loadPendingInvoices();

          // عرض خيارات المشاركة
          _showSharingOptions(collection);
        } else {
          _showErrorSnackBar('فشل في عملية التحصيل');
        }
      } catch (e) {
        _showErrorSnackBar('خطأ في عملية التحصيل: ${e.toString()}');
      }
    }
  }

  void _showSharingOptions(CollectionModel collection) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'مشاركة إشعار التحصيل',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _SharingOption(
                  icon: Icons.chat,
                  label: 'واتساب',
                  color: const Color(0xFF25D366),
                  onTap: () => _shareViaWhatsApp(collection),
                ),
                _SharingOption(
                  icon: Icons.sms,
                  label: 'رسالة نصية',
                  color: const Color(0xFF2196F3),
                  onTap: () => _shareViaSMS(collection),
                ),
              ],
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  void _shareViaWhatsApp(CollectionModel collection) {
    // هنا يمكن إضافة منطق مشاركة الواتساب
    final message = Uri.encodeComponent(collection.notificationMessage);
    final url = 'https://wa.me/${collection.customerPhone}?text=$message';

    // يمكن استخدام url_launcher هنا
    print('مشاركة عبر الواتساب: $url');
    Navigator.pop(context);
    _showSuccessSnackBar('تم فتح الواتساب');
  }

  void _shareViaSMS(CollectionModel collection) {
    // هنا يمكن إضافة منطق إرسال الرسالة النصية
    print('إرسال رسالة نصية: ${collection.notificationMessage}');
    Navigator.pop(context);
    _showSuccessSnackBar('تم إرسال الرسالة النصية');
  }

  List<InvoiceModel> get _filteredInvoices {
    if (_searchQuery.isEmpty) {
      return _pendingInvoices;
    }

    return _pendingInvoices.where((invoice) {
      return invoice.customerName.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          invoice.invoiceNumber.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          children: [
            Text(
              'إدارة التحصيل',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (!_isLoading && _totalPendingAmount > 0)
              Text(
                'إجمالي المستحق: ${NumberFormat.currency(locale: 'ar_SA', symbol: 'ر.س ', decimalDigits: 0).format(_totalPendingAmount)}',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange.shade700,
                ),
              ),
          ],
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black87),
        titleTextStyle: const TextStyle(color: Colors.black87),
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: EdgeInsets.all(16.w),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في الفواتير المستحقة للتحصيل...',
                hintStyle: TextStyle(
                  fontFamily: 'Cairo',
                  color: Colors.grey.shade600,
                ),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: Colors.blue.shade300),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              style: TextStyle(fontFamily: 'Cairo', fontSize: 14.sp),
            ),
          ),

          // إحصائيات شاملة
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade50, Colors.blue.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              children: [
                // العنوان
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: Colors.blue.shade700,
                      size: 24.sp,
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'إحصائيات التحصيل',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),

                // الصف الأول من الإحصائيات
                Row(
                  children: [
                    Expanded(
                      child: _StatCard(
                        title: 'الفواتير المستحقة',
                        value: '${_filteredInvoices.length}',
                        subtitle: 'فاتورة',
                        color: Colors.orange,
                        icon: Icons.receipt_long,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _StatCard(
                        title: 'إجمالي المستحق',
                        value:
                            NumberFormat.currency(
                              locale: 'ar_SA',
                              symbol: 'ر.س ',
                              decimalDigits: 0,
                            ).format(
                              _filteredInvoices.fold(
                                0.0,
                                (sum, invoice) =>
                                    sum + invoice.calculateRemainingAmount(),
                              ),
                            ),
                        subtitle: 'مبلغ التحصيل',
                        color: Colors.red,
                        icon: Icons.payment,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 12.h),

                // الصف الثاني من الإحصائيات
                Row(
                  children: [
                    Expanded(
                      child: _StatCard(
                        title: 'متوسط الفاتورة',
                        value: _filteredInvoices.isEmpty
                            ? '0 ر.س'
                            : NumberFormat.currency(
                                locale: 'ar_SA',
                                symbol: 'ر.س ',
                                decimalDigits: 0,
                              ).format(
                                _filteredInvoices.fold(
                                      0.0,
                                      (sum, invoice) =>
                                          sum +
                                          invoice.calculateRemainingAmount(),
                                    ) /
                                    _filteredInvoices.length,
                              ),
                        subtitle: 'مبلغ متوسط',
                        color: Colors.green,
                        icon: Icons.calculate,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _StatCard(
                        title: 'أكبر فاتورة',
                        value: _filteredInvoices.isEmpty
                            ? '0 ر.س'
                            : NumberFormat.currency(
                                locale: 'ar_SA',
                                symbol: 'ر.س ',
                                decimalDigits: 0,
                              ).format(
                                _filteredInvoices
                                    .map(
                                      (invoice) =>
                                          invoice.calculateRemainingAmount(),
                                    )
                                    .reduce((a, b) => a > b ? a : b),
                              ),
                        subtitle: 'مبلغ مستحق',
                        color: Colors.purple,
                        icon: Icons.trending_up,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // قائمة الفواتير
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredInvoices.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    itemCount: _filteredInvoices.length,
                    itemBuilder: (context, index) {
                      final invoice = _filteredInvoices[index];
                      return Padding(
                        padding: EdgeInsets.only(bottom: 12.h),
                        child: InvoiceCollectionCard(
                          invoice: invoice,
                          onCollect: () => _showCollectionDialog(invoice),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80.sp,
            color: Colors.grey.shade400,
          ),
          SizedBox(height: 16.h),
          Text(
            _searchQuery.isEmpty
                ? 'لا توجد فواتير مستحقة للتحصيل'
                : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16.sp,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              'جميع الفواتير مدفوعة بالكامل أو تم تحصيلها',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14.sp,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final Color color;
  final IconData icon;

  const _StatCard({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16.sp, color: color),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 10.sp,
              color: color.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }
}

class _SharingOption extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _SharingOption({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32.sp, color: color),
            SizedBox(height: 8.h),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
