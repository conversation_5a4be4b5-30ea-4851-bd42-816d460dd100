enum CustomerType { distributor, medicalOfficeA, medicalOfficeB, majorClient }

class CustomerModel {
  final String id;
  final String name;
  final String? email;
  final String? address;
  final String? city;
  final String? governorate;
  final String? postalCode;
  final String? taxNumber;
  final String? notes;
  final CustomerType type;
  final String activity;
  final String? phone1;
  final String? phone2;
  final String? street;
  final String? building;
  final String? floor;
  final String? apartment;
  final String? landmark;
  final String? village;
  final double? latitude;
  final double? longitude;
  final double balance;
  final DateTime? lastOrderDate;
  final String? createdBy;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CustomerModel({
    required this.id,
    required this.name,
    this.email,
    this.address,
    this.city,
    this.governorate,
    this.postalCode,
    this.taxNumber,
    this.notes,
    this.type = CustomerType.medicalOfficeA,
    this.activity = '',
    this.phone1,
    this.phone2,
    this.street,
    this.building,
    this.floor,
    this.apartment,
    this.landmark,
    this.village,
    this.latitude,
    this.longitude,
    this.balance = 0.0,
    this.lastOrderDate,
    this.createdBy,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    return CustomerModel(
      id: json['id'] ?? '',
      name: (json['name'] ?? '').toString().trim(),
      email: json['email']?.toString().trim().isEmpty == true
          ? null
          : json['email']?.toString().trim(),
      address: json['address']?.toString().trim().isEmpty == true
          ? null
          : json['address']?.toString().trim(),
      city: (json['city'] ?? '').toString().trim().isEmpty
          ? ''
          : (json['city'] ?? '').toString().trim(),
      governorate: (json['governorate'] ?? '').toString().trim().isEmpty
          ? ''
          : (json['governorate'] ?? '').toString().trim(),
      postalCode: json['postalCode']?.toString().trim().isEmpty == true
          ? null
          : json['postalCode']?.toString().trim(),
      taxNumber: json['taxNumber']?.toString().trim().isEmpty == true
          ? null
          : json['taxNumber']?.toString().trim(),
      notes: json['notes']?.toString().trim().isEmpty == true
          ? null
          : json['notes']?.toString().trim(),
      type: _parseCustomerType(json['type']),
      activity: (json['activity'] ?? '').toString().trim().isEmpty
          ? ''
          : (json['activity'] ?? '').toString().trim(),
      phone1: json['phone1']?.toString().trim().isEmpty == true
          ? null
          : json['phone1']?.toString().trim(),
      phone2: json['phone2']?.toString().trim().isEmpty == true
          ? null
          : json['phone2']?.toString().trim(),
      street: json['street']?.toString().trim().isEmpty == true
          ? null
          : json['street']?.toString().trim(),
      building: json['building']?.toString().trim().isEmpty == true
          ? null
          : json['building']?.toString().trim(),
      floor: json['floor']?.toString().trim().isEmpty == true
          ? null
          : json['floor']?.toString().trim(),
      apartment: json['apartment']?.toString().trim().isEmpty == true
          ? null
          : json['apartment']?.toString().trim(),
      landmark: json['landmark']?.toString().trim().isEmpty == true
          ? null
          : json['landmark']?.toString().trim(),
      village: json['village']?.toString().trim().isEmpty == true
          ? null
          : json['village']?.toString().trim(),
      latitude: json['latitude'] != null
          ? double.tryParse(json['latitude'].toString()) ?? 0.0
          : null,
      longitude: json['longitude'] != null
          ? double.tryParse(json['longitude'].toString()) ?? 0.0
          : null,
      balance: (json['balance'] ?? 0.0).toDouble(),
      lastOrderDate: json['lastOrderDate'] != null
          ? DateTime.tryParse(json['lastOrderDate'].toString())
          : null,
      createdBy: json['createdBy']?.toString().trim().isEmpty == true
          ? null
          : json['createdBy']?.toString().trim(),
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  factory CustomerModel.fromMap(Map<String, dynamic> map) {
    return CustomerModel(
      id: map['id'] ?? '',
      name: (map['name'] ?? '').toString().trim(),
      email: map['email']?.toString().trim().isEmpty == true
          ? null
          : map['email']?.toString().trim(),
      address: map['address']?.toString().trim().isEmpty == true
          ? null
          : map['address']?.toString().trim(),
      city: (map['city'] ?? '').toString().trim().isEmpty
          ? ''
          : (map['city'] ?? '').toString().trim(),
      governorate: (map['governorate'] ?? '').toString().trim().isEmpty
          ? ''
          : (map['governorate'] ?? '').toString().trim(),
      postalCode: map['postalCode']?.toString().trim().isEmpty == true
          ? null
          : map['postalCode']?.toString().trim(),
      taxNumber: map['taxNumber']?.toString().trim().isEmpty == true
          ? null
          : map['taxNumber']?.toString().trim(),
      notes: map['notes']?.toString().trim().isEmpty == true
          ? null
          : map['notes']?.toString().trim(),
      type: _parseCustomerType(map['type']),
      activity: (map['activity'] ?? '').toString().trim().isEmpty
          ? ''
          : (map['activity'] ?? '').toString().trim(),
      phone1: map['phone1']?.toString().trim().isEmpty == true
          ? null
          : map['phone1']?.toString().trim(),
      phone2: map['phone2']?.toString().trim().isEmpty == true
          ? null
          : map['phone2']?.toString().trim(),
      street: map['street']?.toString().trim().isEmpty == true
          ? null
          : map['street']?.toString().trim(),
      building: map['building']?.toString().trim().isEmpty == true
          ? null
          : map['building']?.toString().trim(),
      floor: map['floor']?.toString().trim().isEmpty == true
          ? null
          : map['floor']?.toString().trim(),
      apartment: map['apartment']?.toString().trim().isEmpty == true
          ? null
          : map['apartment']?.toString().trim(),
      landmark: map['landmark']?.toString().trim().isEmpty == true
          ? null
          : map['landmark']?.toString().trim(),
      village: map['village']?.toString().trim().isEmpty == true
          ? null
          : map['village']?.toString().trim(),
      latitude: map['latitude'] != null
          ? double.tryParse(map['latitude'].toString()) ?? 0.0
          : null,
      longitude: map['longitude'] != null
          ? double.tryParse(map['longitude'].toString()) ?? 0.0
          : null,
      balance: (map['balance'] ?? 0.0).toDouble(),
      lastOrderDate: map['lastOrderDate'] != null
          ? DateTime.tryParse(map['lastOrderDate'].toString())
          : null,
      createdBy: map['createdBy']?.toString().trim().isEmpty == true
          ? null
          : map['createdBy']?.toString().trim(),
      isActive: map['isActive'] == 1 || map['isActive'] == null,
      createdAt: map['createdAt'] != null
          ? DateTime.tryParse(map['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? DateTime.tryParse(map['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  static CustomerType _parseCustomerType(dynamic value) {
    if (value == null) return CustomerType.medicalOfficeA;

    final String typeString = value.toString().trim().toLowerCase();

    switch (typeString) {
      case 'distributor':
      case 'dist':
        return CustomerType.distributor;
      case 'medicalofficea':
      case 'medical_office_a':
      case 'medical office a':
      case 'officea':
        return CustomerType.medicalOfficeA;
      case 'medicalofficeb':
      case 'medical_office_b':
      case 'medical office b':
      case 'officeb':
        return CustomerType.medicalOfficeB;
      case 'majorclient':
      case 'major_client':
      case 'major client':
      case 'client':
        return CustomerType.majorClient;
      default:
        print('تحذير: نوع عميل غير معروف: "$value"، استخدام النوع الافتراضي');
        return CustomerType.medicalOfficeA;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name.trim(),
      'email': email?.trim().isEmpty == true ? null : email?.trim(),
      'address': address?.trim().isEmpty == true ? null : address?.trim(),
      'city': (city ?? '').trim().isEmpty ? '' : (city ?? '').trim(),
      'governorate': (governorate ?? '').trim().isEmpty
          ? ''
          : (governorate ?? '').trim(),
      'postalCode': postalCode?.trim().isEmpty == true
          ? null
          : postalCode?.trim(),
      'taxNumber': taxNumber?.trim().isEmpty == true ? null : taxNumber?.trim(),
      'notes': notes?.trim().isEmpty == true ? null : notes?.trim(),
      'type': type.toString().split('.').last,
      'activity': (activity ?? '').trim().isEmpty
          ? ''
          : (activity ?? '').trim(),
      'phone1': phone1?.trim().isEmpty == true ? null : phone1?.trim(),
      'phone2': phone2?.trim().isEmpty == true ? null : phone2?.trim(),
      'street': street?.trim().isEmpty == true ? null : street?.trim(),
      'building': building?.trim().isEmpty == true ? null : building?.trim(),
      'floor': floor?.trim().isEmpty == true ? null : floor?.trim(),
      'apartment': apartment?.trim().isEmpty == true ? null : apartment?.trim(),
      'landmark': landmark?.trim().isEmpty == true ? null : landmark?.trim(),
      'latitude': latitude,
      'longitude': longitude,
      'balance': balance,
      'lastOrderDate': lastOrderDate?.toIso8601String(),
      'createdBy': createdBy?.trim().isEmpty == true ? null : createdBy?.trim(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name.trim(),
      'email': email?.trim().isEmpty == true ? null : email?.trim(),
      'address': address?.trim().isEmpty == true ? null : address?.trim(),
      'city': (city ?? '').trim().isEmpty ? '' : (city ?? '').trim(),
      'governorate': (governorate ?? '').trim().isEmpty
          ? ''
          : (governorate ?? '').trim(),
      'postalCode': postalCode?.trim().isEmpty == true
          ? null
          : postalCode?.trim(),
      'taxNumber': taxNumber?.trim().isEmpty == true ? null : taxNumber?.trim(),
      'notes': notes?.trim().isEmpty == true ? null : notes?.trim(),
      'type': type.toString().split('.').last,
      'activity': (activity ?? '').trim().isEmpty
          ? ''
          : (activity ?? '').trim(),
      'phone1': phone1?.trim().isEmpty == true ? null : phone1?.trim(),
      'phone2': phone2?.trim().isEmpty == true ? null : phone2?.trim(),
      'street': street?.trim().isEmpty == true ? null : street?.trim(),
      'building': building?.trim().isEmpty == true ? null : building?.trim(),
      'floor': floor?.trim().isEmpty == true ? null : floor?.trim(),
      'apartment': apartment?.trim().isEmpty == true ? null : apartment?.trim(),
      'landmark': landmark?.trim().isEmpty == true ? null : landmark?.trim(),
      'latitude': latitude,
      'longitude': longitude,
      'balance': balance,
      'lastOrderDate': lastOrderDate?.toIso8601String(),
      'createdBy': createdBy,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  CustomerModel copyWith({
    String? id,
    String? name,
    String? email,
    String? address,
    String? city,
    String? governorate,
    String? postalCode,
    String? taxNumber,
    String? notes,
    CustomerType? type,
    String? activity,
    String? phone1,
    String? phone2,
    String? street,
    String? building,
    String? floor,
    String? apartment,
    String? landmark,
    double? latitude,
    double? longitude,
    double? balance,
    DateTime? lastOrderDate,
    String? createdBy,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomerModel(
      id: id ?? this.id,
      name: (name ?? this.name).trim(),
      email: (email ?? this.email)?.trim().isEmpty == true
          ? null
          : (email ?? this.email)?.trim(),
      address: (address ?? this.address)?.trim().isEmpty == true
          ? null
          : (address ?? this.address)?.trim(),
      city: (city ?? this.city ?? '').trim().isEmpty
          ? ''
          : (city ?? this.city ?? '').trim(),
      governorate: (governorate ?? this.governorate ?? '').trim().isEmpty
          ? ''
          : (governorate ?? this.governorate ?? '').trim(),
      postalCode: (postalCode ?? this.postalCode)?.trim().isEmpty == true
          ? null
          : (postalCode ?? this.postalCode)?.trim(),
      taxNumber: (taxNumber ?? this.taxNumber)?.trim().isEmpty == true
          ? null
          : (taxNumber ?? this.taxNumber)?.trim(),
      notes: (notes ?? this.notes)?.trim().isEmpty == true
          ? null
          : (notes ?? this.notes)?.trim(),
      type: type ?? this.type,
      activity: (activity ?? this.activity ?? '').trim().isEmpty
          ? ''
          : (activity ?? this.activity ?? '').trim(),
      phone1: (phone1 ?? this.phone1)?.trim().isEmpty == true
          ? null
          : (phone1 ?? this.phone1)?.trim(),
      phone2: (phone2 ?? this.phone2)?.trim().isEmpty == true
          ? null
          : (phone2 ?? this.phone2)?.trim(),
      street: (street ?? this.street)?.trim().isEmpty == true
          ? null
          : (street ?? this.street)?.trim(),
      building: (building ?? this.building)?.trim().isEmpty == true
          ? null
          : (building ?? this.building)?.trim(),
      floor: (floor ?? this.floor)?.trim().isEmpty == true
          ? null
          : (floor ?? this.floor)?.trim(),
      apartment: (apartment ?? this.apartment)?.trim().isEmpty == true
          ? null
          : (apartment ?? this.apartment)?.trim(),
      landmark: (landmark ?? this.landmark)?.trim().isEmpty == true
          ? null
          : (landmark ?? this.landmark)?.trim(),
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      balance: balance ?? this.balance,
      lastOrderDate: lastOrderDate ?? this.lastOrderDate,
      createdBy: (createdBy ?? this.createdBy)?.trim().isEmpty == true
          ? null
          : (createdBy ?? this.createdBy)?.trim(),
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get fullAddress {
    List<String> addressParts = [];
    if (governorate != null && governorate!.isNotEmpty) {
      addressParts.add(governorate!);
    }
    if (city != null && city!.isNotEmpty) {
      addressParts.add(city!);
    }
    if (street != null && street!.isNotEmpty) {
      addressParts.add(street!);
    }
    return addressParts.join(', ');
  }

  String get displayName {
    if (phone1 != null && phone1!.isNotEmpty) {
      return '$name ($phone1)';
    }
    return name;
  }

  bool get hasContactInfo =>
      (phone1 != null && phone1!.isNotEmpty) ||
      (email != null && email!.isNotEmpty);

  bool get hasAddress => address != null && address!.isNotEmpty;

  bool get hasLocation => latitude != null && longitude != null;

  String get typeDisplayName {
    switch (type) {
      case CustomerType.distributor:
        return 'موزع';
      case CustomerType.medicalOfficeA:
        return 'مكتب طبي أ';
      case CustomerType.medicalOfficeB:
        return 'مكتب طبي ب';
      case CustomerType.majorClient:
        return 'عميل كبير';
    }
  }

  @override
  String toString() {
    return 'CustomerModel(\n'
        '  id: "$id",\n'
        '  name: "${name.trim()}",\n'
        '  type: ${type.toString().split('.').last},\n'
        '  phone1: "${phone1?.trim() ?? 'غير محدد'}",\n'
        '  phone2: "${phone2?.trim() ?? 'غير محدد'}",\n'
        '  governorate: "${governorate?.trim() ?? 'غير محدد'}",\n'
        '  city: "${city?.trim() ?? 'غير محدد'}",\n'
        '  address: "${address?.trim() ?? 'غير محدد'}",\n'
        '  street: "${street?.trim() ?? 'غير محدد'}",\n'
        '  building: "${building?.trim() ?? 'غير محدد'}",\n'
        '  floor: "${floor?.trim() ?? 'غير محدد'}",\n'
        '  apartment: "${apartment?.trim() ?? 'غير محدد'}",\n'
        '  landmark: "${landmark?.trim() ?? 'غير محدد'}",\n'
        '  activity: "${activity?.trim() ?? 'غير محدد'}",\n'
        '  balance: $balance,\n'
        '  isActive: $isActive,\n'
        '  createdAt: ${createdAt.toIso8601String()},\n'
        '  updatedAt: ${updatedAt.toIso8601String()},\n'
        ')';
  }
}
