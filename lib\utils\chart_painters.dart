import 'package:flutter/material.dart';

/// رسام مخطط الإيرادات
class RevenueChartPainter extends CustomPainter {
  final List<double> data;
  final Color color;

  RevenueChartPainter(this.data, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = color.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final points = <Offset>[];

    final maxValue = data.reduce((a, b) => a > b ? a : b);
    final minValue = data.reduce((a, b) => a < b ? a : b);
    final range = maxValue - minValue;

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y = range > 0 
          ? size.height - ((data[i] - minValue) / range) * size.height
          : size.height / 2;
      
      points.add(Offset(x, y));
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    // رسم الخط
    canvas.drawPath(path, paint);

    // رسم المنطقة المملوءة
    final fillPath = Path.from(path);
    fillPath.lineTo(size.width, size.height);
    fillPath.lineTo(0, size.height);
    fillPath.close();
    canvas.drawPath(fillPath, fillPaint);

    // رسم النقاط
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 3, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مخطط الأرباح
class ProfitChartPainter extends CustomPainter {
  final List<double> data;
  final Color color;

  ProfitChartPainter(this.data, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final barPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    final maxValue = data.reduce((a, b) => a > b ? a : b);
    final barWidth = size.width / data.length;
    final barSpacing = barWidth * 0.2;

    for (int i = 0; i < data.length; i++) {
      final barHeight = maxValue > 0 ? (data[i] / maxValue) * size.height : 0.0;
      final x = i * barWidth + barSpacing / 2;
      final y = size.height - barHeight;

      final rect = Rect.fromLTWH(x, y, barWidth - barSpacing, barHeight);
      canvas.drawRect(rect, barPaint);
      canvas.drawRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مخطط التوقعات
class ForecastChartPainter extends CustomPainter {
  final Map<String, List<double>> forecastData;

  ForecastChartPainter(this.forecastData);

  @override
  void paint(Canvas canvas, Size size) {
    if (forecastData.isEmpty) return;

    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
    ];

    int colorIndex = 0;
    for (final entry in forecastData.entries) {
      final data = entry.value;
      if (data.isEmpty) continue;

      final color = colors[colorIndex % colors.length];
      final paint = Paint()
        ..color = color
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;

      final fillPaint = Paint()
        ..color = color.withValues(alpha: 0.1)
        ..style = PaintingStyle.fill;

      final path = Path();
      final maxValue = data.reduce((a, b) => a > b ? a : b);
      final minValue = data.reduce((a, b) => a < b ? a : b);
      final range = maxValue - minValue;

      for (int i = 0; i < data.length; i++) {
        final x = (i / (data.length - 1)) * size.width;
        final y = range > 0 
            ? size.height - ((data[i] - minValue) / range) * size.height
            : size.height / 2;
        
        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      // رسم الخط
      canvas.drawPath(path, paint);

      // رسم المنطقة المملوءة
      final fillPath = Path.from(path);
      fillPath.lineTo(size.width, size.height);
      fillPath.lineTo(0, size.height);
      fillPath.close();
      canvas.drawPath(fillPath, fillPaint);

      colorIndex++;
    }

    // رسم شبكة الخلفية
    final gridPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.2)
      ..strokeWidth = 0.5;

    // خطوط أفقية
    for (int i = 0; i <= 4; i++) {
      final y = (i / 4) * size.height;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // خطوط رأسية
    for (int i = 0; i <= 2; i++) {
      final x = (i / 2) * size.width;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
