import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final CollectionService _collectionService = CollectionService();
  double _totalPendingCollections = 0.0;
  double _totalCollectionsAmount = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCollectionsData();
  }

  Future<void> _loadCollectionsData() async {
    try {
      // حساب مبالغ التحصيل المستحقة
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalPendingCollections = 0.0;
      for (final invoice in pendingInvoices) {
        totalPendingCollections += invoice.calculateRemainingAmount();
      }

      // الحصول على إجمالي التحصيلات المنجزة
      final totalCollections = await _collectionService.getTotalCollections();

      if (mounted) {
        setState(() {
          _totalPendingCollections = totalPendingCollections;
          _totalCollectionsAmount = totalCollections;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 1,
        title: const Text(
          'الإحصائيات العامة',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _loadCollectionsData,
            icon: Icon(Icons.refresh, color: AppColors.primary, size: 24.sp),
            tooltip: 'تحديث',
          ),
        ],
      ),
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await _loadCollectionsData();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نظرة عامة على الأعمال',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 16.h),

                // بطاقة مبالغ التحصيل
                if (!_isLoading) ...[
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.orange.shade50, Colors.orange.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.orange.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.shade200.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.payment,
                              color: Colors.orange.shade700,
                              size: 24.sp,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'إحصائيات التحصيل',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        Row(
                          children: [
                            Expanded(
                              child: _buildCollectionStatCard(
                                'المبالغ المستحقة',
                                _totalPendingCollections.toStringAsFixed(2),
                                Icons.pending_actions,
                                Colors.orange.shade700,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: _buildCollectionStatCard(
                                'التحصيلات المنجزة',
                                _totalCollectionsAmount.toStringAsFixed(2),
                                Icons.check_circle,
                                Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                ],

                const StatisticsCards(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCollectionStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(height: 8.h),
          Text(
            '$value ر.س',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.grey.shade600,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class StatisticsCards extends StatelessWidget {
  const StatisticsCards({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final bool isNarrow = width < 380;
    final int columns = isNarrow ? 2 : 3;
    final double aspect = isNarrow ? 1.2 : 1.0;

    final items = <_StatCard>[
      const _StatCard(
        title: 'إجمالي المبيعات',
        value: '0',
        unit: 'ر.س',
        icon: Icons.trending_up,
        color: AppColors.success,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'الفواتير المعلقة',
        value: '0',
        unit: 'ر.س',
        icon: Icons.pending_actions,
        color: AppColors.warning,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'العملاء النشطين',
        value: '0',
        unit: 'عميل',
        icon: Icons.people,
        color: AppColors.customers,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'المنتجات',
        value: '0',
        unit: 'منتج',
        icon: Icons.inventory_2,
        color: AppColors.products,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'المرتجعات',
        value: '0',
        unit: 'مرتجع',
        icon: Icons.keyboard_return,
        color: AppColors.returns,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'نقص المخزون',
        value: '0',
        unit: 'منتج',
        icon: Icons.warning,
        color: AppColors.error,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'إجمالي الأرباح',
        value: '0',
        unit: 'ر.س',
        icon: Icons.attach_money,
        color: AppColors.primary,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'متوسط الطلبية',
        value: '0',
        unit: 'ر.س',
        icon: Icons.shopping_cart,
        color: AppColors.secondary,
        percentage: '0%',
        isPositive: true,
      ),
      const _StatCard(
        title: 'الطلبات الجديدة',
        value: '0',
        unit: 'طلب',
        icon: Icons.new_releases,
        color: AppColors.info,
        percentage: '0%',
        isPositive: true,
      ),
    ];

    return GridView.count(
      crossAxisCount: columns,
      mainAxisSpacing: 12.h,
      crossAxisSpacing: 12.w,
      childAspectRatio: aspect,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      children: items,
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String unit;
  final IconData icon;
  final Color color;
  final String percentage;
  final bool isPositive;

  const _StatCard({
    required this.title,
    required this.value,
    required this.unit,
    required this.icon,
    required this.color,
    required this.percentage,
    required this.isPositive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.remove,
                      color: AppColors.textSecondary,
                      size: 12.sp,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      percentage,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 4.w),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
