import 'package:flutter/material.dart';
import '../../../models/invoice_model.dart';
import '../../../constants/app_colors.dart';

class InvoiceItemsTable extends StatelessWidget {
  final List<InvoiceItem> items;

  const InvoiceItemsTable({Key? key, required this.items}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columnSpacing: 20,
        headingTextStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
          fontSize: 14,
        ),
        dataTextStyle: const TextStyle(fontSize: 14, color: Colors.black87),
        columns: const [
          DataColumn(label: Text('#')),
          DataColumn(label: Text('المنتج')),
          DataColumn(label: Text('الكود')),
          DataColumn(label: Text('الكمية')),
          DataColumn(label: Text('الوحدة')),
          DataColumn(label: Text('السعر')),
          DataColumn(label: Text('الإجمالي')),
        ],
        rows: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          return DataRow(
            cells: [
              DataCell(Text('${index + 1}')),
              DataCell(
                Text(
                  item.productName,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              DataCell(Text(item.productCode)),
              DataCell(Text(item.quantity.toString())),
              DataCell(Text(item.unit)),
              DataCell(Text('${item.unitPrice.toStringAsFixed(2)} ر.س')),
              DataCell(
                Text(
                  '${item.total.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }
}
