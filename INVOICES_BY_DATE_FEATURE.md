# ميزة الفواتير حسب التاريخ

## نظرة عامة
تم إضافة ميزة جديدة لعرض الفواتير حسب الفترات الزمنية المختلفة، حيث يمكن للمستخدمين تصفية الفواتير حسب:
- اليوم الحالي
- الأسبوع الحالي
- الأسبوعين الماضيين
- الشهر الحالي

## الموقع
تم وضع هذه الميزة في مكان المرتجعات، حيث يمكن الوصول إليها من:
1. **القائمة الجانبية**: في قسم "الفواتير" تحت "إدارة الفواتير"
2. **شاشة العملاء**: زر إضافي بجانب زر تبديل العرض (عادي/حسب المحافظات)
3. **تفاصيل العميل**: زر "عرض الفواتير" في صفحة تفاصيل العميل

## الميزات

### 1. أزرار اختيار الفترة
- **اليوم**: يعرض فواتير اليوم الحالي فقط
- **الأسبوع**: يعرض فواتير الأسبوع الحالي (من بداية الأسبوع)
- **الأسبوعين**: يعرض فواتير آخر 14 يوم
- **الشهر**: يعرض فواتير الشهر الحالي

### 2. إحصائيات سريعة
- إجمالي عدد الفواتير في الفترة المحددة
- إجمالي المبالغ للفواتير المعروضة

### 3. عرض الفواتير
- قائمة منظمة بالفواتير مع جميع التفاصيل
- استخدام `InvoiceCard` المعياري للعرض
- رسائل مناسبة عند عدم وجود فواتير

## التنفيذ التقني

### الملفات المضافة/المحدثة

#### 1. `lib/features/invoices/screens/invoices_by_date_screen.dart`
- شاشة جديدة لعرض الفواتير حسب التاريخ
- تصميم متجاوب مع واجهة مستخدم حديثة
- دعم للغة العربية

#### 2. `lib/services/invoice_service.dart`
- إضافة دالة `getInvoicesByDatePeriod(String period)`
- حساب الفترات الزمنية المختلفة
- استخدام الدالة الموجودة `getInvoicesByDateRange`

#### 3. `lib/widgets/app_drawer.dart`
- إضافة عنصر "الفواتير حسب التاريخ" في القائمة الجانبية
- أيقونة تقويم مناسبة

#### 4. `lib/features/customers/screens/customers_screen.dart`
- إضافة زر للفواتير حسب التاريخ بجانب زر تبديل العرض
- تنقل مباشر إلى الشاشة الجديدة

#### 5. `lib/features/customers/screens/customer_details_screen.dart`
- تحديث زر "عرض الفواتير" لاستخدام الشاشة الجديدة

### الخوارزميات المستخدمة

#### حساب الفترات الزمنية
```dart
switch (period) {
  case 'today':
    startDate = DateTime(now.year, now.month, now.day);
    endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    break;
  case 'week':
    startDate = now.subtract(Duration(days: now.weekday - 1));
    startDate = DateTime(startDate.year, startDate.month, startDate.day);
    endDate = now;
    break;
  case 'twoWeeks':
    startDate = now.subtract(const Duration(days: 14));
    startDate = DateTime(startDate.year, startDate.month, startDate.day);
    endDate = now;
    break;
  case 'month':
    startDate = DateTime(now.year, now.month, 1);
    endDate = now;
    break;
}
```

## الاستخدام

### للمستخدمين
1. افتح القائمة الجانبية
2. اختر "الفواتير حسب التاريخ"
3. اختر الفترة المطلوبة (اليوم، الأسبوع، الأسبوعين، الشهر)
4. استعرض الفواتير والإحصائيات

### للمطورين
```dart
// إنشاء خدمة الفواتير
final invoiceService = InvoiceService();

// الحصول على فواتير اليوم
final todayInvoices = await invoiceService.getInvoicesByDatePeriod('today');

// الحصول على فواتير الأسبوع
final weekInvoices = await invoiceService.getInvoicesByDatePeriod('week');

// الحصول على فواتير الأسبوعين
final twoWeeksInvoices = await invoiceService.getInvoicesByDatePeriod('twoWeeks');

// الحصول على فواتير الشهر
final monthInvoices = await invoiceService.getInvoicesByDatePeriod('month');
```

## المزايا

1. **سهولة الوصول**: متاحة من عدة أماكن في التطبيق
2. **تصفية سريعة**: أزرار واضحة لاختيار الفترة
3. **إحصائيات فورية**: عرض سريع للأرقام المهمة
4. **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
5. **دعم اللغة العربية**: واجهة مستخدم باللغة العربية

## التطويرات المستقبلية

1. **فترات مخصصة**: إمكانية اختيار تاريخ بداية ونهاية مخصص
2. **تصدير التقارير**: تصدير الفواتير حسب التاريخ إلى PDF أو Excel
3. **مقارنة الفترات**: مقارنة إحصائيات فترات مختلفة
4. **تنبيهات**: تنبيهات للفواتير المتأخرة أو المعلقة

## ملاحظات تقنية

- تستخدم الميزة `InvoiceCard` الموجودة للعرض
- تعتمد على `DatabaseService` للوصول للبيانات
- تدعم `Riverpod` لإدارة الحالة
- تستخدم `ScreenUtil` للتجاوب مع أحجام الشاشات المختلفة
