# إصلاح مشكلة قاعدة البيانات - جدول الفواتير

## المشكلة
يحدث خطأ عند محاولة حفظ الفاتورة:
```
DatabaseException(table invoices has no column named customerPhone (code 1 SQLITE_ERROR))
```

## سبب المشكلة
قاعدة البيانات الحالية لا تحتوي على عمود `customerPhone` في جدول `invoices`، رغم أن النموذج يحاول استخدامه.

## الحلول المطبقة

### 1. فحص وإصلاح قاعدة البيانات تلقائياً
- تم إضافة دالة `checkAndFixDatabase()` في `DatabaseService`
- يتم فحص قاعدة البيانات عند بدء التطبيق
- يتم التأكد من وجود جميع الأعمدة المطلوبة

### 2. إجبار ترقية قاعدة البيانات
- تم إضافة دالة `forceDatabaseUpgrade()` 
- تقوم بترقية قاعدة البيانات إلى الإصدار 14
- تضيف عمود `customerPhone` تلقائياً

### 3. إعادة إنشاء جدول الفواتير
- في حالة فشل الترقية، يتم إعادة إنشاء الجدول
- يتم الحفاظ على البيانات الموجودة إن أمكن

### 4. معالجة الأخطاء في واجهة المستخدم
- رسائل خطأ واضحة للمستخدم
- زر "إصلاح قاعدة البيانات" لإصلاح المشكلة يدوياً
- زر "إعادة المحاولة" بعد الإصلاح

## كيفية الاستخدام

### للمطورين
```dart
// فحص وإصلاح قاعدة البيانات
final databaseService = DatabaseService();
await databaseService.checkAndFixDatabase();

// إجبار الترقية
await databaseService.forceDatabaseUpgrade();
```

### للمستخدمين
1. عند حدوث خطأ في حفظ الفاتورة
2. اضغط على زر "إصلاح قاعدة البيانات"
3. انتظر حتى يتم الإصلاح
4. حاول حفظ الفاتورة مرة أخرى

## الأعمدة المطلوبة في جدول الفواتير

```sql
CREATE TABLE invoices (
  id TEXT PRIMARY KEY,
  invoiceNumber TEXT UNIQUE NOT NULL,
  customerId TEXT NOT NULL,
  customerName TEXT NOT NULL,
  customerPhone TEXT DEFAULT "",           -- تم إضافته
  invoiceDate TEXT NOT NULL,
  items TEXT,
  subtotal REAL NOT NULL DEFAULT 0,
  discountAmount REAL NOT NULL DEFAULT 0,  -- تم إضافته
  discountPercentage REAL NOT NULL DEFAULT 0, -- تم إضافته
  total REAL NOT NULL DEFAULT 0,
  paidAmount REAL NOT NULL DEFAULT 0,     -- تم إضافته
  status INTEGER NOT NULL DEFAULT 0,
  notes TEXT,
  createdBy TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL,
  FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
);
```

## إصدارات قاعدة البيانات

- **الإصدار 11**: إنشاء جدول الفواتير الأساسي
- **الإصدار 12**: إضافة أعمدة الخصم
- **الإصدار 13**: إضافة عمود المبلغ المدفوع
- **الإصدار 14**: إضافة عمود رقم هاتف العميل

## ملاحظات مهمة

1. **النسخ الاحتياطي**: يرجى عمل نسخة احتياطية من قاعدة البيانات قبل الترقية
2. **إعادة التشغيل**: قد تحتاج لإعادة تشغيل التطبيق بعد الإصلاح
3. **البيانات**: لن يتم فقدان البيانات الموجودة أثناء الإصلاح
4. **الأداء**: قد يستغرق الإصلاح بضع ثوانٍ

## استكشاف الأخطاء

إذا استمرت المشكلة:

1. تحقق من سجلات التطبيق (Console)
2. تأكد من أن قاعدة البيانات قابلة للكتابة
3. حاول حذف قاعدة البيانات وإعادة إنشائها
4. تأكد من وجود مساحة كافية على الجهاز

## الدعم

للمساعدة الإضافية، يرجى:
1. فحص سجلات التطبيق
2. التقاط صورة للخطأ
3. وصف الخطوات التي أدت إلى المشكلة
