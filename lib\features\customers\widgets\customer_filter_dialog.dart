import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';

class CustomerFilterDialog extends StatefulWidget {
  final CustomerType? selectedType;
  final String? selectedGovernorate;
  final bool showWithBalanceOnly;
  final Function(CustomerType?, String?, bool) onApply;

  const CustomerFilterDialog({
    super.key,
    this.selectedType,
    this.selectedGovernorate,
    this.showWithBalanceOnly = false,
    required this.onApply,
  });

  @override
  State<CustomerFilterDialog> createState() => _CustomerFilterDialogState();
}

class _CustomerFilterDialogState extends State<CustomerFilterDialog> {
  CustomerType? _selectedType;
  String? _selectedGovernorate;
  bool _showWithBalanceOnly = false;

  // Egyptian Governorates
  final List<String> _governorates = [
    'القاهرة',
    'الجيزة',
    'الإسكندرية',
    'القليوبية',
    'الشرقية',
    'المنوفية',
    'الدقهلية',
    'البحيرة',
    'كفر الشيخ',
    'الغربية',
    'دمياط',
    'بورسعيد',
    'الإسماعيلية',
    'السويس',
    'شمال سيناء',
    'جنوب سيناء',
    'المنيا',
    'بني سويف',
    'الفيوم',
    'أسيوط',
    'سوهاج',
    'قنا',
    'الأقصر',
    'أسوان',
    'البحر الأحمر',
    'الوادي الجديد',
    'مطروح',
  ];

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
    _selectedGovernorate = widget.selectedGovernorate;
    _showWithBalanceOnly = widget.showWithBalanceOnly;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'تصفية العملاء',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
        textAlign: TextAlign.center,
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTypeFilter(),
              SizedBox(height: 20.h),
              _buildGovernorateFilter(),
              SizedBox(height: 20.h),
              _buildBalanceFilter(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              _selectedType = null;
              _selectedGovernorate = null;
              _showWithBalanceOnly = false;
            });
          },
          child: Text(
            'مسح الكل',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'إلغاء',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApply(
              _selectedType,
              _selectedGovernorate,
              _showWithBalanceOnly,
            );
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            'تطبيق',
            style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع العميل:',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 12.h),
        ...CustomerType.values.map((type) {
          return RadioListTile<CustomerType?>(
            title: Text(
              _getCustomerTypeText(type),
              style: TextStyle(fontSize: 14.sp, fontFamily: 'Cairo'),
            ),
            value: type,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value;
              });
            },
            activeColor: AppColors.primary,
            contentPadding: EdgeInsets.zero,
          );
        }),
        RadioListTile<CustomerType?>(
          title: Text(
            'جميع الأنواع',
            style: TextStyle(fontSize: 14.sp, fontFamily: 'Cairo'),
          ),
          value: null,
          groupValue: _selectedType,
          onChanged: (value) {
            setState(() {
              _selectedType = value;
            });
          },
          activeColor: AppColors.primary,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildGovernorateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المحافظة:',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String?>(
              value: _selectedGovernorate,
              hint: Text(
                'اختر المحافظة',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontFamily: 'Cairo',
                ),
              ),
              isExpanded: true,
              items: [
                DropdownMenuItem<String?>(
                  value: null,
                  child: Text(
                    'جميع المحافظات',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                ..._governorates.map((governorate) {
                  return DropdownMenuItem<String>(
                    value: governorate,
                    child: Text(
                      governorate,
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        color: AppColors.textPrimary,
                      ),
                    ),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedGovernorate = value;
                });
              },
              icon: Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 14.sp,
                fontFamily: 'Cairo',
              ),
              dropdownColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceFilter() {
    return CheckboxListTile(
      title: Text(
        'عرض العملاء المدينين فقط',
        style: TextStyle(fontSize: 14.sp, fontFamily: 'Cairo'),
      ),
      subtitle: Text(
        'العملاء الذين لديهم رصيد مستحق',
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      value: _showWithBalanceOnly,
      onChanged: (value) {
        setState(() {
          _showWithBalanceOnly = value ?? false;
        });
      },
      activeColor: AppColors.primary,
      contentPadding: EdgeInsets.zero,
      controlAffinity: ListTileControlAffinity.leading,
    );
  }

  String _getCustomerTypeText(CustomerType type) {
    switch (type) {
      case CustomerType.distributor:
        return 'موزع';
      case CustomerType.medicalOfficeA:
        return 'مكتب طبي أ';
      case CustomerType.medicalOfficeB:
        return 'مكتب طبي ب';
      case CustomerType.majorClient:
        return 'عميل كبير';
    }
  }
}
