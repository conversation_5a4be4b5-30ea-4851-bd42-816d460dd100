import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/delete_protection_dialog.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';
import '../widgets/customer_card.dart';

class CustomerManagementScreen extends ConsumerStatefulWidget {
  const CustomerManagementScreen({super.key});

  @override
  ConsumerState<CustomerManagementScreen> createState() =>
      _CustomerManagementScreenState();
}

class _CustomerManagementScreenState
    extends ConsumerState<CustomerManagementScreen> {
  final CustomerService _customerService = CustomerService();
  final CollectionService _collectionService = CollectionService();

  int _selectedTabIndex = 0;
  bool _isLoading = false;
  List<CustomerModel> _allCustomers = [];
  Map<String, dynamic> _statistics = {};
  double _totalPendingCollections = 0.0; // إضافة متغير لمبالغ التحصيل المستحقة

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _loadStatistics();
  }

  Future<void> _loadCustomers() async {
    setState(() => _isLoading = true);
    try {
      _allCustomers = await _customerService.getAllCustomers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملاء: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final customers = await _customerService.getAllCustomers();
      final activeCustomers = customers.where((c) => c.isActive).length;
      final inactiveCustomers = customers.where((c) => !c.isActive).length;
      final customersWithBalance = customers
          .where((c) => c.balance != 0)
          .length;
      final totalBalance = customers.fold(0.0, (sum, c) => sum + c.balance);

      // حساب مبالغ التحصيل المستحقة
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalPendingCollections = 0.0;
      for (final invoice in pendingInvoices) {
        totalPendingCollections += invoice.calculateRemainingAmount();
      }

      setState(() {
        _statistics = {
          'total': customers.length,
          'active': activeCustomers,
          'inactive': inactiveCustomers,
          'withBalance': customersWithBalance,
          'totalBalance': totalBalance,
        };
        _totalPendingCollections = totalPendingCollections;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          'إدارة العملاء',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white, size: 20.sp),
        actions: [
          IconButton(
            onPressed: _showStatistics,
            icon: Icon(Icons.analytics, color: Colors.white, size: 24.sp),
            tooltip: 'الإحصائيات',
          ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.white, size: 24.sp),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh, size: 18.sp, color: AppColors.success),
                    SizedBox(width: 8.w),
                    Text(
                      'تحديث البيانات',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات السريع
          _buildQuickStatsBar(),

          // شريط التبويبات
          _buildTabBar(),

          // المحتوى
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary,
                      ),
                    ),
                  )
                : _buildCustomerList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        backgroundColor: AppColors.primary,
        onPressed: () => _addNewCustomer(),
        icon: Icon(Icons.add, color: Colors.white, size: 20.sp),
        label: Text(
          'إضافة عميل',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
    );
  }

  Widget _buildQuickStatsBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              _buildStatItem(
                icon: Icons.people,
                label: 'إجمالي العملاء',
                value: '${_statistics['total'] ?? 0}',
                color: AppColors.primary,
              ),
              SizedBox(width: 16.w),
              _buildStatItem(
                icon: Icons.check_circle,
                label: 'العملاء النشطين',
                value: '${_statistics['active'] ?? 0}',
                color: AppColors.success,
              ),
              SizedBox(width: 16.w),
              _buildStatItem(
                icon: Icons.account_balance_wallet,
                label: 'إجمالي الرصيد',
                value:
                    '${(_statistics['totalBalance'] ?? 0.0).toStringAsFixed(2)}',
                color: AppColors.warning,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          // بطاقة إضافية لمبالغ التحصيل المستحقة
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade50, Colors.orange.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.payment, color: Colors.orange.shade700, size: 20.sp),
                SizedBox(width: 8.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مبالغ التحصيل المستحقة',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange.shade800,
                        ),
                      ),
                      Text(
                        'من جميع العملاء',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 10.sp,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${_totalPendingCollections.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade800,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          _buildTabButton(
            index: 0,
            icon: Icons.list,
            label: 'جميع العملاء',
            isSelected: _selectedTabIndex == 0,
          ),
          _buildTabButton(
            index: 1,
            icon: Icons.check_circle,
            label: 'العملاء النشطين',
            isSelected: _selectedTabIndex == 1,
          ),
          _buildTabButton(
            index: 2,
            icon: Icons.cancel,
            label: 'العملاء غير النشطين',
            isSelected: _selectedTabIndex == 2,
          ),
          _buildTabButton(
            index: 3,
            icon: Icons.account_balance_wallet,
            label: 'العملاء ذوي الرصيد',
            isSelected: _selectedTabIndex == 3,
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required int index,
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () => setState(() => _selectedTabIndex = index),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : AppColors.textSecondary,
                size: 20.sp,
              ),
              SizedBox(height: 4.h),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: isSelected ? Colors.white : AppColors.textSecondary,
                  fontFamily: 'Cairo',
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerList() {
    List<CustomerModel> filteredCustomers = [];

    switch (_selectedTabIndex) {
      case 0: // جميع العملاء
        filteredCustomers = _allCustomers;
        break;
      case 1: // العملاء النشطين
        filteredCustomers = _allCustomers.where((c) => c.isActive).toList();
        break;
      case 2: // العملاء غير النشطين
        filteredCustomers = _allCustomers.where((c) => !c.isActive).toList();
        break;
      case 3: // العملاء ذوي الرصيد
        filteredCustomers = _allCustomers.where((c) => c.balance != 0).toList();
        break;
    }

    if (filteredCustomers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 24.h),
            Text(
              'لا يوجد عملاء في هذا التصنيف',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'جرب تغيير التصنيف أو أضف عملاء جدد',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: filteredCustomers.length,
      itemBuilder: (context, index) {
        final customer = filteredCustomers[index];
        return CustomerCard(
          customer: customer,
          onTap: () => _viewCustomerDetails(customer),
          onEdit: () => _editCustomer(customer),
          onDelete: () => _deleteCustomer(customer),
        );
      },
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'refresh':
        _loadCustomers();
        _loadStatistics();
        break;
    }
  }

  void _addNewCustomer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
    );

    if (result != null) {
      _loadCustomers();
      _loadStatistics();
    }
  }

  void _editCustomer(CustomerModel customer) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCustomerScreen(customer: customer),
      ),
    );

    if (result != null) {
      _loadCustomers();
      _loadStatistics();
    }
  }

  Future<void> _deleteCustomer(CustomerModel customer) async {
    showDeleteProtectionDialog(
      context: context,
      title: 'حذف العميل',
      message:
          'سيتم حذف العميل نهائياً من قاعدة البيانات. لا يمكن التراجع عن هذا الإجراء.',
      itemName: customer.name ?? 'العميل المحدد',
      onConfirm: () async {
        try {
          setState(() => _isLoading = true);
          final success = await _customerService.deleteCustomer(customer.id);

          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم حذف العميل "${customer.name}" بنجاح',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.success,
              ),
            );
            _loadCustomers();
            _loadStatistics();
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'فشل في حذف العميل: $e',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.error,
              ),
            );
          }
        } finally {
          if (mounted) {
            setState(() => _isLoading = false);
          }
        }
      },
    );
  }

  void _viewCustomerDetails(CustomerModel customer) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailsScreen(customer: customer),
      ),
    );

    // إذا تم حذف العميل، قم بتحديث القائمة
    if (result != null && result is Map) {
      if (result['deleted'] == true) {
        // إزالة العميل من القائمة فوراً
        setState(() {
          _allCustomers.removeWhere((c) => c.id == result['customerId']);
        });

        // إعادة تحميل الإحصائيات
        _loadStatistics();

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف العميل بنجاح',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    }
  }

  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إحصائيات العملاء',
          style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow('إجمالي العملاء', '${_statistics['total'] ?? 0}'),
            _buildStatRow('العملاء النشطين', '${_statistics['active'] ?? 0}'),
            _buildStatRow(
              'العملاء غير النشطين',
              '${_statistics['inactive'] ?? 0}',
            ),
            _buildStatRow(
              'العملاء ذوي الرصيد',
              '${_statistics['withBalance'] ?? 0}',
            ),
            _buildStatRow(
              'إجمالي الرصيد',
              '${(_statistics['totalBalance'] ?? 0.0).toStringAsFixed(2)} جنيه',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إغلاق',
              style: TextStyle(fontFamily: 'Cairo', color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
