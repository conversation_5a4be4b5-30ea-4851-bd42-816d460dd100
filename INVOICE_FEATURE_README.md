# نظام إدارة الفواتير - أطلس للمستلزمات الطبية

## نظرة عامة

تم إضافة نظام إدارة فواتير متكامل إلى تطبيق أطلس للمستلزمات الطبية، يدعم اللغة العربية بالكامل (RTL) ويعمل بكفاءة على جميع الشاشات (موبايل، تابلت، وويب).

## المميزات الرئيسية

### 1. تصميم واجهة عصري
- **شاشة عرض الفواتير**: قائمة منظمة تحتوي على:
  - رقم الفاتورة
  - اسم العميل
  - تاريخ الفاتورة
  - إجمالي المبلغ
  - حالة الدفع (مدفوع / غير مدفوع / ملغي)
- **زر إضافة فاتورة**: Floating Action Button باللون الأزرق الفاتح
- **ألوان التصميم**: أزرق فاتح + تركوازي فاتح + خلفية بيضاء

### 2. شاشة إضافة فاتورة
- **اختيار العميل**: من قائمة العملاء الموجودة مع إمكانية البحث
- **تاريخ الفاتورة**: افتراضي تاريخ اليوم مع إمكانية التعديل
- **جدول المنتجات**: 
  - اختيار المنتج من قائمة منسدلة
  - إدخال الكمية
  - إدخال سعر الوحدة
  - عرض الإجمالي لكل بند (الكمية × السعر)
- **الإجمالي الكلي**: عرض تلقائي أسفل الصفحة
- **أزرار الحفظ والإلغاء**

### 3. الوظائف المتقدمة
- **تخزين محلي**: قاعدة بيانات SQLite
- **تعديل وحذف**: إمكانية تعديل الفاتورة أو حذفها
- **البحث**: البحث برقم الفاتورة أو اسم العميل أو رقم الهاتف
- **المشاركة**: مشاركة الفاتورة على واتساب أو نسخ إلى الحافظة
- **حالة الدفع**: تحديث سريع (مدفوع / غير مدفوع) بزر واحد
- **الفلترة**: فلترة حسب حالة الدفع

## البنية التقنية

### النماذج (Models)
- `InvoiceModel`: نموذج الفاتورة الرئيسي
- `InvoiceItem`: نموذج عنصر الفاتورة

### الخدمات (Services)
- `InvoiceService`: خدمة إدارة الفواتير
- `DatabaseService`: تحديث قاعدة البيانات لدعم الفواتير

### الشاشات (Screens)
- `InvoicesScreen`: شاشة عرض جميع الفواتير
- `AddInvoiceScreen`: شاشة إضافة فاتورة جديدة
- `InvoiceDetailsScreen`: شاشة تفاصيل الفاتورة

### المكونات (Widgets)
- `InvoiceCard`: بطاقة عرض الفاتورة
- `InvoiceFilterDialog`: dialog فلترة الفواتير
- `InvoiceItemForm`: نموذج تعديل عنصر الفاتورة
- `CustomerSelector`: selector اختيار العميل
- `ProductSelector`: selector اختيار المنتج
- `InvoiceItemsTable`: جدول عرض عناصر الفاتورة

## قاعدة البيانات

### جدول الفواتير (invoices)
```sql
CREATE TABLE invoices (
  id TEXT PRIMARY KEY,
  invoiceNumber TEXT UNIQUE NOT NULL,
  customerId TEXT NOT NULL,
  customerName TEXT NOT NULL,
  customerPhone TEXT,
  invoiceDate TEXT NOT NULL,
  items TEXT,
  subtotal REAL NOT NULL DEFAULT 0,
  total REAL NOT NULL DEFAULT 0,
  status INTEGER NOT NULL DEFAULT 0,
  notes TEXT,
  createdBy TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL,
  FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
);
```

### حالات الفاتورة
- `0`: غير مدفوع (pending)
- `1`: مدفوع (paid)
- `2`: ملغي (cancelled)

## الاستخدام

### إضافة فاتورة جديدة
1. انتقل إلى "إدارة الفواتير" من القائمة الجانبية
2. اضغط على زر "+" لإضافة فاتورة جديدة
3. اختر العميل من القائمة
4. أضف المنتجات المطلوبة
5. اضبط الكميات والأسعار
6. أضف ملاحظات (اختياري)
7. اضغط "حفظ الفاتورة"

### إدارة الفواتير
- **عرض الفواتير**: قائمة بجميع الفواتير مرتبة حسب التاريخ
- **البحث**: استخدم شريط البحث للعثور على فاتورة محددة
- **الفلترة**: اضغط على أيقونة الفلترة لتصفية الفواتير حسب الحالة
- **تعديل الحالة**: اضغط على زر الحالة لتغييرها
- **المشاركة**: اضغط على زر المشاركة لنسخ الفاتورة

### تفاصيل الفاتورة
- اضغط على أي فاتورة لعرض تفاصيلها الكاملة
- يمكنك تعديل حالة الدفع أو حذف الفاتورة
- إمكانية مشاركة الفاتورة أو طباعتها

## المميزات التقنية

### دعم RTL
- جميع النصوص باللغة العربية
- تخطيط من اليمين إلى اليسار
- أرقام عربية

### الاستجابة (Responsive)
- تصميم متجاوب لجميع أحجام الشاشات
- تحسين للهواتف والأجهزة اللوحية وأجهزة الكمبيوتر

### الأداء
- تحميل تدريجي للبيانات
- تحديث فوري للإجماليات
- حفظ تلقائي للبيانات

### الأمان
- التحقق من صحة البيانات
- حماية من الأخطاء
- نسخ احتياطي تلقائي

## التحديثات المستقبلية

### المخطط لها
- [ ] طباعة PDF
- [ ] إرسال عبر البريد الإلكتروني
- [ ] إشعارات تلقائية
- [ ] تقارير متقدمة
- [ ] دعم العملات المتعددة
- [ ] نظام الخصومات
- [ ] إدارة المدفوعات

### التحسينات المقترحة
- [ ] واجهة مستخدم محسنة
- [ ] أداء أسرع
- [ ] ميزات بحث متقدمة
- [ ] تصدير البيانات
- [ ] دعم الوضع المظلم

## الدعم الفني

للاستفسارات أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**الإصدار**: 1.0.0  
**تاريخ الإصدار**: ديسمبر 2024  
**المطور**: فريق أطلس للمستلزمات الطبية
