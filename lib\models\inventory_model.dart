class InventoryModel {
  final String id;
  final String productId;
  final String
  warehouseType; // main_warehouse, branch_warehouse, sales_car_warehouse
  final int totalQuantity;
  final int availableQuantity;
  final int reservedQuantity;
  final int minStockLevel;
  final DateTime lastUpdated;
  final String updatedBy;

  const InventoryModel({
    required this.id,
    required this.productId,
    required this.warehouseType,
    required this.totalQuantity,
    required this.availableQuantity,
    required this.reservedQuantity,
    this.minStockLevel = 10,
    required this.lastUpdated,
    required this.updatedBy,
  });

  factory InventoryModel.fromJson(Map<String, dynamic> json) {
    return InventoryModel(
      id: json['id'] ?? '',
      productId: json['productId'] ?? '',
      warehouseType: json['warehouseType'] ?? 'main_warehouse',
      totalQuantity: json['totalQuantity'] ?? 0,
      availableQuantity: json['availableQuantity'] ?? 0,
      reservedQuantity: json['reservedQuantity'] ?? 0,
      minStockLevel: json['minStockLevel'] ?? 10,
      lastUpdated: DateTime.parse(
        json['lastUpdated'] ?? DateTime.now().toIso8601String(),
      ),
      updatedBy: json['updatedBy'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'warehouseType': warehouseType,
      'totalQuantity': totalQuantity,
      'availableQuantity': availableQuantity,
      'reservedQuantity': reservedQuantity,
      'minStockLevel': minStockLevel,
      'lastUpdated': lastUpdated.toIso8601String(),
      'updatedBy': updatedBy,
    };
  }

  InventoryModel copyWith({
    String? id,
    String? productId,
    String? warehouseType,
    int? totalQuantity,
    int? availableQuantity,
    int? reservedQuantity,
    int? minStockLevel,
    DateTime? lastUpdated,
    String? updatedBy,
  }) {
    return InventoryModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseType: warehouseType ?? this.warehouseType,
      totalQuantity: totalQuantity ?? this.totalQuantity,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      reservedQuantity: reservedQuantity ?? this.reservedQuantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  String get warehouseDisplayName {
    switch (warehouseType) {
      case 'main_warehouse':
        return 'المخزن الرئيسي';
      case 'branch_warehouse':
        return 'مخزن الفرع';
      case 'sales_car_warehouse':
        return 'مخزن سيارة المندوب';
      default:
        return 'غير محدد';
    }
  }

  StockStatus get stockStatus {
    if (availableQuantity <= 0) return StockStatus.outOfStock;
    if (availableQuantity <= minStockLevel) return StockStatus.lowStock;
    return StockStatus.inStock;
  }

  String get stockStatusDisplayName {
    switch (stockStatus) {
      case StockStatus.outOfStock:
        return 'نفد المخزون';
      case StockStatus.lowStock:
        return 'مخزون منخفض';
      case StockStatus.inStock:
        return 'متوفر';
    }
  }

  bool get needsRestock => stockStatus != StockStatus.inStock;

  double get stockPercentage {
    if (totalQuantity <= 0) return 0.0;
    return availableQuantity / totalQuantity;
  }
}

enum StockStatus { inStock, lowStock, outOfStock }
