import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/app_colors.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/custom_button.dart';

class LoginForm extends StatefulWidget {
  final bool showButton;
  const LoginForm({super.key, this.showButton = true});

  @override
  State<LoginForm> createState() => LoginFormState();
}

class LoginFormState extends State<LoginForm> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _autoLogin = false;

  late AnimationController _buttonController;
  late Animation<double> _buttonScaleAnimation;

  // مفاتيح حفظ البيانات
  static const String _phoneKey = 'saved_phone';
  static const String _passwordKey = 'saved_password';
  static const String _rememberMeKey = 'remember_me';

  @override
  void initState() {
    super.initState();

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeInOut),
    );

    // تحميل البيانات المحفوظة
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  // واجهة استدعاء من خارج الودجت
  void submit() {
    _handleLogin();
  }

  // تحميل البيانات المحفوظة
  Future<void> _loadSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
      final autoLogin = prefs.getBool('auto_login') ?? false;

      if (mounted) {
        setState(() {
          _rememberMe = rememberMe;
          _autoLogin = autoLogin;
        });
      }
    } catch (e) {
      // تجاهل الأخطاء في تحميل البيانات المحفوظة
      debugPrint('Error loading saved credentials: $e');
    }
  }

  // حفظ البيانات
  Future<void> _saveCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rememberMeKey, _rememberMe);
      await prefs.setBool('auto_login', _autoLogin);

      if (!_rememberMe && !_autoLogin) {
        // مسح البيانات المحفوظة إذا لم يتم تحديد أي خيار
        await prefs.remove(_phoneKey);
        await prefs.remove(_passwordKey);
      }
    } catch (e) {
      debugPrint('Error saving credentials: $e');
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.signInWithPhoneAndPassword(
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        keepSignedIn: _rememberMe,
        enableAutoLogin: _autoLogin,
      );
      await _saveCredentials();
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white, size: 20.sp),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    e.toString(),
                    style: const TextStyle(fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حقل رقم الهاتف
            CustomTextField(
              controller: _phoneController,
              labelText: 'رقم الهاتف',
              hintText: 'أدخل رقم الهاتف',
              prefixIcon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                if (value.length < 10) {
                  return 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل';
                }
                return null;
              },
            ),

            SizedBox(height: 20.h),

            // حقل كلمة المرور
            CustomTextField(
              controller: _passwordController,
              labelText: 'كلمة المرور',
              hintText: 'أدخل كلمة المرور',
              prefixIcon: Icons.lock_outline,
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
                icon: Icon(
                  _obscurePassword
                      ? Icons.visibility_outlined
                      : Icons.visibility_off_outlined,
                  color: AppColors.textSecondary,
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال كلمة المرور';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                }
                return null;
              },
            ),

            SizedBox(height: 16.h),

            // خيارات إضافية
            Column(
              children: [
                // الصف الأول: تذكرني و تسجيل دخول تلقائي
                Row(
                  children: [
                    // تذكرني
                    Expanded(
                      child: Row(
                        children: [
                          Transform.scale(
                            scale: 0.8,
                            child: Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                              activeColor: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                          ),
                          Text(
                            'تذكرني',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textSecondary,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    ),

                    // تسجيل دخول تلقائي
                    Expanded(
                      child: Row(
                        children: [
                          Transform.scale(
                            scale: 0.8,
                            child: Checkbox(
                              value: _autoLogin,
                              onChanged: (value) {
                                setState(() {
                                  _autoLogin = value ?? false;
                                });
                              },
                              activeColor: AppColors.success,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                          ),
                          Flexible(
                            child: Text(
                              'دخول تلقائي',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // الصف الثاني: نسيت كلمة المرور
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        HapticFeedback.selectionClick();
                        _openForgotPasswordSheet();
                      },
                      child: Text(
                        'نسيت كلمة المرور؟',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.primary,
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: widget.showButton ? 32.h : 8.h),

            if (widget.showButton)
              AnimatedBuilder(
                animation: _buttonScaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _buttonScaleAnimation.value,
                    child: CustomButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              _buttonController.forward().then((_) {
                                _buttonController.reverse();
                                _handleLogin();
                              });
                            },
                      text: _isLoading
                          ? 'جاري تسجيل الدخول...'
                          : 'تسجيل الدخول',
                      isLoading: _isLoading,
                      icon: _isLoading ? null : Icons.login_outlined,
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  void _openForgotPasswordSheet() {
    final phoneInit = _phoneController.text.trim();
    final phoneController = TextEditingController(text: phoneInit);
    final otpController = TextEditingController();
    final newPassController = TextEditingController();
    final confirmPassController = TextEditingController();
    bool otpSent = false;
    bool submitting = false;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      showDragHandle: true,
      useRootNavigator: true,
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (ctx) {
        return StatefulBuilder(
          builder: (ctx, setSheetState) {
            Future<void> sendOtp() async {
              if (phoneController.text.trim().length < 10) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'أدخل رقم هاتف صحيح',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }
              setSheetState(() => submitting = true);
              try {
                final auth = Provider.of<AuthService>(context, listen: false);
                await auth.sendPasswordResetOtp(phoneController.text.trim());
                setSheetState(() => otpSent = true);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم إرسال كود التحقق (OTP)',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      e.toString(),
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
              } finally {
                setSheetState(() => submitting = false);
              }
            }

            Future<void> confirmReset() async {
              final phone = phoneController.text.trim();
              final code = otpController.text.trim();
              final newPass = newPassController.text;
              final confirmPass = confirmPassController.text;
              if (phone.length < 10) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'أدخل رقم هاتف صحيح',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }
              if (code.length != 6) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'أدخل كود مكون من 6 أرقام',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }
              if (newPass.length < 6 || newPass != confirmPass) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'كلمة مرور غير صالحة أو غير متطابقة',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }
              setSheetState(() => submitting = true);
              try {
                final auth = Provider.of<AuthService>(context, listen: false);
                await auth.resetPasswordWithOtp(
                  phone: phone,
                  code: code,
                  newPassword: newPass,
                );
                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم تحديث كلمة المرور بنجاح',
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      e.toString(),
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
              } finally {
                setSheetState(() => submitting = false);
              }
            }

            return Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                bottom: MediaQuery.of(ctx).viewInsets.bottom + 16.h,
                top: 16.h,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'استعادة كلمة المرور',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w700,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  SizedBox(height: 12.h),
                  CustomTextField(
                    controller: phoneController,
                    labelText: 'رقم الهاتف',
                    hintText: 'أدخل رقم الهاتف',
                    prefixIcon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  SizedBox(height: 12.h),
                  if (otpSent) ...[
                    CustomTextField(
                      controller: otpController,
                      labelText: 'كود التحقق (OTP)',
                      hintText: 'أدخل الكود المرسل',
                      prefixIcon: Icons.verified_outlined,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                    SizedBox(height: 12.h),
                    CustomTextField(
                      controller: newPassController,
                      labelText: 'كلمة مرور جديدة',
                      hintText: 'أدخل كلمة مرور جديدة',
                      prefixIcon: Icons.lock_reset,
                      obscureText: true,
                    ),
                    SizedBox(height: 12.h),
                    CustomTextField(
                      controller: confirmPassController,
                      labelText: 'تأكيد كلمة المرور',
                      hintText: 'أعد إدخال كلمة المرور',
                      prefixIcon: Icons.lock_outline,
                      obscureText: true,
                    ),
                  ],
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: submitting
                              ? null
                              : () => Navigator.of(ctx).pop(),
                          child: Text(
                            'إلغاء',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: submitting
                              ? null
                              : () => otpSent ? confirmReset() : sendOtp(),
                          child: Text(
                            otpSent ? 'تأكيد' : 'إرسال كود',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}


