import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/egyptian_governorates.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/custom_text_field.dart';

class QuickEditCustomerScreen extends StatefulWidget {
  final CustomerModel customer;

  const QuickEditCustomerScreen({super.key, required this.customer});

  @override
  State<QuickEditCustomerScreen> createState() =>
      _QuickEditCustomerScreenState();
}

class _QuickEditCustomerScreenState extends State<QuickEditCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final CustomerService _customerService = CustomerService();

  // Controllers
  final _nameController = TextEditingController();
  final _activityController = TextEditingController();
  final _phone1Controller = TextEditingController();
  final _phone2Controller = TextEditingController();
  final _emailController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _notesController = TextEditingController();

  // Form data
  CustomerType _selectedType = CustomerType.medicalOfficeB;
  String? _selectedGovernorate;
  String? _selectedCity;
  bool _isActive = true;

  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _populateFormData();

    // مراقبة التغييرات
    _nameController.addListener(_onFieldChanged);
    _activityController.addListener(_onFieldChanged);
    _phone1Controller.addListener(_onFieldChanged);
    _phone2Controller.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _taxNumberController.addListener(_onFieldChanged);
    _notesController.addListener(_onFieldChanged);
  }

  void _populateFormData() {
    final customer = widget.customer;
    _nameController.text = customer.name;
    _activityController.text = customer.activity;
    _phone1Controller.text = customer.phone1 ?? '';
    _phone2Controller.text = customer.phone2 ?? '';
    _emailController.text = customer.email ?? '';
    _taxNumberController.text = customer.taxNumber ?? '';
    _notesController.text = customer.notes ?? '';

    _selectedType = customer.type;
    _selectedGovernorate = customer.governorate;
    _selectedCity = customer.city;
    _isActive = customer.isActive;
  }

  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() => _hasChanges = true);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _activityController.dispose();
    _phone1Controller.dispose();
    _phone2Controller.dispose();
    _emailController.dispose();
    _taxNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          'تعديل سريع - ${widget.customer.name}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white, size: 20.sp),
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _saveChanges,
              child: Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Cairo',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSection(
                title: 'المعلومات الأساسية',
                icon: Icons.person,
                children: [
                  CustomTextField(
                    controller: _nameController,
                    labelText: 'اسم العميل',
                    hintText: 'أدخل اسم العميل',
                    prefixIcon: Icons.person,
                    validator: (value) {
                      if (value?.trim().isEmpty == true) {
                        return 'اسم العميل مطلوب';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  _buildCustomerTypeSelector(),
                  SizedBox(height: 16.h),
                  CustomTextField(
                    controller: _activityController,
                    labelText: 'النشاط',
                    hintText: 'أدخل نشاط العميل',
                    prefixIcon: Icons.work,
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // معلومات الاتصال
              _buildSection(
                title: 'معلومات الاتصال',
                icon: Icons.phone,
                children: [
                  CustomTextField(
                    controller: _phone1Controller,
                    labelText: 'رقم الهاتف 1',
                    hintText: '01xxxxxxxxx',
                    prefixIcon: Icons.phone,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value?.trim().isEmpty == true) {
                        return 'رقم الهاتف مطلوب';
                      }
                      if (!_customerService.isValidPhoneFormat(value!)) {
                        return 'تنسيق رقم الهاتف غير صحيح';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  CustomTextField(
                    controller: _phone2Controller,
                    labelText: 'رقم الهاتف 2 (اختياري)',
                    hintText: '01xxxxxxxxx',
                    prefixIcon: Icons.phone_android,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value?.trim().isNotEmpty == true) {
                        if (!_customerService.isValidPhoneFormat(value!)) {
                          return 'تنسيق رقم الهاتف غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),
                  CustomTextField(
                    controller: _emailController,
                    labelText: 'البريد الإلكتروني (اختياري)',
                    hintText: '<EMAIL>',
                    prefixIcon: Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // معلومات الموقع
              _buildSection(
                title: 'معلومات الموقع',
                icon: Icons.location_on,
                children: [
                  _buildGovernorateSelector(),
                  SizedBox(height: 16.h),
                  _buildCitySelector(),
                ],
              ),

              SizedBox(height: 24.h),

              // معلومات إضافية
              _buildSection(
                title: 'معلومات إضافية',
                icon: Icons.info,
                children: [
                  CustomTextField(
                    controller: _taxNumberController,
                    labelText: 'الرقم الضريبي (اختياري)',
                    hintText: 'أدخل الرقم الضريبي',
                    prefixIcon: Icons.receipt,
                  ),
                  SizedBox(height: 16.h),
                  CustomTextField(
                    controller: _notesController,
                    labelText: 'ملاحظات (اختياري)',
                    hintText: 'أدخل ملاحظات إضافية',
                    prefixIcon: Icons.note,
                    maxLines: 3,
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // حالة العميل
              _buildSection(
                title: 'حالة العميل',
                icon: Icons.toggle_on,
                children: [
                  SwitchListTile(
                    title: Text(
                      'تفعيل العميل',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      _isActive ? 'العميل نشط' : 'العميل غير نشط',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        color: _isActive ? AppColors.success : AppColors.error,
                      ),
                    ),
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                        _hasChanges = true;
                      });
                    },
                    activeColor: AppColors.success,
                  ),
                ],
              ),

              SizedBox(height: 32.h),

              // أزرار الإجراءات
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          ...children,
        ],
      ),
    );
  }

  Widget _buildCustomerTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع العميل',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<CustomerType>(
          value: _selectedType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 8.h,
            ),
          ),
          items: CustomerType.values.map((type) {
            String label;
            switch (type) {
              case CustomerType.distributor:
                label = 'موزع';
                break;
              case CustomerType.medicalOfficeA:
                label = 'مكتب طبي أ';
                break;
              case CustomerType.medicalOfficeB:
                label = 'مكتب طبي ب';
                break;
              case CustomerType.majorClient:
                label = 'عميل كبير';
                break;
            }
            return DropdownMenuItem(
              value: type,
              child: Text(label, style: TextStyle(fontFamily: 'Cairo')),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedType = value;
                _hasChanges = true;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildGovernorateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المحافظة',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: _selectedGovernorate,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 8.h,
            ),
          ),
          items: EgyptianGovernorates.getAllGovernorates().map((governorate) {
            return DropdownMenuItem(
              value: governorate,
              child: Text(governorate, style: TextStyle(fontFamily: 'Cairo')),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedGovernorate = value;
              _selectedCity = null; // إعادة تعيين المدينة
              _hasChanges = true;
            });
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'المحافظة مطلوبة';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCitySelector() {
    if (_selectedGovernorate == null) return SizedBox.shrink();

    final cities = EgyptianGovernorates.getCitiesForGovernorate(
      _selectedGovernorate!,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المدينة (اختياري)',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: _selectedCity,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 8.h,
            ),
          ),
          items: [
            DropdownMenuItem(
              value: null,
              child: Text(
                'اختر المدينة',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            ...cities.map((city) {
              return DropdownMenuItem(
                value: city,
                child: Text(city, style: TextStyle(fontFamily: 'Cairo')),
              );
            }),
          ],
          onChanged: (value) {
            setState(() {
              _selectedCity = value;
              _hasChanges = true;
            });
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _hasChanges ? _saveChanges : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'حفظ التغييرات',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Cairo',
                    ),
                  ),
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final updatedCustomer = widget.customer.copyWith(
        name: _nameController.text.trim(),
        type: _selectedType,
        activity: _activityController.text.trim(),
        phone1: _phone1Controller.text.trim(),
        phone2: _phone2Controller.text.trim().isEmpty
            ? null
            : _phone2Controller.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        governorate: _selectedGovernorate,
        city: _selectedCity,
        taxNumber: _taxNumberController.text.trim().isEmpty
            ? null
            : _taxNumberController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: _isActive,
        updatedAt: DateTime.now(),
      );

      final success = await _customerService.updateCustomer(updatedCustomer);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث العميل بنجاح',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: AppColors.success,
          ),
        );

        Navigator.pop(context, updatedCustomer);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث العميل',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في تحديث العميل: $e',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
