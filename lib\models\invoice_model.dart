import 'dart:convert';
import '../constants/app_strings.dart';

class InvoiceItem {
  final String id;
  final String productId;
  final String productName;
  final String productCode;
  final double unitPrice;
  final int quantity;
  final double total;
  final String unit;

  const InvoiceItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productCode,
    required this.unitPrice,
    required this.quantity,
    required this.total,
    required this.unit,
  });

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    // التأكد من أن الوحدة صحيحة
    String unit = json['unit'] ?? 'قطعة';
    if (unit.isEmpty || !AppStrings.allUnits.contains(unit)) {
      unit = AppStrings.piece;
    }

    return InvoiceItem(
      id: json['id'] ?? '',
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      productCode: json['productCode'] ?? '',
      unitPrice: (json['unitPrice'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 0,
      total: (json['total'] ?? 0.0).toDouble(),
      unit: unit,
    );
  }

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    // التأكد من أن الوحدة صحيحة
    String unit = map['unit'] ?? 'قطعة';
    if (unit.isEmpty || !AppStrings.allUnits.contains(unit)) {
      unit = AppStrings.piece;
    }

    return InvoiceItem(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      productCode: map['productCode'] ?? '',
      unitPrice: (map['unitPrice'] ?? 0.0).toDouble(),
      quantity: map['quantity'] ?? 0,
      total: (map['total'] ?? 0.0).toDouble(),
      unit: unit,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'productCode': productCode,
      'unitPrice': unitPrice,
      'quantity': quantity,
      'total': total,
      'unit': unit,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'productCode': productCode,
      'unitPrice': unitPrice,
      'quantity': quantity,
      'total': total,
      'unit': unit,
    };
  }

  InvoiceItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productCode,
    double? unitPrice,
    int? quantity,
    double? total,
    String? unit,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      unitPrice: unitPrice ?? this.unitPrice,
      quantity: quantity ?? this.quantity,
      total: total ?? this.total,
      unit: unit ?? this.unit,
    );
  }
}

// Remove the enum and replace with string constants
enum InvoiceStatus {
  pending('معلق'),
  paid('مدفوعة'),
  partial('دفع جزئي'),
  cancelled('ملغي');

  const InvoiceStatus(this.displayName);

  final String displayName;

  @override
  String toString() => displayName;
}

class InvoiceModel {
  final String id;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime invoiceDate;
  final List<InvoiceItem> items;
  final double subtotal;
  final double discountAmount;
  final double discountPercentage;
  final double total;
  final double paidAmount;
  final InvoiceStatus status; // Changed back to InvoiceStatus enum
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.invoiceDate,
    required this.items,
    required this.subtotal,
    this.discountAmount = 0.0,
    this.discountPercentage = 0.0,
    required this.total,
    this.paidAmount = 0.0,
    this.status = InvoiceStatus.pending, // Default to pending
    this.notes,
    this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      id: json['id'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        json['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      items:
          (json['items'] as List<dynamic>?)
              ?.map((item) => InvoiceItem.fromJson(item))
              .toList() ??
          [],
      subtotal: (json['subtotal'] ?? 0.0).toDouble(),
      discountAmount: (json['discountAmount'] ?? 0.0).toDouble(),
      discountPercentage: (json['discountPercentage'] ?? 0.0).toDouble(),
      total: (json['total'] ?? 0.0).toDouble(),
      paidAmount: (json['paidAmount'] ?? 0.0).toDouble(),
      status: _parseStatus(json['status']), // Parse status properly
      notes: json['notes'],
      createdBy: json['createdBy'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  factory InvoiceModel.fromMap(Map<String, dynamic> map) {
    return InvoiceModel(
      id: map['id'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        map['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      items: (() {
        try {
          if (map['items'] is String) {
            final itemsJson =
                jsonDecode(map['items'] as String) as List<dynamic>;
            return itemsJson.map((item) => InvoiceItem.fromJson(item)).toList();
          } else if (map['items'] is List) {
            return (map['items'] as List<dynamic>)
                .map((item) => InvoiceItem.fromJson(item))
                .toList();
          }
          return <InvoiceItem>[];
        } catch (e) {
          return <InvoiceItem>[];
        }
      })(),
      subtotal: (map['subtotal'] ?? 0.0).toDouble(),
      discountAmount: (map['discountAmount'] ?? 0.0).toDouble(),
      discountPercentage: (map['discountPercentage'] ?? 0.0).toDouble(),
      total: (map['total'] ?? 0.0).toDouble(),
      paidAmount: (map['paidAmount'] ?? 0.0).toDouble(),
      status: _parseStatus(map['status']), // Parse status properly
      notes: map['notes'],
      createdBy: map['createdBy'],
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Helper method to parse status from various formats
  static InvoiceStatus _parseStatus(dynamic statusValue) {
    if (statusValue == null) return InvoiceStatus.pending;

    if (statusValue is int) {
      switch (statusValue) {
        case 0:
          return InvoiceStatus.pending;
        case 1:
          return InvoiceStatus.paid;
        case 2:
          return InvoiceStatus.partial;
        case 3:
          return InvoiceStatus.cancelled;
        default:
          return InvoiceStatus.pending;
      }
    } else if (statusValue is String) {
      switch (statusValue) {
        case '0':
          return InvoiceStatus.pending;
        case '1':
          return InvoiceStatus.paid;
        case '2':
          return InvoiceStatus.partial;
        case '3':
          return InvoiceStatus.cancelled;
        case 'معلق':
          return InvoiceStatus.pending;
        case 'مدفوعة':
          return InvoiceStatus.paid;
        case 'دفع جزئي':
          return InvoiceStatus.partial;
        case 'ملغي':
          return InvoiceStatus.cancelled;
        default:
          return InvoiceStatus.pending;
      }
    }

    return InvoiceStatus.pending;
  }

  // حساب الإجمالي من العناصر
  double calculateSubtotal() {
    return items.fold(0.0, (sum, item) => sum + item.total);
  }

  // حساب قيمة الخصم
  double calculateDiscountValue() {
    if (discountPercentage > 0) {
      return subtotal * (discountPercentage / 100);
    }
    return discountAmount;
  }

  // حساب الإجمالي النهائي بعد الخصم
  double calculateFinalTotal() {
    return subtotal - calculateDiscountValue();
  }

  // حساب المبلغ المتبقي بعد الدفع
  double calculateRemainingAmount() {
    return total - paidAmount;
  }

  // حساب نسبة الدفع
  double calculatePaymentPercentage() {
    if (total > 0) {
      return (paidAmount / total) * 100;
    }
    return 0.0;
  }

  // التحقق من اكتمال الدفع
  bool get isFullyPaid => paidAmount >= total;

  // الحصول على حالة الدفع
  String get paymentStatus {
    if (isFullyPaid) {
      return 'مدفوع بالكامل';
    } else if (paidAmount > 0) {
      return 'مدفوع جزئياً';
    } else {
      return 'غير مدفوع';
    }
  }

  // الحصول على حالة الفاتورة بالعربية
  String get statusDisplayName {
    switch (status) {
      case InvoiceStatus.pending:
        return 'معلق';
      case InvoiceStatus.paid:
        return 'تم الدفع بالكامل';
      case InvoiceStatus.partial:
        return 'دفع جزئي';
      case InvoiceStatus.cancelled:
        return 'ملغي';
      default:
        return status
            .displayName; // Return the status string as is if it doesn't match known values
    }
  }

  // الحصول على لون حالة الفاتورة
  String get statusColor {
    switch (status) {
      case InvoiceStatus.pending:
        return '#FFA500'; // برتقالي
      case InvoiceStatus.paid:
        return '#4CAF50'; // أخضر
      case InvoiceStatus.partial:
        return '#2196F3'; // أزرق
      case InvoiceStatus.cancelled:
        return '#F44336'; // أحمر
      default:
        return '#9E9E9E'; // رمادي للقيم غير المعروفة
    }
  }

  // التحقق من إمكانية التعديل
  bool get canEdit => status != InvoiceStatus.cancelled;

  // التحقق من إمكانية التعديل بعد 15 دقيقة من الإنشاء
  bool get canEditAfterTimeLimit {
    if (status == InvoiceStatus.cancelled) {
      return false;
    }

    final now = DateTime.now();
    final timeDifference = now.difference(createdAt);
    final fifteenMinutes = const Duration(minutes: 15);

    return timeDifference < fifteenMinutes;
  }

  // الحصول على الوقت المتبقي للتعديل
  Duration? get remainingEditTime {
    if (status == InvoiceStatus.cancelled) {
      return null;
    }

    final now = DateTime.now();
    final timeDifference = now.difference(createdAt);
    final fifteenMinutes = const Duration(minutes: 15);

    if (timeDifference >= fifteenMinutes) {
      return null; // انتهى الوقت
    }

    return fifteenMinutes - timeDifference;
  }

  // الحصول على رسالة حالة التعديل
  String get editStatusMessage {
    if (status == InvoiceStatus.cancelled) {
      return 'لا يمكن تعديل الفاتورة الملغية';
    }

    final remainingTime = remainingEditTime;
    if (remainingTime == null) {
      return 'لا يمكن تعديل الفاتورة بعد مرور 15 دقيقة من إنشائها';
    }

    final minutes = remainingTime.inMinutes;
    final seconds = remainingTime.inSeconds % 60;
    return 'يمكن التعديل خلال: ${minutes}:${seconds.toString().padLeft(2, '0')}';
  }

  // التحقق من إمكانية الحذف
  bool get canDelete => status == InvoiceStatus.pending;

  // الحصول على عدد العناصر
  int get itemsCount => items.length;

  // الحصول على تاريخ الفاتورة المنسق
  String get formattedDate {
    return '${invoiceDate.day}/${invoiceDate.month}/${invoiceDate.year}';
  }

  // الحصول على الوقت المنسق
  String get formattedTime {
    final hour = invoiceDate.hour;
    final minute = invoiceDate.minute;
    final period = hour >= 12 ? 'م' : 'ص'; // م = PM, ص = AM
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  // الحصول على إجمالي مبلغ التحصيل من جميع العناصر

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'discountAmount': discountAmount,
      'discountPercentage': discountPercentage,
      'total': total,
      'paidAmount': paidAmount,
      'status': status.index, // Now directly using the string value
      'notes': notes,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'items': jsonEncode(items.map((item) => item.toJson()).toList()),
      'subtotal': subtotal,
      'discountAmount': discountAmount,
      'discountPercentage': discountPercentage,
      'total': total,
      'paidAmount': paidAmount,
      'status': status.index, // Now directly using the string value
      'notes': notes,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  InvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    DateTime? invoiceDate,
    List<InvoiceItem>? items,
    double? subtotal,
    double? discountAmount,
    double? discountPercentage,
    double? total,
    double? paidAmount,
    InvoiceStatus? status, // Changed from InvoiceStatus to String
    String? notes,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      total: total ?? this.total,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'InvoiceModel(\n'
        '  id: "$id",\n'
        '  invoiceNumber: "$invoiceNumber",\n'
        '  customerName: "$customerName",\n'
        '  customerPhone: "$customerPhone",\n'
        '  invoiceDate: ${invoiceDate.toIso8601String()},\n'
        '  itemsCount: ${items.length},\n'
        '  subtotal: $subtotal,\n'
        '  discountAmount: $discountAmount,\n'
        '  discountPercentage: $discountPercentage,\n'
        '  total: $total,\n'
        '  paidAmount: $paidAmount,\n'
        '  status: ${status.displayName},\n'
        '  createdAt: ${createdAt.toIso8601String()},\n'
        '  updatedAt: ${updatedAt.toIso8601String()},\n'
        ')';
  }
}
