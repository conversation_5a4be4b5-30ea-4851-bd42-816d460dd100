# Database Clear Functionality

## Overview
تم إضافة وظيفة حذف جميع البيانات من قاعدة البيانات لتسهيل عملية إعادة تعيين التطبيق وحذف البيانات الوهمية.

## الميزات المضافة

### 1. حذف جميع البيانات
- يحذف جميع المنتجات والعملاء والفواتير من قاعدة البيانات
- يحافظ على بيانات المستخدمين وبيانات الاعتماد المحفوظة
- لا يمكن التراجع عن هذا الإجراء

### 2. حذف المنتجات فقط
- يحذف جميع المنتجات من قاعدة البيانات
- يحافظ على العملاء والفواتير
- مفيد لحذف المنتجات الوهمية فقط

## كيفية الاستخدام

### من خلال شاشة الإعدادات
1. افتح التطبيق
2. اذهب إلى شاشة الإعدادات (Settings)
3. ابحث عن قسم "إدارة قاعدة البيانات"
4. اختر أحد الخيارات:
   - **حذف جميع البيانات**: لحذف كل شيء
   - **حذف المنتجات فقط**: لحذف المنتجات فقط

### من خلال الكود
```dart
// حذف جميع البيانات
final productService = ProductService();
await productService.clearAllData();

// حذف المنتجات فقط
await productService.clearAllProducts();
```

## الملفات المعدلة

### 1. `lib/services/database_service.dart`
- إضافة `clearAllData()` - حذف جميع البيانات
- إضافة `clearAllProducts()` - حذف المنتجات فقط
- إضافة `clearAllCustomers()` - حذف العملاء فقط
- إضافة `clearAllInvoices()` - حذف الفواتير فقط

### 2. `lib/services/product_service.dart`
- إضافة `clearAllData()` - واجهة لحذف جميع البيانات
- إضافة `clearAllProducts()` - واجهة لحذف المنتجات فقط

### 3. `lib/features/settings/screens/settings_screen.dart`
- إضافة قسم "إدارة قاعدة البيانات"
- إضافة أزرار حذف البيانات مع تأكيدات
- إضافة رسائل نجاح/خطأ

## الأمان والتحذيرات

### تحذيرات للمستخدم
- جميع عمليات الحذف تعرض تحذير قبل التنفيذ
- رسائل واضحة عن عدم إمكانية التراجع
- ألوان حمراء للدلالة على خطورة العملية

### حماية البيانات الأساسية
- لا يتم حذف بيانات المستخدمين
- لا يتم حذف بيانات الاعتماد المحفوظة
- حماية بيانات النظام الأساسية

## الاستخدام المقترح

### لحذف البيانات الوهمية
1. استخدم "حذف المنتجات فقط" إذا كنت تريد الاحتفاظ بالعملاء
2. استخدم "حذف جميع البيانات" إذا كنت تريد بداية جديدة تماماً

### للاختبار والتطوير
- مفيد لحذف البيانات التجريبية
- يساعد في اختبار وظائف إضافة البيانات الجديدة
- يسمح بإعادة تعيين سريعة للتطبيق

## ملاحظات تقنية

### المعاملات (Transactions)
- جميع عمليات الحذف تتم داخل معاملات لضمان الاتساق
- في حالة حدوث خطأ، يتم التراجع عن جميع التغييرات

### التحديث التلقائي
- بعد الحذف، يتم تحديث streams المنتجات تلقائياً
- واجهة المستخدم تتحدث فوراً لتعكس التغييرات

### معالجة الأخطاء
- جميع العمليات محمية بـ try-catch
- رسائل خطأ واضحة للمستخدم
- تسجيل الأخطاء للتشخيص
