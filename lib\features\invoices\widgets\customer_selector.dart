import 'package:flutter/material.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../constants/app_colors.dart';
import '../../../widgets/custom_text_field.dart';

class CustomerSelector extends StatefulWidget {
  const CustomerSelector({super.key});

  @override
  State<CustomerSelector> createState() => _CustomerSelectorState();
}

class _CustomerSelectorState extends State<CustomerSelector> {
  final CustomerService _customerService = CustomerService();
  final TextEditingController _searchController = TextEditingController();

  List<CustomerModel> _customers = [];
  List<CustomerModel> _filteredCustomers = [];
  List<String> _governorates = [];
  String? _selectedGovernorate;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _searchController.addListener(_filterCustomers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final customers = await _customerService.getAllCustomers();

      setState(() {
        _customers = customers;
        _filteredCustomers = customers;
        _isLoading = false;

        // استخراج قائمة المحافظات الفريدة
        _governorates =
            customers
                .where(
                  (customer) =>
                      customer.governorate != null &&
                      customer.governorate!.isNotEmpty,
                )
                .map((customer) => customer.governorate!)
                .toSet()
                .toList()
              ..sort(); // ترتيب المحافظات أبجدياً

        // إضافة خيار "جميع المحافظات" في البداية
        _governorates.insert(0, 'جميع المحافظات');
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل العملاء: $e';
        _isLoading = false;
      });
    }
  }

  void _filterCustomers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredCustomers = _customers.where((customer) {
        // تصفية حسب النص المدخل
        final matchesSearch =
            customer.name.toLowerCase().contains(query) ||
            (customer.phone1?.toLowerCase().contains(query) ?? false) ||
            (customer.phone2?.toLowerCase().contains(query) ?? false);

        // تصفية حسب المحافظة المختارة
        final matchesGovernorate =
            _selectedGovernorate == null ||
            _selectedGovernorate == 'جميع المحافظات' ||
            customer.governorate == _selectedGovernorate;

        return matchesSearch && matchesGovernorate;
      }).toList();
    });
  }

  void _onGovernorateChanged(String? governorate) {
    setState(() {
      _selectedGovernorate = governorate;
    });
    _filterCustomers();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final isTablet = screenWidth > 600;
    final isLandscape = mediaQuery.orientation == Orientation.landscape;
    
    // حساب الأحجام الديناميكية
    final dialogWidth = isTablet 
        ? screenWidth * 0.7 
        : screenWidth * 0.95;
    final dialogHeight = isLandscape 
        ? screenHeight * 0.9 
        : screenHeight * 0.85;
    final padding = isTablet ? 24.0 : 16.0;
    final titleFontSize = isTablet ? 24.0 : 20.0;
    final itemFontSize = isTablet ? 18.0 : 16.0;
    final iconSize = isTablet ? 24.0 : 20.0;

    return Dialog(
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        padding: EdgeInsets.all(padding),
        child: Column(
          children: [
            // العنوان
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary, size: iconSize),
                SizedBox(width: padding * 0.5),
                Text(
                  'اختيار العميل',
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, size: iconSize),
                  padding: EdgeInsets.all(padding * 0.25),
                ),
              ],
            ),

            SizedBox(height: padding),

            // قائمة منسدلة للمحافظات
            if (_governorates.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  horizontal: padding * 0.75,
                  vertical: padding * 0.5,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(padding * 0.5),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedGovernorate ?? 'جميع المحافظات',
                    isExpanded: true,
                    icon: Icon(Icons.arrow_drop_down, size: iconSize),
                    style: TextStyle(fontSize: itemFontSize, color: Colors.black87),
                    items: _governorates.map((String governorate) {
                      return DropdownMenuItem<String>(
                        value: governorate,
                        child: Row(
                          children: [
                            Icon(
                              governorate == 'جميع المحافظات'
                                  ? Icons.location_on
                                  : Icons.location_city,
                              color: governorate == 'جميع المحافظات'
                                  ? Colors.grey
                                  : AppColors.primary,
                              size: iconSize * 0.8,
                            ),
                            SizedBox(width: padding * 0.5),
                            Expanded(
                              child: Text(
                                governorate,
                                style: TextStyle(
                                  color: governorate == 'جميع المحافظات'
                                      ? Colors.grey
                                      : Colors.black87,
                                  fontWeight: governorate == 'جميع المحافظات'
                                      ? FontWeight.normal
                                      : FontWeight.w500,
                                  fontSize: itemFontSize,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: _onGovernorateChanged,
                  ),
                ),
              ),
              SizedBox(height: padding),
            ],

            // البحث
            CustomTextField(
              controller: _searchController,
              labelText: 'البحث في العملاء',
              hintText: 'اكتب اسم العميل أو رقم الهاتف',
              prefixIcon: Icons.search,
            ),

            SizedBox(height: padding),

            // معلومات التصفية
            if (_selectedGovernorate != null &&
                _selectedGovernorate != 'جميع المحافظات') ...[
              Container(
                padding: EdgeInsets.all(padding * 0.5),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(padding * 0.5),
                  border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.filter_list, color: AppColors.primary, size: iconSize * 0.8),
                    SizedBox(width: padding * 0.5),
                    Expanded(
                      child: Text(
                        'عرض العملاء من محافظة: $_selectedGovernorate',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                          fontSize: itemFontSize * 0.9,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    TextButton(
                      onPressed: () => _onGovernorateChanged('جميع المحافظات'),
                      child: Text(
                        'إلغاء التصفية',
                        style: TextStyle(fontSize: itemFontSize * 0.85),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: padding),
            ],

            // قائمة العملاء
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primary,
                      ),
                    )
                  : _errorMessage.isNotEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: isTablet ? 80.0 : 64.0,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: padding),
                          Text(
                            _errorMessage,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: itemFontSize,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: padding),
                          ElevatedButton(
                            onPressed: _loadCustomers,
                            child: Text(
                              'إعادة المحاولة',
                              style: TextStyle(fontSize: itemFontSize * 0.9),
                            ),
                          ),
                        ],
                      ),
                    )
                  : _filteredCustomers.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _selectedGovernorate != null &&
                                    _selectedGovernorate != 'جميع المحافظات'
                                ? Icons.location_off
                                : Icons.person_off,
                            size: isTablet ? 80.0 : 64.0,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: padding),
                          Text(
                            _searchController.text.isNotEmpty
                                ? 'لا توجد نتائج للبحث'
                                : _selectedGovernorate != null &&
                                      _selectedGovernorate != 'جميع المحافظات'
                                ? 'لا توجد عملاء في محافظة $_selectedGovernorate'
                                : 'لا توجد عملاء',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: itemFontSize,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredCustomers.length,
                      itemBuilder: (context, index) {
                        final customer = _filteredCustomers[index];
                        return _buildCustomerItem(customer, padding, itemFontSize, iconSize);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerItem(CustomerModel customer, double padding, double fontSize, double iconSize) {
    return Card(
      margin: EdgeInsets.only(bottom: padding * 0.5),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(
          horizontal: padding * 0.75,
          vertical: padding * 0.25,
        ),
        leading: CircleAvatar(
          radius: iconSize * 0.6,
          backgroundColor: AppColors.primary,
          child: Text(
            customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: fontSize * 0.9,
            ),
          ),
        ),
        title: Text(
          customer.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: fontSize,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (customer.phone1 != null) 
              Text(
                'الهاتف: ${customer.phone1}',
                style: TextStyle(fontSize: fontSize * 0.85),
                overflow: TextOverflow.ellipsis,
              ),
            if (customer.governorate?.isNotEmpty == true)
              Row(
                children: [
                  Icon(Icons.location_city, size: iconSize * 0.7, color: AppColors.primary),
                  SizedBox(width: padding * 0.25),
                  Expanded(
                    child: Text(
                      'المحافظة: ${customer.governorate}',
                      style: TextStyle(fontSize: fontSize * 0.85),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            if (customer.city?.isNotEmpty == true)
              Row(
                children: [
                  Icon(Icons.location_on, size: iconSize * 0.7, color: Colors.grey[600]),
                  SizedBox(width: padding * 0.25),
                  Expanded(
                    child: Text(
                      'المدينة: ${customer.city}',
                      style: TextStyle(fontSize: fontSize * 0.85),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
          ],
        ),
        trailing: Icon(Icons.arrow_forward_ios, size: iconSize * 0.7),
        onTap: () => Navigator.of(context).pop(customer),
      ),
    );
  }
}
