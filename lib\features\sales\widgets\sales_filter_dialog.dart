import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../models/invoice_model.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../utils/screen_utils.dart';

class SalesFilterDialog extends StatefulWidget {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? selectedCustomerId;
  final InvoiceStatus? selectedStatus;

  const SalesFilterDialog({
    super.key,
    this.startDate,
    this.endDate,
    this.selectedCustomerId,
    this.selectedStatus,
  });

  @override
  State<SalesFilterDialog> createState() => _SalesFilterDialogState();
}

class _SalesFilterDialogState extends State<SalesFilterDialog> {
  final CustomerService _customerService = CustomerService();

  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomerId;
  InvoiceStatus? _selectedStatus;
  List<CustomerModel> _customers = [];
  bool _isLoadingCustomers = true;

  @override
  void initState() {
    super.initState();
    _startDate = widget.startDate;
    _endDate = widget.endDate;
    _selectedCustomerId = widget.selectedCustomerId;
    _selectedStatus = widget.selectedStatus;
    _loadCustomers();
  }

  Future<void> _loadCustomers() async {
    try {
      final customers = await _customerService.getAllCustomers();
      if (mounted) {
        setState(() {
          _customers = customers;
          _isLoadingCustomers = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCustomers = false;
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? (_startDate ?? DateTime.now()) : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar', 'EG'),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // إذا كان تاريخ النهاية قبل تاريخ البداية، امسحه
          if (_endDate != null && _endDate!.isBefore(picked)) {
            _endDate = null;
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: isSmallScreen ? double.infinity : 400.w,
        padding: EdgeInsets.all(isSmallScreen ? 20.w : 24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'فلترة المبيعات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                    fontFamily: 'Cairo',
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  iconSize: 20.sp,
                ),
              ],
            ),
            SizedBox(height: 24.h),

            // فلتر التاريخ
            Text(
              'التاريخ',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: _DateFilterButton(
                    title: 'من تاريخ',
                    date: _startDate,
                    onTap: () => _selectDate(context, true),
                    isSmallScreen: isSmallScreen,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _DateFilterButton(
                    title: 'إلى تاريخ',
                    date: _endDate,
                    onTap: () => _selectDate(context, false),
                    isSmallScreen: isSmallScreen,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),

            // فلتر العميل
            Text(
              'العميل',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: DropdownButtonFormField<String>(
                value: _selectedCustomerId,
                decoration: InputDecoration(
                  hintText: 'اختر العميل',
                  hintStyle: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey.shade500,
                    fontFamily: 'Cairo',
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 12.h,
                  ),
                ),
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('جميع العملاء'),
                  ),
                  ..._customers.map((customer) => DropdownMenuItem<String>(
                    value: customer.id,
                    child: Text(
                      customer.name,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: 'Cairo',
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCustomerId = value;
                  });
                },
              ),
            ),
            SizedBox(height: 20.h),

            // فلتر الحالة
            Text(
              'حالة الدفع',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: DropdownButtonFormField<InvoiceStatus>(
                value: _selectedStatus,
                decoration: InputDecoration(
                  hintText: 'اختر الحالة',
                  hintStyle: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey.shade500,
                    fontFamily: 'Cairo',
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 12.h,
                  ),
                ),
                items: [
                  const DropdownMenuItem<InvoiceStatus>(
                    value: null,
                    child: Text('جميع الحالات'),
                  ),
                  ...InvoiceStatus.values.map((status) => DropdownMenuItem<InvoiceStatus>(
                    value: status,
                    child: Text(
                      status.displayName,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  )),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value;
                  });
                },
              ),
            ),
            SizedBox(height: 24.h),

            // أزرار التحكم
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _startDate = null;
                        _endDate = null;
                        _selectedCustomerId = null;
                        _selectedStatus = null;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      'مسح الفلاتر',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.red,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop({
                        'startDate': _startDate,
                        'endDate': _endDate,
                        'customerId': _selectedCustomerId,
                        'status': _selectedStatus,
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      'تطبيق الفلاتر',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _DateFilterButton extends StatelessWidget {
  final String title;
  final DateTime? date;
  final VoidCallback onTap;
  final bool isSmallScreen;

  const _DateFilterButton({
    required this.title,
    required this.date,
    required this.onTap,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 12.h,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 16.sp,
              color: Colors.grey.shade600,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                date != null 
                    ? DateFormat('dd/MM/yyyy').format(date!)
                    : title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: date != null ? Colors.black : Colors.grey.shade500,
                  fontFamily: 'Cairo',
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
