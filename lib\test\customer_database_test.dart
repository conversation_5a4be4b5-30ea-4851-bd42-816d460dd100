import 'package:flutter/material.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import '../../services/database_service.dart';

/// اختبار شامل لقاعدة بيانات العملاء
class CustomerDatabaseTest {
  final CustomerService _customerService = CustomerService();
  final DatabaseService _databaseService = DatabaseService();

  /// تشغيل جميع الاختبارات
  Future<void> runAllTests() async {
    debugPrint('=== بدء اختبارات قاعدة بيانات العملاء ===');

    try {
      // اختبار 1: إنشاء عميل جديد
      await _testCreateCustomer();

      // اختبار 2: جلب جميع العملاء
      await _testGetAllCustomers();

      // اختبار 3: البحث عن عميل
      await _testSearchCustomer();

      // اختبار 4: تحديث عميل
      await _testUpdateCustomer();

      // اختبار 5: حذف عميل (تعطيل)
      await _testDeleteCustomer();

      // اختبار 6: التحقق من صحة البيانات
      await _testDataValidation();

      // اختبار 7: اختبار الأداء
      await _testPerformance();

      debugPrint('=== جميع الاختبارات تمت بنجاح! ===');
    } catch (e) {
      debugPrint('=== فشل في الاختبارات: $e ===');
      rethrow;
    }
  }

  /// اختبار إنشاء عميل جديد
  Future<void> _testCreateCustomer() async {
    debugPrint('\n--- اختبار إنشاء عميل جديد ---');

    try {
      // إنشاء عميل تجريبي
      final testCustomer = CustomerModel(
        id: '',
        name: 'عميل تجريبي للاختبار',
        phone1: '**********',
        phone2: '**********',
        email: '<EMAIL>',
        type: CustomerType.medicalOfficeA,
        activity: 'عيادة طبية',
        governorate: 'القاهرة',
        city: 'مدينة نصر',
        street: 'شارع الاختبار',
        building: 'مبنى 123',
        floor: 'الدور الأول',
        apartment: 'شقة 5',
        notes: 'عميل تجريبي للاختبار',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('إنشاء عميل تجريبي: ${testCustomer.name}');

      // حفظ العميل
      final result = await _customerService.addCustomer(testCustomer);

      if (result) {
        debugPrint('✅ تم إنشاء العميل بنجاح');
      } else {
        throw Exception('فشل في إنشاء العميل');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار إنشاء العميل: $e');
      rethrow;
    }
  }

  /// اختبار جلب جميع العملاء
  Future<void> _testGetAllCustomers() async {
    debugPrint('\n--- اختبار جلب جميع العملاء ---');

    try {
      final customers = await _customerService.getAllCustomers();

      debugPrint('عدد العملاء: ${customers.length}');

      if (customers.isNotEmpty) {
        debugPrint('أسماء العملاء:');
        for (var customer in customers.take(5)) {
          debugPrint('- ${customer.name} (${customer.phone1})');
        }
        debugPrint('✅ تم جلب العملاء بنجاح');
      } else {
        debugPrint('⚠️ لا يوجد عملاء في قاعدة البيانات');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار جلب العملاء: $e');
      rethrow;
    }
  }

  /// اختبار البحث عن عميل
  Future<void> _testSearchCustomer() async {
    debugPrint('\n--- اختبار البحث عن عميل ---');

    try {
      // البحث بالاسم
      final customers = await _customerService.searchCustomers('تجريبي');

      if (customers.isNotEmpty) {
        final foundCustomer = customers.first;
        debugPrint('تم العثور على العميل: ${foundCustomer.name}');
        debugPrint('✅ تم البحث بنجاح');

        // اختبار جلب العميل بواسطة المعرف
        final customerById = await _customerService.getCustomerById(
          foundCustomer.id,
        );
        if (customerById != null) {
          debugPrint('✅ تم جلب العميل بواسطة المعرف');
        } else {
          throw Exception('فشل في جلب العميل بواسطة المعرف');
        }
      } else {
        debugPrint('⚠️ لم يتم العثور على عميل بالبحث');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار البحث: $e');
      rethrow;
    }
  }

  /// اختبار تحديث عميل
  Future<void> _testUpdateCustomer() async {
    debugPrint('\n--- اختبار تحديث عميل ---');

    try {
      // البحث عن عميل للتحديث
      final customers = await _customerService.searchCustomers('تجريبي');

      if (customers.isNotEmpty) {
        final customerToUpdate = customers.first;
        debugPrint('تحديث العميل: ${customerToUpdate.name}');

        // تحديث بيانات العميل
        final updatedCustomer = customerToUpdate.copyWith(
          notes: 'تم التحديث في ${DateTime.now()}',
          updatedAt: DateTime.now(),
        );

        // حفظ التحديث
        final result = await _customerService.updateCustomer(updatedCustomer);

        if (result) {
          debugPrint('✅ تم تحديث العميل بنجاح');

          // التحقق من التحديث
          final updatedCustomerFromDB = await _customerService.getCustomerById(
            updatedCustomer.id,
          );
          if (updatedCustomerFromDB != null &&
              updatedCustomerFromDB.notes == updatedCustomer.notes) {
            debugPrint('✅ تم التحقق من التحديث');
          } else {
            throw Exception('فشل في التحقق من التحديث');
          }
        } else {
          throw Exception('فشل في تحديث العميل');
        }
      } else {
        debugPrint('⚠️ لا يوجد عميل للتحديث');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار التحديث: $e');
      rethrow;
    }
  }

  /// اختبار حذف عميل (تعطيل)
  Future<void> _testDeleteCustomer() async {
    debugPrint('\n--- اختبار حذف عميل ---');

    try {
      // البحث عن عميل للحذف
      final customers = await _customerService.searchCustomers('تجريبي');

      if (customers.isNotEmpty) {
        final customerToDelete = customers.first;
        debugPrint('حذف العميل: ${customerToDelete.name}');

        // حذف العميل (تعطيل)
        final result = await _customerService.deleteCustomer(
          customerToDelete.id,
        );

        if (result) {
          debugPrint('✅ تم حذف العميل بنجاح');

          // التحقق من عدم ظهور العميل في القائمة النشطة
          final activeCustomers = await _customerService.getAllCustomers();
          final deletedCustomer = activeCustomers
              .where((c) => c.id == customerToDelete.id)
              .toList();

          if (deletedCustomer.isEmpty) {
            debugPrint('✅ تم التحقق من الحذف');
          } else {
            throw Exception('العميل لا يزال يظهر في القائمة النشطة');
          }
        } else {
          throw Exception('فشل في حذف العميل');
        }
      } else {
        debugPrint('⚠️ لا يوجد عميل للحذف');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار الحذف: $e');
      rethrow;
    }
  }

  /// اختبار التحقق من صحة البيانات
  Future<void> _testDataValidation() async {
    debugPrint('\n--- اختبار التحقق من صحة البيانات ---');

    try {
      // اختبار عميل بدون اسم
      try {
        final invalidCustomer = CustomerModel(
          id: '',
          name: '', // اسم فارغ
          phone1: '**********',
          governorate: 'القاهرة',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _customerService.addCustomer(invalidCustomer);
        throw Exception('كان يجب أن يفشل حفظ عميل بدون اسم');
      } catch (e) {
        if (e.toString().contains('اسم العميل مطلوب')) {
          debugPrint('✅ تم رفض عميل بدون اسم');
        } else {
          throw Exception('رسالة خطأ غير متوقعة: $e');
        }
      }

      // اختبار عميل بدون رقم هاتف
      try {
        final invalidCustomer = CustomerModel(
          id: '',
          name: 'عميل بدون هاتف',
          phone1: null, // رقم هاتف فارغ
          governorate: 'القاهرة',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _customerService.addCustomer(invalidCustomer);
        throw Exception('كان يجب أن يفشل حفظ عميل بدون رقم هاتف');
      } catch (e) {
        if (e.toString().contains('رقم الهاتف مطلوب')) {
          debugPrint('✅ تم رفض عميل بدون رقم هاتف');
        } else {
          throw Exception('رسالة خطأ غير متوقعة: $e');
        }
      }

      // اختبار عميل بدون محافظة
      try {
        final invalidCustomer = CustomerModel(
          id: '',
          name: 'عميل بدون محافظة',
          phone1: '**********',
          governorate: null, // محافظة فارغة
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _customerService.addCustomer(invalidCustomer);
        throw Exception('كان يجب أن يفشل حفظ عميل بدون محافظة');
      } catch (e) {
        if (e.toString().contains('المحافظة مطلوبة')) {
          debugPrint('✅ تم رفض عميل بدون محافظة');
        } else {
          throw Exception('رسالة خطأ غير متوقعة: $e');
        }
      }

      debugPrint('✅ جميع اختبارات التحقق من صحة البيانات نجحت');
    } catch (e) {
      debugPrint('❌ فشل في اختبار التحقق من صحة البيانات: $e');
      rethrow;
    }
  }

  /// اختبار الأداء
  Future<void> _testPerformance() async {
    debugPrint('\n--- اختبار الأداء ---');

    try {
      final stopwatch = Stopwatch()..start();

      // اختبار جلب العملاء
      final customers = await _customerService.getAllCustomers();
      stopwatch.stop();

      debugPrint(
        'وقت جلب ${customers.length} عميل: ${stopwatch.elapsedMilliseconds} مللي ثانية',
      );

      if (stopwatch.elapsedMilliseconds < 1000) {
        debugPrint('✅ الأداء مقبول');
      } else {
        debugPrint('⚠️ الأداء بطيء');
      }

      // اختبار البحث
      stopwatch.reset();
      stopwatch.start();

      await _customerService.searchCustomers('ا');
      stopwatch.stop();

      debugPrint('وقت البحث: ${stopwatch.elapsedMilliseconds} مللي ثانية');

      if (stopwatch.elapsedMilliseconds < 500) {
        debugPrint('✅ أداء البحث مقبول');
      } else {
        debugPrint('⚠️ أداء البحث بطيء');
      }
    } catch (e) {
      debugPrint('❌ فشل في اختبار الأداء: $e');
      rethrow;
    }
  }

  /// فحص قاعدة البيانات
  Future<void> checkDatabaseHealth() async {
    debugPrint('\n--- فحص صحة قاعدة البيانات ---');

    try {
      await _databaseService.checkAndRepairDatabase();
      debugPrint('✅ تم فحص قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ فشل في فحص قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// تنظيف البيانات التجريبية
  Future<void> cleanupTestData() async {
    debugPrint('\n--- تنظيف البيانات التجريبية ---');

    try {
      // البحث عن العملاء التجريبيين
      final testCustomers = await _customerService.searchCustomers('تجريبي');

      for (var customer in testCustomers) {
        await _customerService.deleteCustomer(customer.id);
        debugPrint('تم حذف العميل التجريبي: ${customer.name}');
      }

      debugPrint('✅ تم تنظيف البيانات التجريبية');
    } catch (e) {
      debugPrint('❌ فشل في تنظيف البيانات التجريبية: $e');
      rethrow;
    }
  }
}
