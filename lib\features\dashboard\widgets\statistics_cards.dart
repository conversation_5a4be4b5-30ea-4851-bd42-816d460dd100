import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';

class StatisticsCards extends StatelessWidget {
  const StatisticsCards({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final bool isNarrow = width < 380;
    final int columns = isNarrow ? 3 : 4;
    final double aspect = isNarrow ? 0.95 : 0.8; // ارتفاع أكبر على الضيق

    final items = <_StatCard>[
      const _StatCard(
        title: 'إجمالي المبيعات',
        value: '--',
        unit: 'ر.س',
        icon: Icons.trending_up,
        color: AppColors.success,
        percentage: '--',
        isPositive: true,
      ),
      const _StatCard(
        title: 'الفواتير المعلقة',
        value: '--',
        unit: 'ر.س',
        icon: Icons.pending_actions,
        color: AppColors.warning,
        percentage: '--',
        isPositive: false,
      ),
      const _StatCard(
        title: 'مبالغ التحصيل',
        value: '--',
        unit: 'ر.س',
        icon: Icons.payment,
        color: Color(0xFF9C27B0), // Purple
        percentage: '--',
        isPositive: false,
      ),
      const _StatCard(
        title: 'التحصيلات المنجزة',
        value: '--',
        unit: 'ر.س',
        icon: Icons.check_circle,
        color: Color(0xFF4CAF50), // Green
        percentage: '--',
        isPositive: true,
      ),
      const _StatCard(
        title: 'العملاء النشطين',
        value: '--',
        unit: 'عميل',
        icon: Icons.people,
        color: AppColors.customers,
        percentage: '--',
        isPositive: true,
      ),
      const _StatCard(
        title: 'المنتجات',
        value: '--',
        unit: 'منتج',
        icon: Icons.inventory_2,
        color: AppColors.products,
        percentage: '--',
        isPositive: true,
      ),
      const _StatCard(
        title: 'المرتجعات',
        value: '--',
        unit: 'مرتجع',
        icon: Icons.keyboard_return,
        color: AppColors.returns,
        percentage: '--',
        isPositive: true,
      ),
      const _StatCard(
        title: 'نقص المخزون',
        value: '--',
        unit: 'منتج',
        icon: Icons.warning,
        color: AppColors.error,
        percentage: '--',
        isPositive: false,
      ),
    ];

    return GridView.count(
      crossAxisCount: columns,
      mainAxisSpacing: 8.h,
      crossAxisSpacing: 8.w,
      childAspectRatio: aspect,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      children: items,
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String unit;
  final IconData icon;
  final Color color;
  final String percentage;
  final bool isPositive;

  const _StatCard({
    required this.title,
    required this.value,
    required this.unit,
    required this.icon,
    required this.color,
    required this.percentage,
    required this.isPositive,
  });

  @override
  Widget build(BuildContext context) {
    final double iconSize = (16.sp).ceilToDouble();

    return LayoutBuilder(
      builder: (context, constraints) {
        final bool isTinyWidth = constraints.maxWidth < 80;
        return Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(5.w),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(icon, color: color, size: iconSize),
                  ),
                  const Spacer(),
                  if (!isTinyWidth)
                    Flexible(
                      fit: FlexFit.loose,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: isPositive
                                ? AppColors.success.withValues(alpha: 0.1)
                                : AppColors.error.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isPositive
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward,
                                color: isPositive
                                    ? AppColors.success
                                    : AppColors.error,
                                size: (8.5.sp).ceilToDouble(),
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                percentage,
                                softWrap: false,
                                style: TextStyle(
                                  fontSize: 8.5.sp,
                                  color: isPositive
                                      ? AppColors.success
                                      : AppColors.error,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(height: 6.h),
              Text(
                title,
                style: TextStyle(
                  fontSize: 9.5.sp,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: TextStyle(
                        fontSize: 14.5.sp,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Text(
                    unit,
                    style: TextStyle(
                      fontSize: 8.5.sp,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
