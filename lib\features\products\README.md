# صفحة قائمة المنتجات - Products List Screen

## نظرة عامة
صفحة قائمة المنتجات هي واجهة عصريّة واحترافيّة لإدارة جميع المنتجات في النظام. تم تصميمها بدعم كامل للغة العربية (RTL) وتوفير تجربة مستخدم سلسة وسريعة.

## الميزات الرئيسية

### 1. عرض المنتجات
- **بطاقات المنتجات**: كل منتج معروض في بطاقة (Card) أنيقة تحتوي على:
  - اسم المنتج والفئة
  - كود المنتج والباركود
  - سعر البيع وسعر الشراء
  - الكمية المتوفرة والحد الأدنى
  - حالة المخزون (متوفر، منخفض، نفذ)
  - هامش الربح
  - أيقونات مميزة لكل فئة

### 2. البحث والتصفية
- **بحث فوري**: البحث بالاسم، الكود، أو الباركود
- **تصفية حسب الفئة**: أجهزة طبية، مستهلكات، معقمات، مستلزمات معمل، مستلزمات عامة
- **تصفية المخزون**: عرض المنتجات ذات المخزون المنخفض فقط
- **خيارات الترتيب**: حسب الاسم، الكود، السعر، الكمية، الفئة
- **اتجاه الترتيب**: تصاعدي أو تنازلي

### 3. الإجراءات السريعة
- **إضافة منتج جديد**: زر + مع نافذة شاملة لإدخال البيانات
- **تعديل المنتج**: تعديل جميع بيانات المنتج
- **حذف المنتج**: مع تأكيد الحذف
- **بيع المنتج**: إنشاء فاتورة بيع سريعة
- **تعديل الكمية**: تعديل كمية المخزون
- **عرض التفاصيل**: نافذة تفصيلية شاملة

### 4. الإحصائيات السريعة
- إجمالي عدد المنتجات
- عدد المنتجات ذات المخزون المنخفض
- عدد المنتجات التي نفد مخزونها

## الملفات المكونة

### 1. الشاشة الرئيسية
- `products_list_screen.dart` - الشاشة الرئيسية لقائمة المنتجات

### 2. الـ Widgets
- `product_card.dart` - بطاقة عرض المنتج
- `product_search_bar.dart` - شريط البحث والتصفية
- `add_product_dialog.dart` - نافذة إضافة منتج جديد
- `product_actions_dialog.dart` - نافذة الإجراءات السريعة

## كيفية الاستخدام

### عرض قائمة المنتجات
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const ProductsListScreen(),
  ),
);
```

### البحث عن منتج
1. اكتب في حقل البحث (الاسم، الكود، أو الباركود)
2. النتائج تظهر فورياً أثناء الكتابة

### تصفية المنتجات
1. اضغط على أيقونة التصفية
2. اختر الفئة المطلوبة
3. حدد خيارات الترتيب
4. اختر اتجاه الترتيب

### إضافة منتج جديد
1. اضغط على زر + (Floating Action Button)
2. املأ البيانات المطلوبة
3. اضغط "حفظ"

### إجراءات سريعة على المنتج
1. اضغط على أيقونة الإجراءات في بطاقة المنتج
2. اختر الإجراء المطلوب:
   - تفاصيل المنتج
   - تعديل المنتج
   - بيع المنتج
   - تعديل الكمية
   - حذف المنتج

## التصميم والواجهة

### الألوان المستخدمة
- **الألوان الأساسية**: أزرق فاتح مع تدرجات
- **ألوان الحالة**: أخضر (نجاح)، برتقالي (تحذير)، أحمر (خطأ)
- **ألوان النص**: أسود للعناوين، رمادي للنصوص الثانوية

### دعم اللغة العربية
- **اتجاه النص**: من اليمين إلى اليسار (RTL)
- **النصوص**: جميع النصوص باللغة العربية
- **الأيقونات**: أيقونات معبرة ومناسبة للسياق

### التجاوب
- **الأجهزة المحمولة**: تصميم متجاوب لجميع أحجام الشاشات
- **التبديل**: دعم التبديل بين الوضع العادي والمظلم
- **الأداء**: تحميل سريع وحركات سلسة

## الميزات المتقدمة

### 1. إدارة المخزون
- تتبع الكميات المتوفرة
- تنبيهات المخزون المنخفض
- تحديث الكميات بسهولة

### 2. إدارة الأسعار
- أسعار مختلفة لأنواع العملاء
- حساب هامش الربح تلقائياً
- دعم العملة المحلية

### 3. التصنيف والتنظيم
- تصنيف المنتجات حسب الفئة
- رموز وأيقونات مميزة لكل فئة
- إمكانية إضافة فئات جديدة

## التطوير المستقبلي

### الميزات المخطط لها
- [ ] استيراد/تصدير المنتجات
- [ ] إدارة الصور للمنتجات
- [ ] ربط مع الموردين
- [ ] تقارير المخزون المتقدمة
- [ ] دعم الباركود (QR Code)
- [ ] إشعارات المخزون

### التحسينات المقترحة
- [ ] بحث صوتي
- [ ] تصفية متقدمة بالسعر
- [ ] حفظ تفضيلات المستخدم
- [ ] مزامنة مع السحابة
- [ ] دعم الطباعة

## الدعم والمساعدة

### استكشاف الأخطاء
- تأكد من وجود بيانات في قاعدة البيانات
- تحقق من صحة الاتصال بقاعدة البيانات
- راجع سجلات الأخطاء في Console

### الأسئلة الشائعة
**س: كيف أضيف منتج جديد؟**
ج: اضغط على زر + في أسفل الشاشة واملأ البيانات المطلوبة.

**س: كيف أبحث عن منتج معين؟**
ج: استخدم حقل البحث في أعلى الشاشة واكتب اسم أو كود المنتج.

**س: كيف أعدل كمية منتج؟**
ج: اضغط على أيقونة "كمية" في بطاقة المنتج وأدخل الكمية الجديدة.

## المساهمة في التطوير

### إرشادات الكود
- استخدم أسماء متغيرات واضحة باللغة الإنجليزية
- اكتب تعليقات باللغة العربية للوظائف المعقدة
- اتبع معايير Flutter للتصميم
- اختبر جميع الميزات قبل الإرسال

### هيكل المشروع
```
lib/features/products/
├── screens/
│   └── products_list_screen.dart
├── widgets/
│   ├── product_card.dart
│   ├── product_search_bar.dart
│   ├── add_product_dialog.dart
│   └── product_actions_dialog.dart
└── README.md
```

---

**تم تطوير هذه الصفحة بواسطة فريق Atlas Medical Supplies**
**آخر تحديث**: ديسمبر 2024
