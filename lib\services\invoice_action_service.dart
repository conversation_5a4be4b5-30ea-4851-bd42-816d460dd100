import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';
import '../models/invoice_action_model.dart';
import '../models/invoice_model.dart';
import '../models/customer_model.dart';
import 'database_service.dart';
import 'customer_service.dart';

class InvoiceActionService {
  static final InvoiceActionService _instance =
      InvoiceActionService._internal();
  factory InvoiceActionService() => _instance;
  InvoiceActionService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final CustomerService _customerService = CustomerService();

  /// تسجيل إجراء دفع على فاتورة
  Future<InvoiceActionModel> recordPayment({
    required String invoiceId,
    required double amount,
    String? notes,
    String? createdBy,
  }) async {
    try {
      // الحصول على بيانات الفاتورة
      final invoice = await _databaseService.getInvoice(invoiceId);
      if (invoice == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      // الحصول على بيانات العميل
      final customer = await _customerService.getCustomerById(
        invoice.customerId,
      );
      if (customer == null) {
        throw Exception('بيانات العميل غير موجودة');
      }

      // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
      final remainingAmount = invoice.calculateRemainingAmount();
      if (amount > remainingAmount) {
        throw Exception(
          'مبلغ الدفع يتجاوز المبلغ المتبقي (${remainingAmount.toStringAsFixed(2)} ر.س)',
        );
      }

      // إنشاء إجراء الدفع
      final action = InvoiceActionModel(
        id: const Uuid().v4(),
        invoiceId: invoiceId,
        invoiceNumber: invoice.invoiceNumber,
        customerId: invoice.customerId,
        customerName: invoice.customerName,
        governorate: customer.governorate ?? '',
        actionType: InvoiceActionType.payment,
        amount: amount,
        actionDate: DateTime.now(),
        notes: notes,
        createdBy: createdBy,
      );

      // حفظ الإجراء في قاعدة البيانات
      await _databaseService.createInvoiceAction(action);

      // تحديث المبلغ المدفوع في الفاتورة
      final newPaidAmount = invoice.paidAmount + amount;
      await _databaseService.updateInvoicePaidAmount(invoiceId, newPaidAmount);

      // تحديث حالة الفاتورة إذا تم الدفع بالكامل
      if (newPaidAmount >= invoice.total) {
        await _databaseService.updateInvoiceStatus(
          invoiceId,
          InvoiceStatus.paid,
        );
      }

      debugPrint(
        'تم تسجيل دفع بقيمة ${amount.toStringAsFixed(2)} ر.س للفاتورة ${invoice.invoiceNumber}',
      );
      return action;
    } catch (e) {
      debugPrint('خطأ في تسجيل الدفع: $e');
      rethrow;
    }
  }

  /// الحصول على جميع إجراءات الفواتير
  Future<List<InvoiceActionModel>> getAllInvoiceActions() async {
    try {
      return await _databaseService.getAllInvoiceActions();
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات الفواتير: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فاتورة معينة
  Future<List<InvoiceActionModel>> getInvoiceActions(String invoiceId) async {
    try {
      return await _databaseService.getInvoiceActions(invoiceId);
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات الفاتورة: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فواتير محافظة معينة
  Future<List<InvoiceActionModel>> getGovernorateInvoiceActions(
    String governorate,
  ) async {
    try {
      final allActions = await getAllInvoiceActions();
      return allActions
          .where((action) => action.governorate == governorate)
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات فواتير المحافظة: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فواتير عميل معين
  Future<List<InvoiceActionModel>> getCustomerInvoiceActions(
    String customerId,
  ) async {
    try {
      final allActions = await getAllInvoiceActions();
      return allActions
          .where((action) => action.customerId == customerId)
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات فواتير العميل: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فواتير في فترة زمنية
  Future<List<InvoiceActionModel>> getInvoiceActionsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final allActions = await getAllInvoiceActions();
      return allActions.where((action) {
        return action.actionDate.isAfter(
              startDate.subtract(const Duration(days: 1)),
            ) &&
            action.actionDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات الفواتير حسب التاريخ: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فواتير اليوم
  Future<List<InvoiceActionModel>> getTodayInvoiceActions() async {
    try {
      final today = DateTime.now();
      final startDate = DateTime(today.year, today.month, today.day);
      final endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);

      return await getInvoiceActionsByDateRange(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات فواتير اليوم: $e');
      return [];
    }
  }

  /// الحصول على إجراءات فواتير الشهر الحالي
  Future<List<InvoiceActionModel>> getCurrentMonthInvoiceActions() async {
    try {
      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month, 1);
      final endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      return await getInvoiceActionsByDateRange(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      debugPrint('خطأ في جلب إجراءات فواتير الشهر الحالي: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات إجراءات الفواتير
  Future<Map<String, dynamic>> getInvoiceActionsStatistics() async {
    try {
      final allActions = await getAllInvoiceActions();

      double totalPayments = 0.0;
      int paymentsCount = 0;

      for (final action in allActions) {
        if (action.actionType == InvoiceActionType.payment) {
          totalPayments += action.amount;
          paymentsCount++;
        }
      }

      return {
        'totalPayments': totalPayments,
        'paymentsCount': paymentsCount,
        'totalActions': allActions.length,
        'totalAmount': totalPayments,
      };
    } catch (e) {
      debugPrint('خطأ في حساب إحصائيات إجراءات الفواتير: $e');
      return {
        'totalPayments': 0.0,
        'paymentsCount': 0,
        'totalActions': 0,
        'totalAmount': 0.0,
      };
    }
  }

  /// الحصول على إحصائيات إجراءات فواتير محافظة معينة
  Future<Map<String, dynamic>> getGovernorateInvoiceActionsStatistics(
    String governorate,
  ) async {
    try {
      final actions = await getGovernorateInvoiceActions(governorate);

      double totalPayments = 0.0;
      int paymentsCount = 0;

      for (final action in actions) {
        if (action.actionType == InvoiceActionType.payment) {
          totalPayments += action.amount;
          paymentsCount++;
        }
      }

      return {
        'governorate': governorate,
        'totalPayments': totalPayments,
        'paymentsCount': paymentsCount,
        'totalActions': actions.length,
        'totalAmount': totalPayments,
      };
    } catch (e) {
      debugPrint('خطأ في حساب إحصائيات إجراءات فواتير المحافظة: $e');
      return {
        'governorate': governorate,
        'totalPayments': 0.0,
        'paymentsCount': 0,
        'totalActions': 0,
        'totalAmount': 0.0,
      };
    }
  }

  /// حذف إجراء فاتورة
  Future<void> deleteInvoiceAction(String actionId) async {
    try {
      await _databaseService.deleteInvoiceAction(actionId);
      debugPrint('تم حذف إجراء الفاتورة بنجاح');
    } catch (e) {
      debugPrint('خطأ في حذف إجراء الفاتورة: $e');
      rethrow;
    }
  }
}
