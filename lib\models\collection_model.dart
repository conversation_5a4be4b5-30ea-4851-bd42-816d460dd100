import 'dart:convert';

class CollectionModel {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime invoiceDate;
  final double invoiceTotal;
  final double previousPaidAmount;
  final double
  paymentAmount; // تم تغيير الاسم من collectionAmount إلى paymentAmount
  final double remainingAmount;
  final DateTime
  paymentDate; // تم تغيير الاسم من collectionDate إلى paymentDate
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;

  const CollectionModel({
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.invoiceDate,
    required this.invoiceTotal,
    required this.previousPaidAmount,
    required this.paymentAmount, // تم تغيير الاسم
    required this.remainingAmount,
    required this.paymentDate, // تم تغيير الاسم
    this.notes,
    this.createdBy,
    required this.createdAt,
  });

  factory CollectionModel.fromJson(Map<String, dynamic> json) {
    return CollectionModel(
      id: json['id'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        json['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      invoiceTotal: (json['invoiceTotal'] ?? 0.0).toDouble(),
      previousPaidAmount: (json['previousPaidAmount'] ?? 0.0).toDouble(),
      paymentAmount: (json['paymentAmount'] ?? json['collectionAmount'] ?? 0.0)
          .toDouble(), // دعم الاسم القديم والجديد
      remainingAmount: (json['remainingAmount'] ?? 0.0).toDouble(),
      paymentDate: DateTime.parse(
        json['paymentDate'] ??
            json['collectionDate'] ??
            DateTime.now().toIso8601String(), // دعم الاسم القديم والجديد
      ),
      notes: json['notes'],
      createdBy: json['createdBy'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  factory CollectionModel.fromMap(Map<String, dynamic> map) {
    return CollectionModel(
      id: map['id'] ?? '',
      invoiceId: map['invoiceId'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        map['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      invoiceTotal: (map['invoiceTotal'] ?? 0.0).toDouble(),
      previousPaidAmount: (map['previousPaidAmount'] ?? 0.0).toDouble(),
      paymentAmount: (map['paymentAmount'] ?? map['collectionAmount'] ?? 0.0)
          .toDouble(), // دعم الاسم القديم والجديد
      remainingAmount: (map['remainingAmount'] ?? 0.0).toDouble(),
      paymentDate: DateTime.parse(
        map['paymentDate'] ??
            map['collectionDate'] ??
            DateTime.now().toIso8601String(), // دعم الاسم القديم والجديد
      ),
      notes: map['notes'],
      createdBy: map['createdBy'],
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'invoiceTotal': invoiceTotal,
      'previousPaidAmount': previousPaidAmount,
      'paymentAmount': paymentAmount, // تم تغيير الاسم
      'remainingAmount': remainingAmount,
      'paymentDate': paymentDate.toIso8601String(), // تم تغيير الاسم
      'notes': notes,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'invoiceTotal': invoiceTotal,
      'previousPaidAmount': previousPaidAmount,
      'paymentAmount': paymentAmount, // تم تغيير الاسم
      'remainingAmount': remainingAmount,
      'paymentDate': paymentDate.toIso8601String(), // تم تغيير الاسم
      'notes': notes,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  CollectionModel copyWith({
    String? id,
    String? invoiceId,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    DateTime? invoiceDate,
    double? invoiceTotal,
    double? previousPaidAmount,
    double? paymentAmount, // تم تغيير الاسم
    double? remainingAmount,
    DateTime? paymentDate, // تم تغيير الاسم
    String? notes,
    String? createdBy,
    DateTime? createdAt,
  }) {
    return CollectionModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      invoiceTotal: invoiceTotal ?? this.invoiceTotal,
      previousPaidAmount: previousPaidAmount ?? this.previousPaidAmount,
      paymentAmount: paymentAmount ?? this.paymentAmount, // تم تغيير الاسم
      remainingAmount: remainingAmount ?? this.remainingAmount,
      paymentDate: paymentDate ?? this.paymentDate, // تم تغيير الاسم
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // الحصول على تاريخ الفاتورة المنسق
  String get formattedInvoiceDate {
    return '${invoiceDate.day}/${invoiceDate.month}/${invoiceDate.year}';
  }

  // الحصول على تاريخ الدفع المنسق
  String get formattedPaymentDate {
    return '${paymentDate.day}/${paymentDate.month}/${paymentDate.year}';
  }

  // الحصول على وقت الدفع المنسق
  String get formattedPaymentTime {
    final hour = paymentDate.hour;
    final minute = paymentDate.minute;
    final period = hour >= 12 ? 'م' : 'ص';
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  // Getters للتوافق مع الكود القديم
  double get collectionAmount => paymentAmount;
  DateTime get collectionDate => paymentDate;
  String get formattedCollectionDate => formattedPaymentDate;
  String get formattedCollectionTime => formattedPaymentTime;

  // الحصول على نص الإشعار للعميل - تم تحديثه ليركز على الدفع
  String get notificationMessage {
    final companyName = 'أطلس للمستلزمات الطبية';
    final invoiceDateTime =
        '${formattedInvoiceDate} ${_formatTime(invoiceDate)}';
    final paymentDateTime = '${formattedPaymentDate} ${formattedPaymentTime}';

    return '''$companyName

مرحباً ${customerName}

تم استلام دفعة بقيمة ${paymentAmount.toStringAsFixed(2)} ر.س من الفاتورة رقم $invoiceNumber

تفاصيل الفاتورة:
- تاريخ الفاتورة: $invoiceDateTime
- إجمالي الفاتورة: ${invoiceTotal.toStringAsFixed(2)} ر.س
- المبلغ المدفوع سابقاً: ${previousPaidAmount.toStringAsFixed(2)} ر.س
- مبلغ الدفعة: ${paymentAmount.toStringAsFixed(2)} ر.س
- المتبقي: ${remainingAmount.toStringAsFixed(2)} ر.س

تاريخ الدفع: $paymentDateTime

شكراً لثقتكم بنا''';
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'م' : 'ص';
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  @override
  String toString() {
    return 'CollectionModel(\n'
        '  id: "$id",\n'
        '  invoiceNumber: "$invoiceNumber",\n'
        '  customerName: "$customerName",\n'
        '  invoiceTotal: $invoiceTotal,\n'
        '  paymentAmount: $paymentAmount,\n'
        '  remainingAmount: $remainingAmount,\n'
        '  paymentDate: ${paymentDate.toIso8601String()},\n'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CollectionModel &&
        other.id == id &&
        other.invoiceId == invoiceId &&
        other.paymentDate == paymentDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^ invoiceId.hashCode ^ paymentDate.hashCode;
  }
}
