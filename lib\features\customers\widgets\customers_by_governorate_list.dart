import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import 'customer_card.dart';
import '../screens/customer_details_screen.dart';
import '../screens/governorate_customers_screen.dart';

class CustomersByGovernorateList extends StatefulWidget {
  final String searchQuery;
  final bool isSelectionMode;

  const CustomersByGovernorateList({
    super.key, 
    required this.searchQuery,
    this.isSelectionMode = false,
  });

  @override
  State<CustomersByGovernorateList> createState() =>
      _CustomersByGovernorateListState();
}

class _CustomersByGovernorateListState
    extends State<CustomersByGovernorateList> {
  final CustomerService _customerService = CustomerService();

  // إحصائيات إضافية
  int _totalCustomers = 0;
  int _totalGovernorates = 0;

  /// جلب العميل المحدث من قاعدة البيانات
  Future<CustomerModel?> _getUpdatedCustomer(String customerId) async {
    try {
      return await _customerService.getCustomerById(customerId);
    } catch (e) {
      debugPrint('خطأ في جلب العميل المحدث: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, List<CustomerModel>>>(
      stream: _customerService.getCustomersByGovernorate(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
                SizedBox(height: 16.h),
                Text(
                  'حدث خطأ في تحميل البيانات',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.error,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                ElevatedButton(
                  onPressed: () {
                    setState(() {});
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                SizedBox(height: 16.h),
                Text(
                  'جاري تحميل العملاء...',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          );
        }

        final customersByGovernorate = snapshot.data ?? {};

        // تحديث الإحصائيات
        _totalCustomers = customersByGovernorate.values.fold(
          0,
          (sum, customers) => sum + customers.length,
        );
        _totalGovernorates = customersByGovernorate.length;

        if (customersByGovernorate.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 24.h),
                Text(
                  'لا يوجد عملاء حتى الآن',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'ابدأ بإضافة عميل جديد',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // تطبيق البحث
        final filteredCustomersByGovernorate = <String, List<CustomerModel>>{};

        for (final entry in customersByGovernorate.entries) {
          final governorate = entry.key;
          final customers = entry.value;

          if (widget.searchQuery.isEmpty) {
            filteredCustomersByGovernorate[governorate] = customers;
          } else {
            final filteredCustomers = customers.where((customer) {
              return customer.name.toLowerCase().contains(
                    widget.searchQuery.toLowerCase(),
                  ) ||
                  customer.activity.toLowerCase().contains(
                    widget.searchQuery.toLowerCase(),
                  ) ||
                  (customer.phone1?.contains(widget.searchQuery) ?? false) ||
                  (customer.phone2?.contains(widget.searchQuery) ?? false);
            }).toList();

            if (filteredCustomers.isNotEmpty) {
              filteredCustomersByGovernorate[governorate] = filteredCustomers;
            }
          }
        }

        if (filteredCustomersByGovernorate.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 24.h),
                Text(
                  'لا توجد نتائج للبحث',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'جرب تغيير كلمات البحث',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // قائمة المحافظات
            SizedBox(height: 16.h), // إضافة مسافة إضافية فوق قائمة المحافظات
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: filteredCustomersByGovernorate.length,
                itemBuilder: (context, index) {
                  final governorate = filteredCustomersByGovernorate.keys
                      .elementAt(index);
                  final customers =
                      filteredCustomersByGovernorate[governorate]!;

                  return Card(
                    margin: EdgeInsets.only(bottom: 16.h),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: InkWell(
                      onTap: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => GovernorateCustomersScreen(
                              governorateName: governorate,
                              customers: customers,
                              isSelectionMode: widget.isSelectionMode,
                            ),
                          ),
                        );

                        // التعامل مع النتيجة الجديدة
                        if (result != null) {
                          if (result is CustomerModel) {
                            // تم اختيار عميل، أرسل النتيجة للشاشة الأم
                            Navigator.pop(context, result);
                          } else if (result is Map) {
                            if (result['deleted'] == true) {
                              // إزالة العميل من القائمة فوراً
                              setState(() {
                                customers.removeWhere(
                                  (c) => c.id == result['customerId'],
                                );
                              });

                              // عرض رسالة نجاح
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'تم حذف العميل بنجاح',
                                      style: TextStyle(fontFamily: 'Cairo'),
                                    ),
                                    backgroundColor: AppColors.success,
                                  ),
                                );
                              }
                            } else if (result['updated'] == true) {
                              // تحديث العميل في القائمة
                              final customerId = result['customerId'];
                              final updatedCustomer = await _getUpdatedCustomer(customerId);
                              
                              if (updatedCustomer != null) {
                                setState(() {
                                  final index = customers.indexWhere((c) => c.id == customerId);
                                  if (index != -1) {
                                    customers[index] = updatedCustomer;
                                  }
                                });
                              }

                              // عرض رسالة نجاح
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'تم تحديث العميل بنجاح',
                                      style: TextStyle(fontFamily: 'Cairo'),
                                    ),
                                    backgroundColor: AppColors.success,
                                  ),
                                );
                              }
                            }
                          }
                        }
                      },
                      borderRadius: BorderRadius.circular(12.r),
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25.r),
                          border: Border.all(
                            color: AppColors.primary,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.folder,
                              color: AppColors.primary,
                              size: 24.sp,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    governorate,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  Text(
                                    'اضغط لعرض العملاء',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.textSecondary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                '${customers.length}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Cairo',
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: AppColors.primary,
                              size: 16.sp,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  // شريط الإحصائيات
  Widget _buildStatisticsBar() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.h,
      ), // زيادة المسافة العمودية
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.people,
              label: 'إجمالي العملاء',
              value: '$_totalCustomers',
            ),
          ),
          Container(
            width: 1,
            height: 40.h,
            color: AppColors.primary.withValues(alpha: 0.2),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.location_on,
              label: 'عدد المحافظات',
              value: '$_totalGovernorates',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primary, size: 24.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
            fontFamily: 'Cairo',
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
            fontFamily: 'Cairo',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // عرض العملاء في محافظة محددة
  void _showCustomersInGovernorate(
    BuildContext context,
    String governorateName,
    List<CustomerModel> customers,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: Column(
          children: [
            // شريط العنوان
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.location_on, color: Colors.white, size: 24.sp),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      'عملاء $governorateName',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            // قائمة العملاء
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  final customer = customers[index];
                  return Card(
                    margin: EdgeInsets.only(bottom: 12.h),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppColors.primary.withValues(
                          alpha: 0.1,
                        ),
                        child: Icon(Icons.person, color: AppColors.primary),
                      ),
                      title: Text(
                        customer.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (customer.phone1 != null)
                            Text('${customer.phone1}'),
                          if (customer.activity != null)
                            Text('${customer.activity}'),
                        ],
                      ),
                      onTap: () async {
                        Navigator.pop(context);
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                CustomerDetailsScreen(customer: customer),
                          ),
                        );

                        // إذا تم حذف العميل، قم بتحديث القائمة
                        if (result != null && result is Map) {
                          if (result['deleted'] == true) {
                            // إزالة العميل من القائمة فوراً
                            setState(() {
                              customers.removeWhere(
                                (c) => c.id == result['customerId'],
                              );
                            });

                            // عرض رسالة نجاح
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'تم حذف العميل بنجاح',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                  backgroundColor: AppColors.success,
                                ),
                              );
                            }
                          }
                        }
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
