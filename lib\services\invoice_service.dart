import 'dart:async';
import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../models/invoice_model.dart';
import '../models/customer_model.dart';
import '../models/product_model.dart';
import '../models/collection_model.dart';

import 'database_service.dart';
import 'customer_service.dart';
import 'sales_service.dart';
import 'collection_service.dart';
import '../constants/app_strings.dart';

class InvoiceService {
  static final InvoiceService _instance = InvoiceService._internal();
  factory InvoiceService() => _instance;
  InvoiceService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// إنشاء فاتورة جديدة
  Future<InvoiceModel> createInvoice({
    required CustomerModel customer,
    required List<InvoiceItem> items,
    required DateTime invoiceDate,
    double discountAmount = 0.0,
    double discountPercentage = 0.0,
    double paidAmount = 0.0, // إضافة معلمة المبلغ المدفوع
    String? notes,
    String? createdBy,
  }) async {
    // التحقق من أن تاريخ الفاتورة لا يسبق تاريخ اليوم الحالي
    final today = DateTime.now();
    final invoiceDateOnly = DateTime(
      invoiceDate.year,
      invoiceDate.month,
      invoiceDate.day,
    );
    final todayOnly = DateTime(today.year, today.month, today.day);

    if (invoiceDateOnly.isBefore(todayOnly)) {
      throw Exception('لا يمكن إنشاء فاتورة بتاريخ سابق لليوم الحالي');
    }

    // التحقق من الكميات المتوفرة في المخزون
    final List<String> insufficientStockItems = [];

    for (final item in items) {
      try {} catch (e) {
        debugPrint('خطأ في التحقق من مخزون المنتج ${item.productName}: $e');
      }
    }

    if (insufficientStockItems.isNotEmpty) {
      final errorMessage =
          'الكميات المطلوبة تتجاوز المخزون المتوفر:\n${insufficientStockItems.join('\n')}';
      throw Exception(errorMessage);
    }

    // الحصول على رقم الفاتورة التالي
    final invoiceNumber = await _databaseService.getNextInvoiceNumber();

    // حساب الإجماليات
    final subtotal = items.fold(0.0, (sum, item) => sum + item.total);

    // حساب قيمة الخصم
    double discountValue = 0.0;
    if (discountPercentage > 0) {
      discountValue = subtotal * (discountPercentage / 100);
    } else {
      discountValue = discountAmount;
    }

    final total = subtotal - discountValue; // الإجمالي بعد الخصم

    final invoice = InvoiceModel(
      id: '',
      invoiceNumber: invoiceNumber,
      customerId: customer.id,
      customerName: customer.name,
      customerPhone: customer.phone1 ?? customer.phone2 ?? '',
      invoiceDate: invoiceDate,
      items: items,
      subtotal: subtotal,
      discountAmount: discountAmount,
      discountPercentage: discountPercentage,
      total: total,
      paidAmount: paidAmount, // إضافة المبلغ المدفوع
      notes: notes,
      createdBy: createdBy,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      final createdInvoice = await _databaseService.createInvoice(invoice);

      // إضافة المبيعات تلقائياً
      try {
        final salesService = SalesService();
        await salesService.addSalesFromInvoice(createdInvoice);
      } catch (salesError) {
        debugPrint('خطأ في إضافة المبيعات: $salesError');
        // لا نريد أن نفشل إنشاء الفاتورة بسبب خطأ في المبيعات
      }

      return createdInvoice;
    } catch (e) {
      if (e.toString().contains('customerPhone') ||
          e.toString().contains('no column named')) {
        debugPrint('مشكلة في مخطط قاعدة البيانات، محاولة إصلاح...');
        try {
          // محاولة إجبار ترقية قاعدة البيانات
          await _databaseService.forceDatabaseUpgrade();
          // إعادة المحاولة بعد الترقية
          final createdInvoice = await _databaseService.createInvoice(invoice);

          // إضافة المبيعات تلقائياً
          try {
            final salesService = SalesService();
            await salesService.addSalesFromInvoice(createdInvoice);
          } catch (salesError) {
            debugPrint('خطأ في إضافة المبيعات: $salesError');
          }

          return createdInvoice;
        } catch (upgradeError) {
          debugPrint('فشل في إصلاح قاعدة البيانات: $upgradeError');
          rethrow;
        }
      }
      rethrow;
    }
  }

  /// فحص وإصلاح قاعدة البيانات
  Future<void> checkAndFixDatabase() async {
    try {
      await _databaseService.checkAndFixDatabase();
    } catch (e) {
      debugPrint('خطأ في فحص وإصلاح قاعدة البيانات: $e');
    }
  }

  /// تحديث فاتورة موجودة
  Future<InvoiceModel> updateInvoice(InvoiceModel invoice) async {
    // التحقق من إمكانية التعديل بعد 15 دقيقة من الإنشاء
    if (!invoice.canEditAfterTimeLimit) {
      throw Exception(invoice.editStatusMessage);
    }

    // التحقق من أن تاريخ الفاتورة لا يسبق تاريخ اليوم الحالي
    final today = DateTime.now();
    final invoiceDateOnly = DateTime(
      invoice.invoiceDate.year,
      invoice.invoiceDate.month,
      invoice.invoiceDate.day,
    );
    final todayOnly = DateTime(today.year, today.month, today.day);

    if (invoiceDateOnly.isBefore(todayOnly)) {
      throw Exception('لا يمكن تحديث فاتورة بتاريخ سابق لليوم الحالي');
    }

    // التحقق من الكميات المتوفرة في المخزون
    final List<String> insufficientStockItems = [];

    for (final item in invoice.items) {
      try {} catch (e) {
        debugPrint('خطأ في التحقق من مخزون المنتج ${item.productName}: $e');
      }
    }

    if (insufficientStockItems.isNotEmpty) {
      final errorMessage =
          'الكميات المطلوبة تتجاوز المخزون المتوفر:\n${insufficientStockItems.join('\n')}';
      throw Exception(errorMessage);
    }

    // إعادة حساب الإجماليات
    final subtotal = invoice.calculateSubtotal();
    final total = subtotal; // بدون ضرائب

    final updatedInvoice = invoice.copyWith(
      subtotal: subtotal,
      total: total,
      updatedAt: DateTime.now(),
    );

    final result = await _databaseService.updateInvoice(updatedInvoice);

    // تحديث المبيعات تلقائياً
    try {
      final salesService = SalesService();
      await salesService.updateSalesFromInvoice(result);
    } catch (salesError) {
      debugPrint('خطأ في تحديث المبيعات: $salesError');
      // لا نريد أن نفشل تحديث الفاتورة بسبب خطأ في المبيعات
    }

    return result;
  }

  /// حذف فاتورة
  Future<void> deleteInvoice(String invoiceId) async {
    await _databaseService.deleteInvoice(invoiceId);

    // حذف المبيعات تلقائياً
    try {
      final salesService = SalesService();
      await salesService.deleteSalesByInvoiceId(invoiceId);
    } catch (salesError) {
      debugPrint('خطأ في حذف المبيعات: $salesError');
      // لا نريد أن نفشل حذف الفاتورة بسبب خطأ في المبيعات
    }
  }

  /// الحصول على فاتورة واحدة
  Future<InvoiceModel?> getInvoice(String invoiceId) async {
    return await _databaseService.getInvoice(invoiceId);
  }

  /// الحصول على جميع الفواتير
  Future<List<InvoiceModel>> getAllInvoices() async {
    return await _databaseService.getAllInvoices();
  }

  /// البحث في الفواتير
  Future<List<InvoiceModel>> searchInvoices(String query) async {
    if (query.trim().isEmpty) {
      return await getAllInvoices();
    }
    return await _databaseService.searchInvoices(query.trim());
  }

  /// الحصول على فواتير عميل معين
  Future<List<InvoiceModel>> getCustomerInvoices({
    required String customerId,
    String period = 'all',
  }) async {
    final allInvoices = await getAllInvoices();

    // تصفية الفواتير حسب العميل
    var customerInvoices = allInvoices
        .where((invoice) => invoice.customerId == customerId)
        .toList();

    // إذا كانت الفترة "الكل"، نعيد جميع فواتير العميل
    if (period == 'all') {
      return customerInvoices;
    }

    // تصفية حسب الفترة الزمنية
    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate;

    switch (period) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case 'week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        endDate = now;
        break;
      case 'month':
        startDate = DateTime(now.year, now.month, 1);
        endDate = now;
        break;
      case 'year':
        startDate = DateTime(now.year, 1, 1);
        endDate = now;
        break;
      default:
        return customerInvoices;
    }

    return customerInvoices.where((invoice) {
      return invoice.invoiceDate.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          invoice.invoiceDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// الحصول على فواتير بحالة معينة
  Future<List<InvoiceModel>> getInvoicesByStatus(InvoiceStatus status) async {
    return await _databaseService.getInvoicesByStatus(status);
  }

  /// تحديث حالة الفاتورة
  Future<void> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  ) async {
    // الحصول على الفاتورة للتحقق من الوقت
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice != null) {
      // التحقق من إمكانية التعديل بعد 15 دقيقة من الإنشاء
      if (!invoice.canEditAfterTimeLimit) {
        throw Exception(invoice.editStatusMessage);
      }
    }

    await _databaseService.updateInvoiceStatus(invoiceId, status);
  }

  /// الحصول على إحصائيات الفواتير
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    return await _databaseService.getInvoiceStatistics();
  }

  /// إنشاء عنصر فاتورة من منتج
  InvoiceItem createInvoiceItem({
    required ProductModel product,
    required int quantity,
    required CustomerModel customer, // إضافة العميل كمعلمة مطلوبة
    String? unit, // إضافة معلمة الوحدة
    double? customPrice, // تم تجاهل هذه المعلمة - السعر يأتي دائماً من المنتج
  }) {
    // تحديد الوحدة - استخدام الوحدة المُمررة أو وحدة المنتج الافتراضية
    String selectedUnit = unit ?? product.unit;
    if (selectedUnit.isEmpty || !AppStrings.allUnits.contains(selectedUnit)) {
      selectedUnit = AppStrings.piece; // استخدام القيمة الافتراضية
    }

    // الحصول على السعر حسب الوحدة المختارة ونوع العميل
    final unitPrice = product.getPriceForUnitAndCustomerType(
      selectedUnit,
      customer.type,
    );
    final total = unitPrice * quantity;

    return InvoiceItem(
      id: const Uuid().v4(),
      productId: product.id,
      productName: product.name,
      productCode: product.code,
      unitPrice: unitPrice,
      quantity: quantity,
      total: total,
      unit: selectedUnit,
    );
  }

  /// تحديث عنصر فاتورة
  InvoiceItem updateInvoiceItem({
    required InvoiceItem item,
    required CustomerModel customer, // إضافة العميل كمعلمة مطلوبة
    int? quantity,
    double? unitPrice,
    String? unit,
  }) {
    final newQuantity = quantity ?? item.quantity;

    // تحديث الوحدة والسعر
    String newUnit = unit ?? item.unit;
    if (newUnit.isEmpty || !AppStrings.allUnits.contains(newUnit)) {
      newUnit = AppStrings.piece; // استخدام القيمة الافتراضية
    }

    // نحتاج لجلب معلومات المنتج لحساب السعر الجديد
    // سنقوم بتحديث السعر في InvoiceItemForm عند تغيير الوحدة
    double newUnitPrice = item.unitPrice; // القيمة الافتراضية

    // إذا تغيرت الوحدة، نحتاج لحساب السعر الجديد
    if (unit != null && unit != item.unit) {
      // سنقوم بتحديث السعر في InvoiceItemForm عند تغيير الوحدة
      // هنا نستخدم السعر الحالي مؤقتاً
    }

    final newTotal = newQuantity * newUnitPrice;

    return item.copyWith(
      quantity: newQuantity,
      unitPrice: newUnitPrice,
      total: newTotal,
      unit: newUnit,
    );
  }

  /// حساب إجمالي الفاتورة
  double calculateInvoiceTotal(List<InvoiceItem> items) {
    return items.fold(0.0, (sum, item) => sum + item.total);
  }

  /// التحقق من صحة الفاتورة
  bool validateInvoice({
    required CustomerModel customer,
    required List<InvoiceItem> items,
  }) {
    // التحقق من وجود عميل
    if (customer.id.isEmpty || customer.name.isEmpty) {
      return false;
    }

    // التحقق من وجود عناصر
    if (items.isEmpty) {
      return false;
    }

    // التحقق من صحة العناصر
    for (final item in items) {
      if (item.productId.isEmpty ||
          item.productName.isEmpty ||
          item.quantity <= 0 ||
          item.unitPrice <= 0) {
        return false;
      }
    }

    return true;
  }

  /// الحصول على فواتير اليوم
  Future<List<InvoiceModel>> getTodayInvoices() async {
    final allInvoices = await getAllInvoices();
    final today = DateTime.now();

    return allInvoices.where((invoice) {
      return invoice.invoiceDate.year == today.year &&
          invoice.invoiceDate.month == today.month &&
          invoice.invoiceDate.day == today.day;
    }).toList();
  }

  /// الحصول على فواتير الشهر الحالي
  Future<List<InvoiceModel>> getCurrentMonthInvoices() async {
    final allInvoices = await getAllInvoices();
    final now = DateTime.now();

    return allInvoices.where((invoice) {
      return invoice.invoiceDate.year == now.year &&
          invoice.invoiceDate.month == now.month;
    }).toList();
  }

  /// الحصول على فواتير فترة زمنية
  Future<List<InvoiceModel>> getInvoicesByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final allInvoices = await getAllInvoices();

    return allInvoices.where((invoice) {
      return invoice.invoiceDate.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          invoice.invoiceDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// الحصول على فواتير حسب الفترة المحددة
  Future<List<InvoiceModel>> getInvoicesByDatePeriod(String period) async {
    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate;

    switch (period) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case 'week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        endDate = now;
        break;
      case 'twoWeeks':
        startDate = now.subtract(const Duration(days: 14));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        endDate = now;
        break;
      case 'month':
        startDate = DateTime(now.year, now.month, 1);
        endDate = now;
        break;
      default:
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    }

    return getInvoicesByDateRange(startDate: startDate, endDate: endDate);
  }

  /// تحديث المبلغ المدفوع للفاتورة
  Future<void> updatePaidAmount(String invoiceId, double paidAmount) async {
    if (paidAmount < 0) {
      throw Exception('المبلغ المدفوع لا يمكن أن يكون سالباً');
    }

    // الحصول على الفاتورة للتأكد من وجودها
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice == null) {
      throw Exception('الفاتورة غير موجودة');
    }

    // التحقق من إمكانية التعديل بعد 15 دقيقة من الإنشاء
    if (!invoice.canEditAfterTimeLimit) {
      throw Exception(invoice.editStatusMessage);
    }

    // التحقق من أن المبلغ المدفوع لا يتجاوز إجمالي الفاتورة
    if (paidAmount > invoice.total) {
      throw Exception('المبلغ المدفوع لا يمكن أن يتجاوز إجمالي الفاتورة');
    }

    // تحديث المبلغ المدفوع
    await _databaseService.updateInvoicePaidAmount(invoiceId, paidAmount);

    // تحديث حالة الفاتورة تلقائياً بناءً على المبلغ المدفوع
    InvoiceStatus newStatus;
    if (paidAmount >= invoice.total) {
      // إذا تم دفع المبلغ بالكامل أو أكثر، تصبح الفاتورة مدفوعة
      newStatus = InvoiceStatus.paid;
    } else if (paidAmount > 0) {
      // إذا تم دفع جزء من المبلغ، تبقى الفاتورة معلقة
      newStatus = InvoiceStatus.partial;
    } else {
      // إذا لم يتم دفع أي مبلغ، تبقى الفاتورة معلقة
      newStatus = InvoiceStatus.pending;
    }

    // تحديث حالة الفاتورة
    await _databaseService.updateInvoiceStatus(invoiceId, newStatus);
  }

  /// حذف جميع الفواتير
  Future<void> clearAllInvoices() async {
    await _databaseService.clearAllInvoices();
  }

  /// الحصول على فاتورة بواسطة المعرف
  Future<InvoiceModel?> getInvoiceById(String invoiceId) async {
    try {
      return await _databaseService.getInvoiceById(invoiceId);
    } catch (e) {
      debugPrint('Error getting invoice by ID: $e');
      return null;
    }
  }

  /// الحصول على الفواتير حسب المحافظة
  Stream<Map<String, List<InvoiceModel>>> getInvoicesByGovernorate() {
    return Stream.fromFuture(_getInvoicesByGovernorateAsync());
  }

  /// الحصول على الفواتير حسب المحافظة بشكل متزامن
  Future<Map<String, List<InvoiceModel>>>
  _getInvoicesByGovernorateAsync() async {
    try {
      final invoices = await getAllInvoices();
      final customerService = CustomerService();

      final Map<String, List<InvoiceModel>> invoicesByGovernorate = {};

      for (final invoice in invoices) {
        try {
          // الحصول على معلومات العميل من قاعدة البيانات
          final customer = await customerService.getCustomerById(
            invoice.customerId,
          );
          final governorate = customer?.governorate ?? 'غير محدد';

          invoicesByGovernorate.putIfAbsent(governorate, () => []);
          invoicesByGovernorate[governorate]!.add(invoice);
        } catch (e) {
          // في حالة عدم العثور على العميل، نضع الفاتورة في فئة "غير محدد"
          final governorate = 'غير محدد';
          invoicesByGovernorate.putIfAbsent(governorate, () => []);
          invoicesByGovernorate[governorate]!.add(invoice);
        }
      }

      return invoicesByGovernorate;
    } catch (e) {
      debugPrint('Error getting invoices by governorate: $e');
      return {};
    }
  }

  /// تصدير الفاتورة كنص للمشاركة
  Future<String> exportInvoiceToText(InvoiceModel invoice) async {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final timeFormat = DateFormat('hh:mm a', 'en_US'); // نظام 12 ساعة مع AM/PM

    final invoiceDate = dateFormat.format(invoice.invoiceDate);
    final invoiceTime = timeFormat.format(invoice.invoiceDate);

    final remainingAmount = invoice.total - invoice.paidAmount;

    final StringBuffer message = StringBuffer();
    message.write('''
🏥 *Atlas Medical Supplies*

👤 *العميل:* ${invoice.customerName}

📋 *تفاصيل الفاتورة:*
• رقم الفاتورة: ${invoice.invoiceNumber}
• التاريخ: $invoiceDate
• الوقت: $invoiceTime

💰 *المبالغ:*
• المبلغ الإجمالي: ${invoice.total.toStringAsFixed(2)} ر.س
• المدفوع: ${invoice.paidAmount.toStringAsFixed(2)} ر.س
• المتبقي: ${remainingAmount.toStringAsFixed(2)} ر.س

📦 *المنتجات (${invoice.items.length} منتج):*
''');

    // إضافة تفاصيل المنتجات
    for (int i = 0; i < invoice.items.length; i++) {
      final item = invoice.items[i];
      message.write(
        '${i + 1}. ${item.productName} - ${item.quantity} ${item.unit} - ${item.total.toStringAsFixed(2)} ر.س\n',
      );
    }

    // إضافة الخصم إذا كان موجود
    if (invoice.discountAmount > 0 || invoice.discountPercentage > 0) {
      message.write('\n🎯 *الخصم:* ');
      if (invoice.discountPercentage > 0) {
        message.write('${invoice.discountPercentage.toStringAsFixed(1)}%');
      } else {
        message.write('${invoice.discountAmount.toStringAsFixed(2)} ر.س');
      }
    }

    // إضافة الملاحظات إذا كانت موجودة
    if (invoice.notes != null && invoice.notes!.isNotEmpty) {
      message.write('\n\n📝 *ملاحظات:* ${invoice.notes}');
    }

    // إضافة حالة الدفع
    message.write('\n\n💳 *حالة الدفع:* ');
    if (invoice.isFullyPaid) {
      message.write('✅ تم الدفع بالكامل');
    } else if (invoice.paidAmount > 0) {
      message.write('🔄 دفع جزئي');
    } else {
      message.write('⏳ معلق');
    }

    // إضافة تفاصيل المبالغ المحصلة إذا كانت موجودة
    if (invoice.paidAmount > 0) {
      message.write('\n\n💵 *تفاصيل المبالغ المحصلة:*');

      // الحصول على تفاصيل التحصيل من قاعدة البيانات
      await _addCollectionDetails(message, invoice.id);
    }

    message.write('\n\nشكراً لثقتكم في Atlas Medical Supplies 🏥');

    return message.toString();
  }

  /// إضافة تفاصيل المبالغ المحصلة للنص
  Future<void> _addCollectionDetails(
    StringBuffer message,
    String invoiceId,
  ) async {
    try {
      final CollectionService collectionService = CollectionService();
      final collections = await collectionService.getCollectionsByInvoiceId(
        invoiceId,
      );

      if (collections.isNotEmpty) {
        // ترتيب التحصيلات حسب التاريخ (الأحدث أولاً)
        collections.sort(
          (a, b) => b.collectionDate.compareTo(a.collectionDate),
        );

        for (int i = 0; i < collections.length; i++) {
          final collection = collections[i];
          final collectionDate = DateFormat(
            'dd/MM/yyyy',
          ).format(collection.collectionDate);
          final collectionTime = DateFormat(
            'hh:mm a',
            'en_US',
          ).format(collection.collectionDate);

          message.write(
            '\n${i + 1}. ${collection.collectionAmount.toStringAsFixed(2)} ر.س',
          );
          message.write(' - $collectionDate $collectionTime');

          if (collection.notes != null && collection.notes!.isNotEmpty) {
            message.write(' (${collection.notes})');
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في جلب تفاصيل التحصيل: $e');
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }
}
