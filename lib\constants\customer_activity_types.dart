class CustomerActivityTypes {
  static const Map<String, String> activities = {
    'pharmacy': 'صيدلية',
    'hospital': 'مستشفى',
    'clinic': 'عيادة طبية',
    'medical_center': 'مركز طبي',
    'laboratory': 'مختبر طبي',
    'radiology_center': 'مركز أشعة',
    'dental_clinic': 'عيادة أسنان',
    'optical_center': 'مركز بصريات',
    'physiotherapy_center': 'مركز علاج طبيعي',
    'pharmaceutical_company': 'شركة أدوية',
    'medical_supplies': 'مستلزمات طبية',
    'veterinary_clinic': 'عيادة بيطرية',
    'medical_equipment': 'أجهزة طبية',
    'health_insurance': 'تأمين صحي',
    'medical_tourism': 'سياحة طبية',
    'other': 'أخرى',
  };

  static List<String> getActivityNames() {
    return activities.values.toList();
  }

  static String getActivityKey(String arabicName) {
    return activities.entries
        .firstWhere((entry) => entry.value == arabicName,
            orElse: () => MapEntry('other', 'أخرى'))
        .key;
  }

  static String getActivityValue(String key) {
    return activities[key] ?? 'أخرى';
  }
}
