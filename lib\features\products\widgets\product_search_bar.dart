import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';

class ProductSearchBar extends StatefulWidget {
  final TextEditingController searchController;
  final String selectedCategory;
  final String sortBy;
  final bool sortAscending;
  final bool showLowStockOnly;
  final Function(String) onSearchChanged;
  final Function(String) onCategoryChanged;
  final Function(String) onSortChanged;
  final VoidCallback onSortDirectionChanged;
  final Function(bool) onLowStockFilterChanged;

  const ProductSearchBar({
    super.key,
    required this.searchController,
    required this.selectedCategory,
    required this.sortBy,
    required this.sortAscending,
    required this.showLowStockOnly,
    required this.onSearchChanged,
    required this.onCategoryChanged,
    required this.onSortChanged,
    required this.onSortDirectionChanged,
    required this.onLowStockFilterChanged,
  });

  @override
  State<ProductSearchBar> createState() => _ProductSearchBarState();
}

class _ProductSearchBarState extends State<ProductSearchBar> {
  bool _showFilters = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث الرئيسي
          _buildSearchBar(),

          // أزرار التصفية السريعة
          _buildQuickFilters(),

          // لوحة التصفية المتقدمة
          if (_showFilters) _buildAdvancedFilters(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.surface, AppColors.surface.withOpacity(0.9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: AppColors.primary.withOpacity(0.2), width: 1),
      ),
      child: Row(
        children: [
          // أيقونة البحث مع تأثير متحرك
          Container(
            padding: const EdgeInsets.all(16),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.searchController.text.isNotEmpty
                    ? AppColors.primary.withOpacity(0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                widget.searchController.text.isNotEmpty
                    ? Icons.search
                    : Icons.search_outlined,
                color: AppColors.primary,
                size: 24,
              ),
            ),
          ),

          // حقل البحث
          Expanded(
            child: TextField(
              controller: widget.searchController,
              onChanged: widget.onSearchChanged,
              decoration: InputDecoration(
                hintText: 'البحث بالاسم، الكود، أو الباركود...',
                hintStyle: TextStyle(color: AppColors.textHint, fontSize: 16),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // زر مسح النص مع تأثير متحرك
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: widget.searchController.text.isNotEmpty
                ? IconButton(
                    key: const ValueKey('clear_button'),
                    onPressed: () {
                      widget.searchController.clear();
                      widget.onSearchChanged('');
                    },
                    icon: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.clear,
                        color: AppColors.error,
                        size: 18,
                      ),
                    ),
                  )
                : const SizedBox(key: ValueKey('empty_space')),
          ),

          // زر الفلاتر مع تأثير متحرك
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _showFilters = !_showFilters;
                });
              },
              icon: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _showFilters
                      ? AppColors.primary.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
                  color: _showFilters
                      ? AppColors.primary
                      : AppColors.textSecondary,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // تصفية حسب الفئة
            _buildFilterChip(
              'الكل',
              'all',
              widget.selectedCategory == 'all',
              AppColors.primary,
              () => widget.onCategoryChanged('all'),
            ),
            _buildFilterChip(
              'أجهزة طبية',
              'devices',
              widget.selectedCategory == 'devices',
              AppColors.info,
              () => widget.onCategoryChanged('devices'),
            ),
            _buildFilterChip(
              'مستهلكات',
              'consumables',
              widget.selectedCategory == 'consumables',
              AppColors.success,
              () => widget.onCategoryChanged('consumables'),
            ),
            _buildFilterChip(
              'معقمات',
              'sterilization',
              widget.selectedCategory == 'sterilization',
              AppColors.warning,
              () => widget.onCategoryChanged('sterilization'),
            ),
            _buildFilterChip(
              'مستلزمات معمل',
              'laboratory',
              widget.selectedCategory == 'laboratory',
              AppColors.accent,
              () => widget.onCategoryChanged('laboratory'),
            ),
            _buildFilterChip(
              'مستلزمات عامة',
              'general_supplies',
              widget.selectedCategory == 'general_supplies',
              AppColors.secondary,
              () => widget.onCategoryChanged('general_supplies'),
            ),

            const SizedBox(width: 16),

            // تصفية المخزون المنخفض
            _buildSwitchFilter(
              'مخزون منخفض فقط',
              widget.showLowStockOnly,
              widget.onLowStockFilterChanged,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التصفية المتقدمة
          Row(
            children: [
              Icon(Icons.tune, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'خيارات التصفية والترتيب',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // خيارات الترتيب
          Row(
            children: [
              Expanded(
                child: _buildSortOption('الاسم', 'name', Icons.sort_by_alpha),
              ),
              const SizedBox(width: 12),
              Expanded(child: _buildSortOption('الكود', 'code', Icons.qr_code)),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSortOption('السعر', 'price', Icons.attach_money),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSortOption('الكمية', 'quantity', Icons.inventory),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // اتجاه الترتيب
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'اتجاه الترتيب:',
                style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: widget.onSortDirectionChanged,
                icon: Icon(
                  widget.sortAscending
                      ? Icons.arrow_upward
                      : Icons.arrow_downward,
                  size: 18,
                ),
                label: Text(
                  widget.sortAscending ? 'تصاعدي' : 'تنازلي',
                  style: const TextStyle(fontSize: 12),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  minimumSize: const Size(80, 36),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String value,
    bool isSelected,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.textOnPrimary : color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: AppColors.surfaceVariant,
        selectedColor: color,
        checkmarkColor: AppColors.textOnPrimary,
        side: BorderSide(
          color: isSelected ? color : AppColors.border,
          width: 1,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildSwitchFilter(
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.warning,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(String label, String value, IconData icon) {
    final isSelected = widget.sortBy == value;

    return InkWell(
      onTap: () => widget.onSortChanged(value),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? AppColors.textOnPrimary
                  : AppColors.textSecondary,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
