# ميزات مشاركة الفاتورة

## الوصف
تم إضافة ميزات مشاركة متقدمة للفواتير تتيح للمستخدمين مشاركة الفواتير بعدة طرق مختلفة، بما في ذلك المشاركة عبر واتس آب والرسائل النصية والمشاركة العامة.

## الميزات المضافة

### 1. زر مشاركة متقدم في شريط العنوان
- **موقع**: في شريط العنوان (AppBar) بجانب العنوان
- **الشكل**: أيقونة مشاركة مع قائمة منسدلة
- **الخيارات**: 4 خيارات مشاركة مختلفة

### 2. زر مشاركة في أزرار الإجراءات
- **موقع**: في أسفل الشاشة مع أزرار الطباعة والحذف
- **الشكل**: زر "مشاركة" مع قائمة منسدلة
- **الخيارات**: نفس الخيارات الأربعة

## خيارات المشاركة المتاحة

### 1. مشاركة عبر واتس آب 🟢
- **الوظيفة**: فتح واتس آب مع نص الفاتورة جاهز للإرسال
- **الاستخدام**: 
  - إذا كان واتس آب مثبت: يفتح التطبيق مع النص
  - إذا لم يكن مثبت: يفتح متجر Google Play
- **الميزات**: 
  - ترميز النص تلقائياً
  - معالجة الأخطاء
  - رسائل تأكيد للمستخدم

### 2. مشاركة عبر رسالة نصية 📱
- **الوظيفة**: فتح تطبيق الرسائل مع نص الفاتورة
- **الاستخدام**: يفتح تطبيق الرسائل الافتراضي
- **الميزات**:
  - دعم جميع تطبيقات الرسائل
  - معالجة الأخطاء
  - رسائل تأكيد

### 3. نسخ كنص 📋
- **الوظيفة**: نسخ نص الفاتورة إلى الحافظة
- **الاستخدام**: يمكن لصق النص في أي مكان
- **الميزات**:
  - نسخ فوري
  - رسالة تأكيد
  - سهولة الاستخدام

### 4. مشاركة عامة 🔗
- **الوظيفة**: فتح قائمة المشاركة العامة للنظام
- **الاستخدام**: يظهر جميع التطبيقات المتاحة للمشاركة
- **الميزات**:
  - دعم جميع التطبيقات
  - مشاركة عبر البريد الإلكتروني
  - مشاركة عبر وسائل التواصل الاجتماعي

## الكود المضافة

### زر المشاركة في شريط العنوان
```dart
PopupMenuButton<String>(
  icon: const Icon(Icons.share, color: Colors.white),
  tooltip: 'مشاركة الفاتورة',
  onSelected: (value) => _handleShareOption(value),
  itemBuilder: (context) => [
    PopupMenuItem(
      value: 'whatsapp',
      child: Row(
        children: [
          Icon(Icons.chat, color: Colors.green),
          SizedBox(width: 8),
          Text('مشاركة عبر واتس آب'),
        ],
      ),
    ),
    // ... باقي الخيارات
  ],
),
```

### زر المشاركة في أزرار الإجراءات
```dart
PopupMenuButton<String>(
  child: Container(
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    decoration: BoxDecoration(
      color: AppColors.primary,
      borderRadius: BorderRadius.circular(8),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.share, color: Colors.white),
        const SizedBox(width: 8),
        const Text('مشاركة'),
        const Icon(Icons.arrow_drop_down, color: Colors.white),
      ],
    ),
  ),
  onSelected: (value) => _handleShareOption(value),
  // ... باقي الكود
),
```

### دوال المشاركة
```dart
void _handleShareOption(String option) {
  switch (option) {
    case 'whatsapp':
      _shareViaWhatsApp();
      break;
    case 'sms':
      _shareViaSMS();
      break;
    case 'text':
      _copyToClipboard();
      break;
    case 'general':
      _shareGeneral();
      break;
  }
}

void _shareViaWhatsApp() async {
  final invoiceText = _invoiceService.exportInvoiceToText(widget.invoice);
  final whatsappUrl = 'whatsapp://send?text=${Uri.encodeComponent(invoiceText)}';
  
  try {
    if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
      await launchUrl(Uri.parse(whatsappUrl));
    } else {
      final playStoreUrl = 'https://play.google.com/store/apps/details?id=com.whatsapp';
      await launchUrl(Uri.parse(playStoreUrl));
    }
  } catch (e) {
    // معالجة الأخطاء
  }
}
```

## المكتبات المطلوبة

### 1. share_plus
```yaml
dependencies:
  share_plus: ^7.2.1
```
- **الوظيفة**: المشاركة العامة عبر النظام
- **الاستخدام**: مشاركة النص مع جميع التطبيقات المتاحة

### 2. url_launcher
```yaml
dependencies:
  url_launcher: ^6.3.1
```
- **الوظيفة**: فتح روابط واتس آب والرسائل
- **الاستخدام**: فتح التطبيقات الخارجية

## كيفية عمل المشاركة

### 1. مشاركة عبر واتس آب
1. المستخدم يختار "مشاركة عبر واتس آب"
2. يتم إنشاء نص الفاتورة
3. يتم ترميز النص للرابط
4. فتح واتس آب مع النص جاهز
5. المستخدم يختار جهة الاتصال ويرسل

### 2. مشاركة عبر رسالة نصية
1. المستخدم يختار "مشاركة عبر رسالة نصية"
2. يتم إنشاء نص الفاتورة
3. فتح تطبيق الرسائل
4. النص جاهز في الرسالة
5. المستخدم يختار الرقم ويرسل

### 3. نسخ كنص
1. المستخدم يختار "نسخ كنص"
2. يتم نسخ النص إلى الحافظة
3. رسالة تأكيد
4. يمكن لصق النص في أي مكان

### 4. مشاركة عامة
1. المستخدم يختار "مشاركة عامة"
2. فتح قائمة المشاركة العامة
3. اختيار التطبيق المطلوب
4. مشاركة النص

## الميزات التقنية

### 1. معالجة الأخطاء
- **واتس آب غير مثبت**: فتح متجر Google Play
- **تطبيق الرسائل غير متاح**: رسالة خطأ واضحة
- **أخطاء عامة**: رسائل خطأ مفصلة

### 2. تجربة المستخدم
- **أيقونات ملونة**: كل خيار له لون مميز
- **رسائل تأكيد**: تأكيد نجاح العملية
- **واجهة بديهية**: سهولة الاستخدام

### 3. التوافق
- **Android**: دعم كامل
- **iOS**: دعم كامل
- **Web**: دعم محدود (المشاركة العامة فقط)

## الفوائد

### 1. للمستخدم
- **سهولة المشاركة**: خيارات متعددة
- **سرعة**: مشاركة فورية
- **مرونة**: اختيار الطريقة المناسبة

### 2. للنظام
- **احترافية**: ميزات متقدمة
- **تكامل**: مع التطبيقات الخارجية
- **موثوقية**: معالجة الأخطاء

## الاختبار

### 1. اختبار واتس آب
- [ ] واتس آب مثبت ويعمل
- [ ] واتس آب غير مثبت (يفتح المتجر)
- [ ] معالجة الأخطاء

### 2. اختبار الرسائل
- [ ] تطبيق الرسائل يعمل
- [ ] تطبيق الرسائل غير متاح
- [ ] معالجة الأخطاء

### 3. اختبار النسخ
- [ ] نسخ النص
- [ ] رسالة التأكيد
- [ ] لصق النص

### 4. اختبار المشاركة العامة
- [ ] فتح قائمة المشاركة
- [ ] اختيار التطبيق
- [ ] مشاركة النص

## ملاحظات مهمة

### 1. الأذونات
- **Android**: لا يحتاج أذونات إضافية
- **iOS**: قد يحتاج أذونات للوصول للتطبيقات

### 2. التوافق
- **واتس آب**: يعمل على جميع الأجهزة
- **الرسائل**: يعتمد على التطبيق المثبت
- **المشاركة العامة**: يعتمد على النظام

### 3. الأمان
- **البيانات**: لا يتم إرسال بيانات حساسة
- **الروابط**: روابط آمنة ومشفرة
- **الأخطاء**: رسائل خطأ آمنة

## التطوير المستقبلي

### 1. مشاركة كصورة
- **PDF**: تصدير الفاتورة كملف PDF
- **صورة**: تحويل الفاتورة لصورة
- **طباعة**: إرسال للطباعة

### 2. مشاركة متقدمة
- **QR Code**: إنشاء رمز QR للفاتورة
- **رابط**: رابط مباشر للفاتورة
- **تقويم**: إضافة موعد الدفع للتقويم

### 3. تخصيص
- **قوالب**: قوالب مشاركة مختلفة
- **لغات**: دعم لغات متعددة
- **تصميم**: تخصيص شكل المشاركة
