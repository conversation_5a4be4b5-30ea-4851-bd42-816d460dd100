import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../models/invoice_model.dart';
import '../../../models/customer_model.dart';
import '../../../models/collection_model.dart';
import '../../../constants/app_colors.dart';
import '../../../widgets/back_button.dart';
import '../../../services/invoice_service.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../../../core/settings/app_settings.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final InvoiceModel invoice;
  final CustomerModel? customer;

  const InvoiceDetailsScreen({Key? key, required this.invoice, this.customer})
    : super(key: key);

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  final InvoiceService _invoiceService = InvoiceService();
  final CollectionService _collectionService = CollectionService();
  final AppSettings _appSettings = AppSettings();
  bool _isProtected = false;
  List<CollectionModel> _collections = [];
  bool _isLoadingCollections = true;

  @override
  void initState() {
    super.initState();
    _checkInvoiceProtection();
    _loadCollections();
  }

  Future<void> _loadCollections() async {
    try {
      final collections = await _collectionService.getCollectionsByInvoiceId(
        widget.invoice.id,
      );
      setState(() {
        _collections = collections;
        _isLoadingCollections = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCollections = false;
      });
      print('خطأ في تحميل التحصيلات: $e');
    }
  }

  Future<void> _checkInvoiceProtection() async {
    final isProtected = await _appSettings.isPasswordRequiredForInvoiceAccess();
    if (isProtected) {
      _showInvoiceProtectionDialog();
    }
  }

  void _showInvoiceProtectionDialog() {
    showDeleteProtectionDialog(
      context: context,
      title: 'حماية الفاتورة',
      message:
          'هذه الفاتورة محمية بكلمة مرور. يرجى إدخال كلمة المرور للوصول إليها.',
      itemName: 'الفاتورة رقم ${widget.invoice.invoiceNumber}',
      onConfirm: () {
        setState(() {
          _isProtected = false;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'تفاصيل الفاتورة رقم ${widget.invoice.invoiceNumber}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: _showShareOptions,
            tooltip: 'مشاركة الفاتورة',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات العميل
                  _buildCustomerSection(),
                  const SizedBox(height: 16),

                  // معلومات الفاتورة الأساسية
                  _buildInvoiceBasicInfo(),
                  const SizedBox(height: 16),

                  // تفاصيل المنتجات
                  _buildProductsSection(),
                  const SizedBox(height: 16),

                  // ملخص الفاتورة
                  _buildInvoiceSummary(),
                  const SizedBox(height: 16),

                  // سجل التحصيلات
                  if (widget.invoice.paidAmount > 0) ...[
                    _buildCollectionsSection(),
                    const SizedBox(height: 16),
                  ],

                  // الملاحظات
                  if (widget.invoice.notes != null &&
                      widget.invoice.notes!.isNotEmpty) ...[
                    _buildNotesSection(),
                    const SizedBox(height: 16),
                  ],

                  // معلومات النظام
                  _buildSystemInfo(),
                  const SizedBox(height: 100), // مساحة للأزرار
                ],
              ),
            ),
          ),

          // أزرار الإجراءات في الأسفل
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'معلومات العميل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (widget.customer != null) ...[
            Text(
              'الاسم: ${widget.customer!.name}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            if (widget.customer!.phone1 != null) ...[
              const SizedBox(height: 8),
              Text(
                'الهاتف: ${widget.customer!.phone1}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
            if (widget.customer!.phone2 != null) ...[
              const SizedBox(height: 8),
              Text(
                'هاتف إضافي: ${widget.customer!.phone2}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
            if (widget.customer!.address != null) ...[
              const SizedBox(height: 8),
              Text(
                'العنوان: ${widget.customer!.address}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ] else ...[
            Text(
              'العميل: ${widget.invoice.customerName}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            if (widget.invoice.customerPhone.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'الهاتف: ${widget.invoice.customerPhone}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildInvoiceBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.receipt, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'معلومات الفاتورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رقم الفاتورة',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      widget.invoice.invoiceNumber,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ الفاتورة',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      widget.invoice.formattedDate,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الفاتورة',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${widget.invoice.total.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ المدفوع',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${widget.invoice.paidAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: widget.invoice.paidAmount > 0
                            ? Colors.green[700]
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // إضافة سطر جديد لعرض المبلغ المتبقي بوضوح
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ المتبقي',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${_getRemainingAmount().toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: _getRemainingAmount() > 0
                            ? Colors.orange[700]
                            : Colors.green[700],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة الدفع',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getPaymentStatusColor().withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _getPaymentStatusColor().withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        _getPaymentStatusText(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: _getPaymentStatusColor(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة مساعدة للحصول على نص حالة الدفع
  String _getPaymentStatusText() {
    if (widget.invoice.isFullyPaid) {
      return 'تم الدفع بالكامل';
    } else if (widget.invoice.paidAmount > 0) {
      return 'دفع جزئي';
    } else {
      return 'لم يتم الدفع';
    }
  }

  // دالة مساعدة للحصول على لون حالة الدفع
  Color _getPaymentStatusColor() {
    if (widget.invoice.isFullyPaid) {
      return Colors.green;
    } else if (widget.invoice.paidAmount > 0) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }

  Widget _buildProductsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.shopping_cart, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'المنتجات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const Spacer(),
              Text(
                '${widget.invoice.items.length} منتج',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...widget.invoice.items
              .map((item) => _buildProductItem(item))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildProductItem(InvoiceItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (item.productCode.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'الكود: ${item.productCode}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'الكمية',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  '${item.quantity} ${item.unit}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'السعر',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  '${item.unitPrice.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'الإجمالي',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  '${item.total.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.calculate, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'ملخص الفاتورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text('المجموع الفرعي:'),
              const Spacer(),
              Text('${widget.invoice.subtotal.toStringAsFixed(2)} ر.س'),
            ],
          ),
          if (widget.invoice.discountAmount > 0 ||
              widget.invoice.discountPercentage > 0) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Text('الخصم:', style: TextStyle(color: Colors.red[600])),
                const Spacer(),
                Text(
                  '-${_getDiscountValue().toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    color: Colors.red[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
          const Divider(),
          Row(
            children: [
              const Text(
                'الإجمالي النهائي:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${widget.invoice.total.toStringAsFixed(2)} ر.س',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          // إضافة قسم معلومات الدفع
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.payment, color: Colors.blue, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'معلومات الدفع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Text(
                      'المبلغ المدفوع:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${widget.invoice.paidAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: widget.invoice.paidAmount > 0
                            ? Colors.green[700]
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'المبلغ المتبقي:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${_getRemainingAmount().toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _getRemainingAmount() > 0
                            ? Colors.orange[700]
                            : Colors.green[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'نسبة الدفع:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${widget.invoice.calculatePaymentPercentage().toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color:
                            widget.invoice.calculatePaymentPercentage() >= 100
                            ? Colors.green[700]
                            : Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.note, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'ملاحظات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.invoice.notes!,
            style: const TextStyle(fontSize: 14, height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.grey),
              const SizedBox(width: 8),
              Text(
                'معلومات النظام',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ الإنشاء',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      _formatDateTime(widget.invoice.createdAt),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'آخر تحديث',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      _formatDateTime(widget.invoice.updatedAt),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (widget.invoice.createdBy != null) ...[
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أنشئ بواسطة',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  widget.invoice.createdBy!,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partial:
        return Colors.blue;
      case InvoiceStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return 'معلق';
      case InvoiceStatus.paid:
        return 'مدفوع';
      case InvoiceStatus.partial:
        return 'دفع جزئي';
      case InvoiceStatus.cancelled:
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  double _getDiscountValue() {
    if (widget.invoice.discountPercentage > 0) {
      return widget.invoice.subtotal *
          (widget.invoice.discountPercentage / 100);
    }
    return widget.invoice.discountAmount;
  }

  double _getRemainingAmount() {
    return widget.invoice.total - widget.invoice.paidAmount;
  }

  void _showShareOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // عنوان البطاقة
              Row(
                children: [
                  const Icon(Icons.share, color: AppColors.primary, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'مشاركة الفاتورة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // خيار WhatsApp
              _buildShareOption(
                context: context,
                icon: Icons.chat_bubble,
                title: 'مشاركة عبر WhatsApp',
                subtitle: 'إرسال الفاتورة عبر WhatsApp',
                color: const Color(0xFF25D366),
                onTap: () => _shareViaWhatsApp(),
              ),

              const SizedBox(height: 12),

              // خيار SMS
              _buildShareOption(
                context: context,
                icon: Icons.sms,
                title: 'مشاركة عبر SMS',
                subtitle: 'إرسال الفاتورة عبر الرسائل النصية',
                color: Colors.blue,
                onTap: () => _shareViaSMS(),
              ),

              const SizedBox(height: 12),

              // خيار نسخ النص
              _buildShareOption(
                context: context,
                icon: Icons.copy,
                title: 'نسخ النص',
                subtitle: 'نسخ نص الفاتورة إلى الحافظة',
                color: Colors.purple,
                onTap: () => _copyToClipboard(),
              ),

              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade200),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  void _shareViaWhatsApp() async {
    try {
      final message = await _buildInvoiceMessage();

      // الحصول على رقم الهاتف الأفضل للعميل
      final phoneNumber = _getBestPhoneNumber();
      final formattedPhone = _formatPhoneNumber(phoneNumber);

      // إغلاق النافذة المنبثقة أولاً
      Navigator.pop(context);

      String successMessage;
      bool launched = false;

      if (formattedPhone.isNotEmpty) {
        // محاولة فتح واتساب مع رقم العميل
        successMessage = 'تم فتح WhatsApp مع رقم العميل: $formattedPhone';

        // تجربة عدة طرق لفتح واتساب
        final uris = [
          // الطريقة الأولى: رابط wa.me (الأكثر موثوقية)
          Uri.parse(
            'https://wa.me/$formattedPhone?text=${Uri.encodeComponent(message)}',
          ),
          // الطريقة الثانية: مخطط واتساب المباشر
          Uri.parse(
            'whatsapp://send?phone=$formattedPhone&text=${Uri.encodeComponent(message)}',
          ),
          // الطريقة الثالثة: مخطط واتساب بدون رقم
          Uri.parse('whatsapp://send?text=${Uri.encodeComponent(message)}'),
        ];

        for (final uri in uris) {
          try {
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              launched = true;
              break;
            }
          } catch (e) {
            // استمر في المحاولة مع الطريقة التالية
            continue;
          }
        }
      } else {
        // بدون رقم: افتح واتساب مع النص فقط
        successMessage = 'تم فتح WhatsApp. يمكنك اختيار جهة الاتصال يدوياً';

        final uris = [
          Uri.parse('https://wa.me/?text=${Uri.encodeComponent(message)}'),
          Uri.parse('whatsapp://send?text=${Uri.encodeComponent(message)}'),
        ];

        for (final uri in uris) {
          try {
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              launched = true;
              break;
            }
          } catch (e) {
            continue;
          }
        }
      }

      if (!launched) {
        // عرض خيار فتح متجر Google Play لتحميل واتساب
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text(
                'واتساب غير مثبت',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
              content: const Text(
                'يبدو أن واتساب غير مثبت على جهازك. هل تريد تحميله من متجر Google Play؟',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                ),
                TextButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                    try {
                      final playStoreUri = Uri.parse(
                        'https://play.google.com/store/apps/details?id=com.whatsapp',
                      );
                      if (await canLaunchUrl(playStoreUri)) {
                        await launchUrl(
                          playStoreUri,
                          mode: LaunchMode.externalApplication,
                        );
                      }
                    } catch (e) {
                      _showErrorSnackBar('تعذر فتح متجر Google Play');
                    }
                  },
                  child: const Text(
                    'تحميل',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
          );
        }
        return;
      }

      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    successMessage,
                    style: const TextStyle(fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      // إغلاق النافذة المنبثقة في حالة الخطأ
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      _showErrorSnackBar('حدث خطأ أثناء فتح WhatsApp: $e');
    }
  }

  void _shareViaSMS() async {
    final message = await _buildInvoiceMessage();

    // الحصول على رقم الهاتف الأفضل للعميل
    final phoneNumber = _getBestPhoneNumber();
    final formattedPhone = _formatPhoneNumber(phoneNumber);

    // التحقق من أن رقم الهاتف صحيح
    if (formattedPhone.isEmpty) {
      _showErrorSnackBar('لا يوجد رقم هاتف صحيح للعميل');
      return;
    }

    // إنشاء رابط الرسائل مع رقم الهاتف والنص
    final smsUrl = 'sms:$formattedPhone?body=${Uri.encodeComponent(message)}';

    try {
      final uri = Uri.parse(smsUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        Navigator.pop(context);

        // عرض رسالة تأكيد
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم فتح تطبيق الرسائل مع رقم العميل: $formattedPhone',
                style: const TextStyle(fontFamily: 'Cairo'),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        _showErrorSnackBar('لا يمكن فتح تطبيق الرسائل');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء فتح تطبيق الرسائل: $e');
    }
  }

  Future<String> _buildInvoiceMessage() async {
    final invoiceService = InvoiceService();
    return await invoiceService.exportInvoiceToText(widget.invoice);
  }

  /// الحصول على رقم الهاتف الأفضل للعميل
  String _getBestPhoneNumber() {
    // محاولة الحصول على رقم الهاتف من بيانات العميل إذا كانت متوفرة
    if (widget.customer != null) {
      // أولوية للهاتف الأول
      if (widget.customer!.phone1 != null &&
          widget.customer!.phone1!.isNotEmpty &&
          widget.customer!.phone1!.length >= 10) {
        return widget.customer!.phone1!;
      }
      // ثم الهاتف الثاني
      if (widget.customer!.phone2 != null &&
          widget.customer!.phone2!.isNotEmpty &&
          widget.customer!.phone2!.length >= 10) {
        return widget.customer!.phone2!;
      }
    }

    // إذا لم يكن هناك رقم هاتف من العميل، استخدم الرقم المخزن في الفاتورة
    if (widget.invoice.customerPhone.isNotEmpty &&
        widget.invoice.customerPhone.length >= 10) {
      return widget.invoice.customerPhone;
    }

    return '';
  }

  /// تنظيف وتنسيق رقم الهاتف
  String _formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      return '';
    }

    // تنظيف رقم الهاتف من الرموز غير الرقمية
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من أن رقم الهاتف صحيح
    if (cleaned.isEmpty) {
      return '';
    }

    // إزالة الأصفار من البداية إذا كانت موجودة
    while (cleaned.startsWith('0') && cleaned.length > 1) {
      cleaned = cleaned.substring(1);
    }

    // إزالة الرموز الدولية إذا كانت موجودة
    if (cleaned.startsWith('00')) {
      cleaned = cleaned.substring(2);
    } else if (cleaned.startsWith('+')) {
      cleaned = cleaned.substring(1);
    }

    // التحقق من أن الرقم يحتوي على 10 أرقام على الأقل
    if (cleaned.length < 10) {
      return '';
    }

    // إذا كان الرقم يبدأ بـ 20 (مصر) وطوله 12 رقم، اتركه كما هو
    if (cleaned.startsWith('20') && cleaned.length == 12) {
      return cleaned;
    }

    // إذا كان الرقم 10 أرقام، أضف رمز مصر
    if (cleaned.length == 10) {
      return '20$cleaned';
    }

    // إذا كان الرقم 11 رقم ويبدأ بـ 1، أضف رمز مصر
    if (cleaned.length == 11 && cleaned.startsWith('1')) {
      return '20${cleaned.substring(1)}';
    }

    // في الحالات الأخرى، أضف رمز مصر إذا لم يكن موجوداً
    if (!cleaned.startsWith('20')) {
      cleaned = '20$cleaned';
    }

    return cleaned;
  }

  void _copyToClipboard() async {
    try {
      final message = await _buildInvoiceMessage();

      // إغلاق النافذة المنبثقة أولاً
      Navigator.pop(context);

      // نسخ النص إلى الحافظة
      await Clipboard.setData(ClipboardData(text: message));

      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تم نسخ نص الفاتورة إلى الحافظة',
                    style: const TextStyle(fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // إغلاق النافذة المنبثقة في حالة الخطأ
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      _showErrorSnackBar('حدث خطأ أثناء نسخ النص: $e');
    }
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر المشاركة
          Expanded(
            child: PopupMenuButton<String>(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.share, color: Colors.white),
                    SizedBox(width: 8),
                    Text(
                      'مشاركة',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    Icon(Icons.arrow_drop_down, color: Colors.white),
                  ],
                ),
              ),
              onSelected: (value) {
                switch (value) {
                  case 'whatsapp':
                    _shareViaWhatsApp();
                    break;
                  case 'sms':
                    _shareViaSMS();
                    break;
                  case 'copy':
                    _copyToClipboard();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'whatsapp',
                  child: Row(
                    children: [
                      const Icon(Icons.chat_bubble, color: Color(0xFF25D366)),
                      const SizedBox(width: 8),
                      const Text('مشاركة عبر WhatsApp'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'sms',
                  child: Row(
                    children: [
                      const Icon(Icons.sms, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text('مشاركة عبر SMS'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'copy',
                  child: Row(
                    children: [
                      const Icon(Icons.copy, color: Colors.purple),
                      const SizedBox(width: 8),
                      const Text('نسخ النص'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // زر الحذف (إذا كان مسموحاً)
          if (widget.invoice.canDelete)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showDeleteConfirmation(),
                icon: const Icon(Icons.delete, color: Colors.white),
                label: const Text(
                  'حذف',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() async {
    // التحقق من حماية الحذف
    final isDeleteProtected = await _appSettings
        .isPasswordRequiredForInvoiceDelete();

    if (isDeleteProtected) {
      // استخدام حوار الحماية
      showDeleteProtectionDialog(
        context: context,
        title: 'حماية حذف الفاتورة',
        message:
            'حذف الفاتورة محمي بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة.',
        itemName: 'الفاتورة رقم ${widget.invoice.invoiceNumber}',
        onConfirm: () {
          _deleteInvoice();
        },
      );
    } else {
      // استخدام حوار التأكيد العادي
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text(
              'تأكيد الحذف',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'هل أنت متأكد من حذف الفاتورة رقم ${widget.invoice.invoiceNumber}؟\nلا يمكن التراجع عن هذا الإجراء.',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _deleteInvoice();
                },
                child: const Text(
                  'حذف',
                  style: TextStyle(
                    color: Colors.red,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  void _deleteInvoice() async {
    try {
      await _invoiceService.deleteInvoice(widget.invoice.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'تم حذف الفاتورة بنجاح',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى الحذف
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في حذف الفاتورة: $e',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildCollectionsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.payment, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'سجل التحصيلات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Text(
                  '${_collections.length} تحصيل',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_isLoadingCollections) ...[
            const Center(child: CircularProgressIndicator()),
          ] else if (_collections.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.2)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'لا توجد تحصيلات مسجلة لهذه الفاتورة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // ملخص التحصيلات
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.2)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.summarize, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'ملخص التحصيلات',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إجمالي المحصل',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              '${_getTotalCollected().toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'آخر تحصيل',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              _getLastCollectionDate(),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قائمة التحصيلات المفصلة
            ..._collections
                .map((collection) => _buildCollectionItem(collection))
                .toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildCollectionItem(CollectionModel collection) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صف العنوان والتاريخ
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(Icons.payment, color: Colors.green, size: 16),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تحصيل رقم ${collection.id.substring(0, 8)}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      collection.formattedCollectionDate,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${collection.collectionAmount.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    collection.formattedCollectionTime,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // تفاصيل التحصيل
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.withOpacity(0.2)),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      'المبلغ المحصل:',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${collection.collectionAmount.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'المبلغ المدفوع سابقاً:',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${collection.previousPaidAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'المبلغ المتبقي بعد التحصيل:',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${collection.remainingAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: collection.remainingAmount > 0
                            ? Colors.orange
                            : Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الملاحظات إذا وجدت
          if (collection.notes != null && collection.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.withOpacity(0.2)),
              ),
              child: Row(
                children: [
                  Icon(Icons.note, color: Colors.blue[600], size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      collection.notes!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getTotalCollected() {
    return _collections.fold(
      0.0,
      (sum, collection) => sum + collection.collectionAmount,
    );
  }

  String _getLastCollectionDate() {
    if (_collections.isEmpty) return 'لا يوجد';

    final lastCollection = _collections.reduce(
      (a, b) => a.collectionDate.isAfter(b.collectionDate) ? a : b,
    );

    return lastCollection.formattedCollectionDate;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
