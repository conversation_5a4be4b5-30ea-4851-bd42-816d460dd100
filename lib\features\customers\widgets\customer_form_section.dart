import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/customer_activity_types.dart';
import '../../../models/customer_model.dart';

class CustomerFormSection extends StatelessWidget {
  final TextEditingController nameController;
  final TextEditingController activityController;
  final TextEditingController phone1Controller;
  final TextEditingController phone2Controller;
  final TextEditingController taxNumberController;
  final TextEditingController notesController;
  final CustomerType selectedType;
  final ValueChanged<CustomerType> onTypeChanged;

  const CustomerFormSection({
    super.key,
    required this.nameController,
    required this.activityController,
    required this.phone1Controller,
    required this.phone2Controller,
    required this.taxNumberController,
    required this.notesController,
    required this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: AppColors.primary, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'بيانات العميل',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildTextField(
            controller: nameController,
            label: 'اسم العميل',
            hint: 'أدخل اسم العميل',
            isRequired: true,
            icon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'اسم العميل مطلوب';
              }
              if (value.trim().length < 2) {
                return 'اسم العميل يجب أن يكون أكثر من حرفين';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          _buildCustomerTypeCheckboxes(),
          SizedBox(height: 16.h),
          _buildActivityDropdown(context),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: phone1Controller,
            label: 'رقم الهاتف الأساسي',
            hint: '01xxxxxxxxx',
            isRequired: true,
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'رقم الهاتف الأساسي مطلوب';
              }
              if (!RegExp(r'^01[0-9]{9}$').hasMatch(value.trim())) {
                return 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 01 ويتكون من 11 رقم)';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: phone2Controller,
            label: 'رقم الهاتف الثانوي',
            hint: '01xxxxxxxxx (اختياري)',
            icon: Icons.phone_android,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                if (!RegExp(r'^01[0-9]{9}$').hasMatch(value.trim())) {
                  return 'رقم الهاتف غير صحيح';
                }
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: taxNumberController,
            label: 'الرقم الضريبي',
            hint: 'أدخل الرقم الضريبي (اختياري)',
            icon: Icons.receipt,
            keyboardType: TextInputType.text,
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: notesController,
            label: 'ملاحظات',
            hint: 'أضف أي ملاحظات إضافية...',
            icon: Icons.note,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    bool isRequired = false,
    IconData? icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: 4.w),
              Text(
                '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.error,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          textAlign: TextAlign.right,
          textDirection: TextDirection.rtl,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enableInteractiveSelection: true,
          obscureText: false,
          autocorrect: false,
          enableSuggestions: false,
          readOnly: false,
          enabled: true,
          showCursor: true,
          cursorColor: AppColors.primary,
          cursorWidth: 2.0,
          cursorRadius: const Radius.circular(1.0),
          cursorHeight: 20.0,
          cursorOpacityAnimates: true,
          style: TextStyle(fontSize: 14.sp, fontFamily: 'Cairo'),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            isDense: true,
            prefixIcon: icon != null
                ? Icon(icon, color: AppColors.textSecondary, size: 20.sp)
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.error),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildCustomerTypeCheckboxes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'نوع العميل',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              '*',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.error,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  'موزع',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
                value: selectedType == CustomerType.distributor,
                onChanged: (value) {
                  if (value == true) {
                    onTypeChanged(CustomerType.distributor);
                  }
                },
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
                activeColor: AppColors.primary,
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  'مكتب طبي',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
                value:
                    selectedType == CustomerType.medicalOfficeA ||
                    selectedType == CustomerType.medicalOfficeB,
                onChanged: (value) {
                  if (value == true) {
                    onTypeChanged(CustomerType.medicalOfficeA);
                  }
                },
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
                activeColor: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getCustomerTypeText(CustomerType type) {
    switch (type) {
      case CustomerType.distributor:
        return 'موزع';
      case CustomerType.medicalOfficeA:
        return 'مكتب طبي أ';
      case CustomerType.medicalOfficeB:
        return 'مكتب طبي ب';
      case CustomerType.majorClient:
        return 'عميل كبير';
    }
  }

  Widget _buildActivityDropdown(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'نوع النشاط',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            Text(
              ' (اختياري)',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.border),
          ),
          child: DropdownButtonFormField<String>(
            value: activityController.text.isNotEmpty
                ? activityController.text
                : null,
            decoration: InputDecoration(
              hintText: 'اختر نوع النشاط',
              hintStyle: TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              prefixIcon: Icon(
                Icons.business,
                color: AppColors.textSecondary,
                size: 20.sp,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            items: [
              DropdownMenuItem<String>(
                value: null,
                child: Text(
                  'اختر نوع النشاط',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
              ...CustomerActivityTypes.getActivityNames().map((activity) {
                return DropdownMenuItem<String>(
                  value: activity,
                  child: Text(
                    activity,
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                );
              }),
              DropdownMenuItem<String>(
                value: 'custom',
                child: Text(
                  'أخرى - إدخال مخصص',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
            onChanged: (value) {
              if (value == 'custom') {
                _showCustomActivityDialog(context);
              } else {
                activityController.text = value ?? '';
              }
            },
            validator: (value) {
              // No validation required as it's optional
              return null;
            },
            dropdownColor: Colors.white,
            icon: Icon(Icons.arrow_drop_down, color: AppColors.primary),
            isExpanded: true,
            style: TextStyle(
              fontSize: 14.sp,
              fontFamily: 'Cairo',
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  void _showCustomActivityDialog(BuildContext context) {
    final customController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إدخال نوع نشاط مخصص',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: TextField(
          controller: customController,
          textAlign: TextAlign.right,
          textDirection: TextDirection.rtl,
          decoration: InputDecoration(
            hintText: 'أدخل نوع النشاط',
            hintStyle: TextStyle(fontFamily: 'Cairo'),
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (customController.text.trim().isNotEmpty) {
                activityController.text = customController.text.trim();
                Navigator.pop(context);
              }
            },
            child: Text(
              'حفظ',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
          ),
        ],
      ),
    );
  }
}
