# ميزة تصدير الفواتير ومشاركتها على واتس أب

## نظرة عامة
تم إضافة ميزة جديدة لتصدير الفواتير كملفات PDF ومشاركتها على واتس أب مع فتح التطبيق تلقائياً على رقم العميل.

## الميزات المضافة

### 1. تصدير الفاتورة كملف PDF
- إنشاء ملف PDF احترافي للفاتورة
- تصميم جميل يتضمن:
  - رأس الفاتورة مع معلومات الشركة
  - معلومات العميل
  - جدول المنتجات
  - ملخص الفاتورة
  - تذييل الفاتورة

### 2. مشاركة الفاتورة على واتس أب
- مشاركة ملف PDF مع رسالة مخصصة
- فتح واتس أب تلقائياً على رقم العميل
- رسالة تلقائية تتضمن:
  - اسم العميل
  - رقم الفاتورة
  - المبلغ الإجمالي
  - التاريخ

### 3. طباعة الفاتورة
- طباعة مباشرة للفاتورة
- دعم جميع أحجام الورق

### 4. تصدير ومشاركة متكامل
- دمج عملية التصدير والمشاركة في خطوة واحدة
- تجربة مستخدم محسنة

## الملفات المضافة/المحدثة

### 1. `lib/services/invoice_export_service.dart` (جديد)
خدمة متكاملة لتصدير الفواتير تتضمن:
- `exportInvoiceToPdf()` - تصدير الفاتورة كملف PDF
- `shareInvoiceOnWhatsApp()` - مشاركة الفاتورة على واتس أب
- `openWhatsAppOnCustomerPhone()` - فتح واتس أب على رقم العميل
- `exportAndShareInvoice()` - تصدير ومشاركة متكامل
- `printInvoice()` - طباعة الفاتورة

### 2. `lib/features/invoices/screens/invoice_details_screen.dart` (محدث)
تم إضافة أزرار جديدة:
- **تصدير PDF**: لتصدير الفاتورة كملف PDF
- **طباعة**: لطباعة الفاتورة مباشرة
- **مشاركة على واتس أب**: لمشاركة الفاتورة على واتس أب
- **تصدير ومشاركة**: لدمج العمليتين

## كيفية الاستخدام

### 1. تصدير الفاتورة كملف PDF
1. انتقل إلى شاشة تفاصيل الفاتورة
2. اضغط على زر "تصدير PDF"
3. سيتم إنشاء ملف PDF وحفظه في مجلد مؤقت
4. ستظهر رسالة تأكيد بنجاح العملية

### 2. مشاركة الفاتورة على واتس أب
1. انتقل إلى شاشة تفاصيل الفاتورة
2. اضغط على زر "مشاركة على واتس أب"
3. سيتم إنشاء ملف PDF
4. سيتم فتح واتس أب تلقائياً على رقم العميل
5. ستظهر رسالة مخصصة مع الملف المرفق

### 3. طباعة الفاتورة
1. انتقل إلى شاشة تفاصيل الفاتورة
2. اضغط على زر "طباعة"
3. سيتم فتح نافذة الطباعة
4. اختر الطابعة والإعدادات المطلوبة

### 4. تصدير ومشاركة متكامل
1. انتقل إلى شاشة تفاصيل الفاتورة
2. اضغط على زر "تصدير ومشاركة"
3. سيتم تصدير الفاتورة ومشاركتها تلقائياً
4. ستظهر رسالة تأكيد بنجاح العملية

## المتطلبات

### المكتبات المطلوبة
```yaml
dependencies:
  pdf: ^3.11.1
  printing: ^5.13.2
  path_provider: ^2.1.4
  share_plus: ^7.2.1
  url_launcher: ^6.3.1
```

### الأذونات المطلوبة (Android)
```xml
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

## الميزات التقنية

### 1. معالجة أرقام الهواتف
- تنظيف تلقائي لأرقام الهواتف
- إضافة رمز البلد (+20) تلقائياً
- دعم صيغ مختلفة للأرقام

### 2. إنشاء ملفات PDF
- تصميم احترافي ومتجاوب
- دعم النصوص العربية
- ألوان وأيقونات جذابة
- تخطيط منظم وواضح

### 3. مشاركة الملفات
- دعم جميع تطبيقات المشاركة
- رسائل مخصصة
- مواضيع واضحة للملفات

### 4. معالجة الأخطاء
- رسائل خطأ واضحة
- معالجة الاستثناءات
- توجيه المستخدم في حالة الفشل

## أمثلة الاستخدام

### مثال 1: تصدير فاتورة بسيطة
```dart
final exportService = InvoiceExportService();
final pdfFile = await exportService.exportInvoiceToPdf(invoice);
if (pdfFile != null) {
  print('تم تصدير الفاتورة: ${pdfFile.path}');
}
```

### مثال 2: مشاركة فاتورة على واتس أب
```dart
final exportService = InvoiceExportService();
final success = await exportService.exportAndShareInvoice(invoice);
if (success) {
  print('تم تصدير ومشاركة الفاتورة بنجاح');
}
```

### مثال 3: فتح واتس أب على رقم العميل
```dart
final exportService = InvoiceExportService();
await exportService.openWhatsAppOnCustomerPhone(
  invoice.customerPhone,
  message: 'مرحباً، هذه فاتورتك الجديدة',
);
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل في إنشاء ملف PDF
- تأكد من وجود مساحة كافية على الجهاز
- تحقق من صلاحيات الكتابة
- تأكد من تثبيت مكتبة PDF

#### 2. فشل في مشاركة الملف
- تحقق من وجود تطبيقات مشاركة
- تأكد من صلاحيات التخزين
- تحقق من اتصال الإنترنت

#### 3. فشل في فتح واتس أب
- تأكد من تثبيت واتس أب
- تحقق من صحة رقم الهاتف
- تأكد من وجود اتصال بالإنترنت

## التطوير المستقبلي

### ميزات مقترحة
1. **إرسال عبر البريد الإلكتروني**: إضافة خيار إرسال الفاتورة عبر البريد
2. **حفظ في السحابة**: رفع الفواتير إلى Google Drive أو Dropbox
3. **قوالب مخصصة**: إضافة قوالب مختلفة للفواتير
4. **توقيع رقمي**: إضافة توقيع رقمي للفواتير
5. **باركود/QR**: إضافة باركود أو رمز QR للفواتير

### تحسينات تقنية
1. **ضغط الملفات**: تقليل حجم ملفات PDF
2. **ذاكرة التخزين المؤقت**: تحسين الأداء
3. **معالجة متوازية**: تسريع عملية التصدير
4. **نسخ احتياطية**: حفظ نسخ من الفواتير المصدرة

## الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك أسئلة حول هذه الميزة، يرجى:
1. مراجعة هذا الدليل
2. فحص رسائل الخطأ
3. التأكد من تثبيت جميع المكتبات المطلوبة
4. التواصل مع فريق التطوير

## الخلاصة

تم إضافة ميزة شاملة ومتقدمة لتصدير الفواتير ومشاركتها على واتس أب، مما يوفر:
- تجربة مستخدم محسنة
- كفاءة في العمل
- تتبع أفضل للفواتير
- تواصل محسن مع العملاء

هذه الميزة تجعل عملية إدارة الفواتير أكثر احترافية وفعالية.
