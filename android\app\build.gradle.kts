plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.atlas2"
    compileSdk = 35
    ndkVersion = "27.0.12077973"
    
    // حل مشكلة Kotlin incremental compilation
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
        freeCompilerArgs += listOf("-Xskip-prerelease-check")
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.atlas2"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        
        // تحسينات لسرعة التطبيق
        multiDexEnabled = true
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            // Enable code shrinking and resource shrinking for smaller APKs
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                file("proguard-rules.pro")
            )
            // TODO: Replace with your release signing config when ready
            signingConfig = signingConfigs.getByName("debug")
            
            // تحسينات إضافية للأداء
            isDebuggable = false
            isJniDebuggable = false
            isRenderscriptDebuggable = false
            isPseudoLocalesEnabled = false
            isZipAlignEnabled = true
            
            // تحسينات إضافية لتقليل الحجم
            isCrunchPngs = true
            isJniDebuggable = false
            isRenderscriptDebuggable = false
            isPseudoLocalesEnabled = false
            isZipAlignEnabled = true
            isDebuggable = false
        }
        
        debug {
            // تحسينات للتطوير
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            // إزالة applicationIdSuffix و versionNameSuffix ليعمل على النسخة المثبتة
            // applicationIdSuffix = ".debug"
            // versionNameSuffix = "-debug"
        }
    }

    buildFeatures {
        buildConfig = true
        // تحسينات للأداء
        viewBinding = false
        dataBinding = false
    }
    
    // تحسينات لسرعة البناء
    dexOptions {
        preDexLibraries = true
        maxProcessCount = 8
        javaMaxHeapSize = "4g"
    }
    
    // تحسينات للأداء
    packagingOptions {
        exclude("META-INF/DEPENDENCIES")
        exclude("META-INF/LICENSE")
        exclude("META-INF/LICENSE.txt")
        exclude("META-INF/license.txt")
        exclude("META-INF/NOTICE")
        exclude("META-INF/NOTICE.txt")
        exclude("META-INF/notice.txt")
        exclude("META-INF/ASL2.0")
        exclude("META-INF/*.kotlin_module")
    }
}

flutter {
    source = "../.."
}

dependencies {
    // تحسينات للأداء
    implementation("androidx.multidex:multidex:2.0.1")
}
