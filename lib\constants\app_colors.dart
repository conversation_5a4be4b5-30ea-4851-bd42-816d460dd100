import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية - أزرق خفيف جداً
  static const Color primary = Color(0xFF4FC3F7); // أزرق فاتح جداً
  static const Color primaryLight = Color(0xFF81D4FA);
  static const Color primaryDark = Color(0xFF29B6F6);

  static const Color secondary = Color(0xFF81D4FA); // أزرق فاتح جداً
  static const Color secondaryLight = Color(0xFFB3E5FC);
  static const Color secondaryDark = Color(0xFF4FC3F7);

  static const Color accent = Color(0xFFFF9800); // برتقالي
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFE65100);

  // ألوان الخلفية
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);

  // ألوان النص
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // ألوان الحدود
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocus = Color(0xFF2196F3);
  static const Color borderError = Color(0xFFF44336);

  // ألوان الظلال
  static const Color shadow = Color(0x1A000000);
  static const Color shadowDark = Color(0x33000000);

  // الوضع المظلم
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkSurfaceVariant = Color(0xFF2D2D2D);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFBDBDBD);
  static const Color darkTextHint = Color(0xFF757575);

  // الدرجات الشفافة
  static const Color overlay = Color(0x80000000);
  static const Color shimmer = Color(0xFFE0E0E0);

  // ألوان خاصة بالتطبيق


  static const Color returns = Color(0xFFD32F2F);
  static const Color inventory = Color(0xFF7B1FA2);
  static const Color customers = Color(0xFF0288D1);
  static const Color products = Color(0xFFE64A19);
  static const Color reports = Color(0xFF5E35B1);

  // تدرجات الألوان - أزرق خفيف
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [surface, surfaceVariant],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient blueGradient = LinearGradient(
    colors: [Color(0xFFE3F2FD), Color(0xFFBBDEFB)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
