import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../models/sales_model.dart';
import '../../../models/invoice_model.dart';
import '../../../services/sales_service.dart';
import '../../../services/customer_service.dart';
import '../../../services/collection_service.dart';
import '../../../utils/screen_utils.dart';
import '../../../widgets/app_drawer.dart';
import '../widgets/sales_summary_card.dart';
import '../widgets/sales_filter_dialog.dart';
import '../widgets/sales_list_item.dart';
import '../widgets/sales_export_dialog.dart';

class SalesScreen extends StatefulWidget {
  const SalesScreen({super.key});

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen> {
  final SalesService _salesService = SalesService();
  final CustomerService _customerService = CustomerService();
  final CollectionService _collectionService = CollectionService();

  List<SalesModel> _sales = [];
  List<SalesModel> _filteredSales = [];
  Map<String, dynamic> _statistics = {};
  double _totalPendingCollections = 0.0; // إضافة متغير لمبالغ التحصيل المستحقة

  bool _isLoading = true;
  bool _isRefreshing = false;

  // فلاتر
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomerId;
  InvoiceStatus? _selectedStatus;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!_isRefreshing) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      await Future.wait([
        _loadSales(),
        _loadStatistics(),
        _loadPendingCollections(),
      ]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isRefreshing = false;
        });
      }
    }
  }

  Future<void> _loadSales() async {
    try {
      final sales = await _salesService.getAllSales();
      if (mounted) {
        setState(() {
          _sales = sales;
          _applyFilters();
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المبيعات: $e');
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await _salesService.getSalesStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }
  }

  // إضافة دالة جديدة لحساب مبالغ التحصيل المستحقة
  Future<void> _loadPendingCollections() async {
    try {
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalAmount = 0.0;

      for (final invoice in pendingInvoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _totalPendingCollections = totalAmount;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مبالغ التحصيل: $e');
    }
  }

  void _applyFilters() {
    _filteredSales = _sales.where((sale) {
      // فلتر التاريخ
      if (_startDate != null && sale.invoiceDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && sale.invoiceDate.isAfter(_endDate!)) {
        return false;
      }

      // فلتر العميل
      if (_selectedCustomerId != null &&
          sale.customerId != _selectedCustomerId) {
        return false;
      }

      // فلتر الحالة
      if (_selectedStatus != null && sale.status != _selectedStatus) {
        return false;
      }

      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return sale.customerName.toLowerCase().contains(query) ||
            sale.invoiceNumber.toLowerCase().contains(query);
      }

      return true;
    }).toList();
  }

  Future<void> _showFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => SalesFilterDialog(
        startDate: _startDate,
        endDate: _endDate,
        selectedCustomerId: _selectedCustomerId,
        selectedStatus: _selectedStatus,
      ),
    );

    if (result != null) {
      setState(() {
        _startDate = result['startDate'];
        _endDate = result['endDate'];
        _selectedCustomerId = result['customerId'];
        _selectedStatus = result['status'];
      });
      _applyFilters();
    }
  }

  Future<void> _showExportDialog() async {
    await showDialog(
      context: context,
      builder: (context) =>
          SalesExportDialog(sales: _filteredSales, statistics: _statistics),
    );
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
      _selectedCustomerId = null;
      _selectedStatus = null;
      _searchQuery = '';
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);

    return Scaffold(
      backgroundColor: Colors.white,
      drawer: const AppDrawer(),
      appBar: AppBar(
        title: const Text(
          'المبيعات',
          style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _showExportDialog,
            tooltip: 'تصدير المبيعات',
          ),
          // زر الفلترة
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة المبيعات',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _isRefreshing = true;
          });
          await _loadData();
        },
        child: SingleChildScrollView(
          child: Column(
            children: [
              // شريط البحث
              Container(
                padding: EdgeInsets.all(isSmallScreen ? 12.w : 16.w),
                child: TextField(
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _applyFilters();
                  },
                  decoration: InputDecoration(
                    hintText: 'البحث في المبيعات...',
                    hintStyle: TextStyle(
                      fontFamily: 'Cairo',
                      color: Colors.grey.shade600,
                    ),
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchQuery = '';
                              });
                              _applyFilters();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.primary),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
              ),

              // عرض الفلاتر النشطة
              if (_startDate != null ||
                  _endDate != null ||
                  _selectedCustomerId != null ||
                  _selectedStatus != null)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12.w : 16.w,
                    vertical: 8.h,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              if (_startDate != null || _endDate != null)
                                Container(
                                  margin: EdgeInsets.only(right: 8.w),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 12.w,
                                    vertical: 6.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20.r),
                                    border: Border.all(
                                      color: AppColors.primary.withOpacity(0.3),
                                    ),
                                  ),
                                  child: Text(
                                    'التاريخ: ${_startDate != null ? DateFormat('dd/MM/yyyy').format(_startDate!) : 'البداية'} - ${_endDate != null ? DateFormat('dd/MM/yyyy').format(_endDate!) : 'النهاية'}',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.primary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ),
                              if (_selectedStatus != null)
                                Container(
                                  margin: EdgeInsets.only(right: 8.w),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 12.w,
                                    vertical: 6.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20.r),
                                    border: Border.all(
                                      color: Colors.blue.withOpacity(0.3),
                                    ),
                                  ),
                                  child: Text(
                                    'الحالة: ${_selectedStatus!.displayName}',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.blue,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: _clearFilters,
                        child: Text(
                          'مسح الفلاتر',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // بطاقة ملخص المبيعات
              if (!_isLoading)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12.w : 16.w,
                    vertical: 8.h,
                  ),
                  child: SalesSummaryCard(
                    statistics: _statistics,
                    filteredCount: _filteredSales.length,
                    totalCount: _sales.length,
                  ),
                ),

              // بطاقة مبالغ التحصيل المستحقة
              if (!_isLoading && _totalPendingCollections > 0)
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12.w : 16.w,
                    vertical: 8.h,
                  ),
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.orange.shade50, Colors.orange.shade100],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: Colors.orange.shade200),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.shade200.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade200,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Icon(
                          Icons.payment,
                          color: Colors.orange.shade800,
                          size: 20.sp,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مبالغ التحصيل المستحقة',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                            Text(
                              'من جميع الفواتير',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 12.sp,
                                color: Colors.orange.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade200,
                          borderRadius: BorderRadius.circular(16.r),
                          border: Border.all(color: Colors.orange.shade300),
                        ),
                        child: Text(
                          NumberFormat.currency(
                            locale: 'ar_SA',
                            symbol: 'ر.س ',
                            decimalDigits: 0,
                          ).format(_totalPendingCollections),
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade800,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // قائمة المبيعات
              _isLoading
                  ? Container(
                      height: 300.h,
                      child: const Center(child: CircularProgressIndicator()),
                    )
                  : _filteredSales.isEmpty
                  ? Container(
                      height: 300.h,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 64.sp,
                              color: Colors.grey.shade400,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              _sales.isEmpty
                                  ? 'لا توجد مبيعات'
                                  : 'لا توجد نتائج',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.grey.shade600,
                                fontFamily: 'Cairo',
                              ),
                            ),
                            if (_sales.isNotEmpty)
                              TextButton(
                                onPressed: _clearFilters,
                                child: Text(
                                  'مسح الفلاتر',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.primary,
                                    fontFamily: 'Cairo',
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    )
                  : Column(
                      children: _filteredSales.map((sale) {
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 12.w : 16.w,
                            vertical: 4.h,
                          ),
                          child: SalesListItem(
                            sale: sale,
                            onTap: () {
                              // يمكن إضافة تفاصيل المبيعات هنا
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'فاتورة رقم: ${sale.invoiceNumber}',
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      }).toList(),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
