import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';

class CustomerSearchBar extends StatelessWidget {
  final Function(String) onSearchChanged;

  const CustomerSearchBar({super.key, required this.onSearchChanged});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25.r),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        elevation: 0,
        child: TextField(
          textAlign: TextAlign.right,
          style: TextStyle(
            fontSize: 16.sp,
            fontFamily: 'Cairo',
            color: AppColors.textPrimary,
          ),
          decoration: InputDecoration(
            hintText: 'البحث في العملاء...',
            hintStyle: TextStyle(
              color: AppColors.textSecondary.withValues(alpha: 0.7),
              fontFamily: 'Cairo',
              fontSize: 15.sp,
            ),
            prefixIcon: Container(
              margin: EdgeInsets.only(left: 8.w),
              child: Icon(Icons.search, color: AppColors.primary, size: 22.sp),
            ),
            suffixIcon: Container(
              margin: EdgeInsets.only(right: 8.w),
              child: Icon(
                Icons.tune,
                color: AppColors.textSecondary.withValues(alpha: 0.6),
                size: 20.sp,
              ),
            ),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
              horizontal: 20.w,
              vertical: 16.h,
            ),
            filled: true,
            fillColor: Colors.white,
            isDense: true,
          ),
          onChanged: onSearchChanged,
        ),
      ),
    );
  }
}
