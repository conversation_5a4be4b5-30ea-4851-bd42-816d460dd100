# نظام تفاصيل التحصيلات في الفاتورة

## نظرة عامة

تم تطوير نظام تفصيلي لعرض سجل التحصيلات في شاشة تفاصيل الفاتورة، حيث يتم تسجيل كل عملية تحصيل بالتفصيل مع التاريخ والوقت بشكل منظم وواضح.

## الميزات الرئيسية

### 1. عرض سجل التحصيلات المفصل
- **قسم مخصص**: قسم جديد في شاشة تفاصيل الفاتورة يعرض جميع التحصيلات
- **عرض مشروط**: يظهر فقط إذا كان هناك مبلغ مدفوع في الفاتورة
- **ترتيب زمني**: التحصيلات مرتبة حسب التاريخ (الأحدث أولاً)

### 2. ملخص التحصيلات
- **إجمالي المحصل**: مجموع جميع التحصيلات للفاتورة
- **آخر تحصيل**: تاريخ آخر عملية تحصيل
- **عدد التحصيلات**: عرض عدد عمليات التحصيل

### 3. تفاصيل كل تحصيل
- **رقم التحصيل**: معرف فريد لكل تحصيل
- **التاريخ والوقت**: تاريخ ووقت التحصيل بالتفصيل
- **المبلغ المحصل**: المبلغ الذي تم تحصيله
- **المبلغ المدفوع سابقاً**: إجمالي المدفوع قبل هذا التحصيل
- **المبلغ المتبقي**: المبلغ المتبقي بعد التحصيل
- **الملاحظات**: ملاحظات إضافية إن وجدت

## التصميم والواجهة

### الألوان المستخدمة
- **الأخضر**: للتحصيلات والمبالغ المحصلة
- **الأزرق**: للملخصات والمعلومات الإضافية
- **البرتقالي**: للمبالغ المتبقية
- **الرمادي**: للنصوص الثانوية

### العناصر البصرية
- **أيقونات**: أيقونات واضحة لكل قسم
- **بطاقات**: تصميم بطاقات منفصلة لكل تحصيل
- **حدود ملونة**: حدود بألوان مختلفة لتمييز المحتوى
- **خلفيات شفافة**: خلفيات ملونة شفافة للتمييز

## البنية التقنية

### الملفات المعدلة
1. **`lib/features/invoices/screens/invoice_details_screen.dart`**
   - إضافة استيراد `CollectionService` و `CollectionModel`
   - إضافة متغيرات حالة للتحصيلات
   - إضافة دالة `_loadCollections()`
   - إضافة قسم `_buildCollectionsSection()`
   - إضافة دالة `_buildCollectionItem()`
   - إضافة دوال مساعدة للحسابات

### المتغيرات الجديدة
```dart
List<CollectionModel> _collections = [];
bool _isLoadingCollections = true;
```

### الدوال الجديدة
- `_loadCollections()`: تحميل التحصيلات من قاعدة البيانات
- `_buildCollectionsSection()`: بناء قسم التحصيلات
- `_buildCollectionItem()`: بناء عنصر تحصيل واحد
- `_getTotalCollected()`: حساب إجمالي المحصل
- `_getLastCollectionDate()`: الحصول على تاريخ آخر تحصيل

## كيفية العمل

### 1. تحميل البيانات
```dart
Future<void> _loadCollections() async {
  try {
    final collections = await _collectionService.getCollectionsByInvoiceId(widget.invoice.id);
    setState(() {
      _collections = collections;
      _isLoadingCollections = false;
    });
  } catch (e) {
    setState(() {
      _isLoadingCollections = false;
    });
    print('خطأ في تحميل التحصيلات: $e');
  }
}
```

### 2. عرض القسم
```dart
// سجل التحصيلات
if (widget.invoice.paidAmount > 0) ...[
  _buildCollectionsSection(),
  const SizedBox(height: 16),
],
```

### 3. بناء عنصر التحصيل
```dart
Widget _buildCollectionItem(CollectionModel collection) {
  return Container(
    // تصميم البطاقة مع جميع التفاصيل
    child: Column(
      children: [
        // العنوان والتاريخ
        Row(children: [...]),
        // تفاصيل التحصيل
        Container(children: [...]),
        // الملاحظات
        if (collection.notes != null) Container(children: [...]),
      ],
    ),
  );
}
```

## معلومات العرض

### لكل تحصيل يتم عرض:
1. **رقم التحصيل**: أول 8 أحرف من المعرف الفريد
2. **التاريخ**: تاريخ التحصيل (مثال: 15/12/2024)
3. **الوقت**: وقت التحصيل (مثال: 02:30 م)
4. **المبلغ المحصل**: المبلغ الذي تم تحصيله
5. **المبلغ المدفوع سابقاً**: إجمالي المدفوع قبل هذا التحصيل
6. **المبلغ المتبقي**: المبلغ المتبقي بعد التحصيل
7. **الملاحظات**: ملاحظات إضافية إن وجدت

### ملخص التحصيلات:
- **إجمالي المحصل**: مجموع جميع التحصيلات
- **آخر تحصيل**: تاريخ آخر عملية تحصيل
- **عدد التحصيلات**: العدد الإجمالي للتحصيلات

## حالات العرض

### 1. حالة التحميل
```dart
if (_isLoadingCollections) ...[
  const Center(
    child: CircularProgressIndicator(),
  ),
]
```

### 2. حالة عدم وجود تحصيلات
```dart
else if (_collections.isEmpty) ...[
  Container(
    child: Row(
      children: [
        Icon(Icons.info_outline),
        Text('لا توجد تحصيلات مسجلة لهذه الفاتورة'),
      ],
    ),
  ),
]
```

### 3. حالة وجود تحصيلات
```dart
else ...[
  // ملخص التحصيلات
  Container(children: [...]),
  // قائمة التحصيلات المفصلة
  ..._collections.map((collection) => _buildCollectionItem(collection)).toList(),
]
```

## التكامل مع النظام الحالي

### 1. قاعدة البيانات
- يستخدم نفس جدول `collections` الموجود
- لا يحتاج لتعديلات إضافية في قاعدة البيانات

### 2. الخدمات
- يستخدم `CollectionService` الموجود
- يستخدم `getCollectionsByInvoiceId()` للحصول على التحصيلات

### 3. النماذج
- يستخدم `CollectionModel` الموجود
- يستخدم جميع الخصائص المتاحة في النموذج

## المزايا

### 1. الشفافية
- عرض واضح لجميع عمليات التحصيل
- تتبع كامل للمدفوعات والمتبقي

### 2. التنظيم
- ترتيب زمني للتحصيلات
- تصميم منظم وواضح

### 3. التفاصيل
- عرض كامل لجميع المعلومات المهمة
- إمكانية رؤية التقدم في الدفع

### 4. سهولة الاستخدام
- واجهة بسيطة وواضحة
- ألوان مميزة للتمييز السريع

## الاستخدام

### للمستخدم:
1. افتح شاشة تفاصيل الفاتورة
2. انتقل لأسفل الصفحة
3. ستجد قسم "سجل التحصيلات" إذا كان هناك مبلغ مدفوع
4. اطلع على تفاصيل كل تحصيل
5. راجع ملخص التحصيلات

### للمطور:
1. تأكد من وجود `CollectionService` و `CollectionModel`
2. تأكد من أن قاعدة البيانات تحتوي على جدول `collections`
3. يمكن تخصيص التصميم والألوان حسب الحاجة

## التطوير المستقبلي

### ميزات مقترحة:
1. **تصدير سجل التحصيلات**: إمكانية تصدير السجل كـ PDF
2. **فلترة التحصيلات**: فلترة حسب التاريخ أو المبلغ
3. **إحصائيات متقدمة**: رسوم بيانية للتحصيلات
4. **إشعارات**: إشعارات للتحصيلات الجديدة
5. **طباعة**: إمكانية طباعة سجل التحصيلات

### تحسينات مقترحة:
1. **أيقونات متحركة**: إضافة حركات بسيطة
2. **ألوان مخصصة**: إمكانية تخصيص الألوان
3. **تصميم متجاوب**: تحسين العرض على الشاشات المختلفة
4. **بحث**: إمكانية البحث في التحصيلات

## الخلاصة

تم تطوير نظام تفصيلي ومتكامل لعرض سجل التحصيلات في شاشة تفاصيل الفاتورة، حيث يوفر:

- **عرض منظم**: ترتيب زمني واضح للتحصيلات
- **تفاصيل شاملة**: جميع المعلومات المهمة لكل تحصيل
- **تصميم جذاب**: واجهة بسيطة وسهلة الاستخدام
- **تكامل كامل**: يعمل مع النظام الحالي بدون تعديلات إضافية

هذا النظام يوفر الشفافية الكاملة في تتبع عمليات التحصيل ويجعل من السهل على المستخدم فهم حالة الدفع للفاتورة.
