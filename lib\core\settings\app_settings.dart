import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج إعدادات التطبيق الشامل
class AppSettings {
  static const String _settingsKey = 'app_settings';

  // Performance Settings
  bool _splashOptimization = true;
  bool _enableLazyLoading = true;
  bool _enableImageCompression = true;
  bool _enablePrecache = true;
  bool _removeDebugLogs = true;
  bool _enableTreeShaking = true;

  // UI/UX Settings
  bool _enableAdaptiveTheme = false;
  bool _enableRTL = true;
  bool _enableResponsiveLayout = true;
  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';
  bool _enableBiometricLogin = false;

  // Security Settings
  bool _enableAppSignatureVerification = true;
  bool _enableDatabaseEncryption = true;
  bool _disableScreenCapture = true;
  bool _enableRootDetection = true;
  bool _enableCodeObfuscation = true;
  String? _userPassword = '1234'; // كلمة مرور افتراضية
  
  // Invoice Protection Settings
  bool _enableInvoiceProtection = true;
  bool _requirePasswordForInvoiceAccess = false;
  bool _requirePasswordForInvoiceEdit = true;
  bool _requirePasswordForInvoiceDelete = true;

  // Data Settings
  bool _enableBackupRestore = true;
  int _cacheExpirationDays = 7;
  bool _enableDataCompression = true;
  bool _enableSecureStorage = true;

  // Connectivity Settings
  bool _enableOfflineMode = true;
  bool _enablePushNotifications = true;
  bool _enableAutoSync = true;
  int _syncIntervalMinutes = 30;

  // UX Settings
  bool _showOnboarding = true;
  bool _enableStatePersistence = true;
  bool _enablePullToRefresh = true;
  bool _enableInAppNotifications = true;

  // Build Settings
  String _appVersion = '1.0.0';
  int _buildNumber = 1;
  bool _enableAppSigning = true;
  bool _enableMinification = true;

  // Getters
  bool get splashOptimization => _splashOptimization;
  bool get enableLazyLoading => _enableLazyLoading;
  bool get enableImageCompression => _enableImageCompression;
  bool get enablePrecache => _enablePrecache;
  bool get removeDebugLogs => _removeDebugLogs;
  bool get enableTreeShaking => _enableTreeShaking;

  bool get enableAdaptiveTheme => _enableAdaptiveTheme;
  bool get enableRTL => _enableRTL;
  bool get enableResponsiveLayout => _enableResponsiveLayout;
  String get selectedLanguage => _selectedLanguage;
  String get selectedTheme => _selectedTheme;
  bool get enableBiometricLogin => _enableBiometricLogin;

  bool get enableAppSignatureVerification => _enableAppSignatureVerification;
  bool get enableDatabaseEncryption => _enableDatabaseEncryption;
  bool get disableScreenCapture => _disableScreenCapture;
  bool get enableRootDetection => _enableRootDetection;
  bool get enableCodeObfuscation => _enableCodeObfuscation;
  String? get userPassword => _userPassword;
  
  // Invoice Protection Getters
  bool get enableInvoiceProtection => _enableInvoiceProtection;
  bool get requirePasswordForInvoiceAccess => _requirePasswordForInvoiceAccess;
  bool get requirePasswordForInvoiceEdit => _requirePasswordForInvoiceEdit;
  bool get requirePasswordForInvoiceDelete => _requirePasswordForInvoiceDelete;

  bool get enableBackupRestore => _enableBackupRestore;
  int get cacheExpirationDays => _cacheExpirationDays;
  bool get enableDataCompression => _enableDataCompression;
  bool get enableSecureStorage => _enableSecureStorage;

  bool get enableOfflineMode => _enableOfflineMode;
  bool get enablePushNotifications => _enablePushNotifications;
  bool get enableAutoSync => _enableAutoSync;
  int get syncIntervalMinutes => _syncIntervalMinutes;

  bool get showOnboarding => _showOnboarding;
  bool get enableStatePersistence => _enableStatePersistence;
  bool get enablePullToRefresh => _enablePullToRefresh;
  bool get enableInAppNotifications => _enableInAppNotifications;

  String get appVersion => _appVersion;
  int get buildNumber => _buildNumber;
  bool get enableAppSigning => _enableAppSigning;
  bool get enableMinification => _enableMinification;

  // Setters
  set splashOptimization(bool value) => _splashOptimization = value;
  set enableLazyLoading(bool value) => _enableLazyLoading = value;
  set enableImageCompression(bool value) => _enableImageCompression = value;
  set enablePrecache(bool value) => _enablePrecache = value;
  set removeDebugLogs(bool value) => _removeDebugLogs = value;
  set enableTreeShaking(bool value) => _enableTreeShaking = value;

  set enableAdaptiveTheme(bool value) => _enableAdaptiveTheme = value;
  set enableRTL(bool value) => _enableRTL = value;
  set enableResponsiveLayout(bool value) => _enableResponsiveLayout = value;
  set selectedLanguage(String value) => _selectedLanguage = value;
  set selectedTheme(String value) => _selectedTheme = value;
  set enableBiometricLogin(bool value) => _enableBiometricLogin = value;

  set enableAppSignatureVerification(bool value) =>
      _enableAppSignatureVerification = value;
  set enableDatabaseEncryption(bool value) => _enableDatabaseEncryption = value;
  set disableScreenCapture(bool value) => _disableScreenCapture = value;
  set enableRootDetection(bool value) => _enableRootDetection = value;
  set enableCodeObfuscation(bool value) => _enableCodeObfuscation = value;
  set userPassword(String? value) => _userPassword = value;
  
  // Invoice Protection Setters
  set enableInvoiceProtection(bool value) => _enableInvoiceProtection = value;
  set requirePasswordForInvoiceAccess(bool value) => _requirePasswordForInvoiceAccess = value;
  set requirePasswordForInvoiceEdit(bool value) => _requirePasswordForInvoiceEdit = value;
  set requirePasswordForInvoiceDelete(bool value) => _requirePasswordForInvoiceDelete = value;

  set enableBackupRestore(bool value) => _enableBackupRestore = value;
  set cacheExpirationDays(int value) => _cacheExpirationDays = value;
  set enableDataCompression(bool value) => _enableDataCompression = value;
  set enableSecureStorage(bool value) => _enableSecureStorage = value;

  set enableOfflineMode(bool value) => _enableOfflineMode = value;
  set enablePushNotifications(bool value) => _enablePushNotifications = value;
  set enableAutoSync(bool value) => _enableAutoSync = value;
  set syncIntervalMinutes(int value) => _syncIntervalMinutes = value;

  set showOnboarding(bool value) => _showOnboarding = value;
  set enableStatePersistence(bool value) => _enableStatePersistence = value;
  set enablePullToRefresh(bool value) => _enablePullToRefresh = value;
  set enableInAppNotifications(bool value) => _enableInAppNotifications = value;

  set appVersion(String value) => _appVersion = value;
  set buildNumber(int value) => _buildNumber = value;
  set enableAppSigning(bool value) => _enableAppSigning = value;
  set enableMinification(bool value) => _enableMinification = value;

  Map<String, dynamic> toJson() {
    return {
      'splashOptimization': _splashOptimization,
      'enableLazyLoading': _enableLazyLoading,
      'enableImageCompression': _enableImageCompression,
      'enablePrecache': _enablePrecache,
      'removeDebugLogs': _removeDebugLogs,
      'enableTreeShaking': _enableTreeShaking,
      'enableAdaptiveTheme': _enableAdaptiveTheme,
      'enableRTL': _enableRTL,
      'enableResponsiveLayout': _enableResponsiveLayout,
      'selectedLanguage': _selectedLanguage,
      'selectedTheme': _selectedTheme,
      'enableBiometricLogin': _enableBiometricLogin,
      'enableAppSignatureVerification': _enableAppSignatureVerification,
      'enableDatabaseEncryption': _enableDatabaseEncryption,
      'disableScreenCapture': _disableScreenCapture,
      'enableRootDetection': _enableRootDetection,
      'enableCodeObfuscation': _enableCodeObfuscation,
      'userPassword': _userPassword,
      'enableInvoiceProtection': _enableInvoiceProtection,
      'requirePasswordForInvoiceAccess': _requirePasswordForInvoiceAccess,
      'requirePasswordForInvoiceEdit': _requirePasswordForInvoiceEdit,
      'requirePasswordForInvoiceDelete': _requirePasswordForInvoiceDelete,
      'enableBackupRestore': _enableBackupRestore,
      'cacheExpirationDays': _cacheExpirationDays,
      'enableDataCompression': _enableDataCompression,
      'enableSecureStorage': _enableSecureStorage,
      'enableOfflineMode': _enableOfflineMode,
      'enablePushNotifications': _enablePushNotifications,
      'enableAutoSync': _enableAutoSync,
      'syncIntervalMinutes': _syncIntervalMinutes,
      'showOnboarding': _showOnboarding,
      'enableStatePersistence': _enableStatePersistence,
      'enablePullToRefresh': _enablePullToRefresh,
      'enableInAppNotifications': _enableInAppNotifications,
      'appVersion': _appVersion,
      'buildNumber': _buildNumber,
      'enableAppSigning': _enableAppSigning,
      'enableMinification': _enableMinification,
    };
  }

  void fromJson(Map<String, dynamic> json) {
    _splashOptimization = json['splashOptimization'] ?? true;
    _enableLazyLoading = json['enableLazyLoading'] ?? true;
    _enableImageCompression = json['enableImageCompression'] ?? true;
    _enablePrecache = json['enablePrecache'] ?? true;
    _removeDebugLogs = json['removeDebugLogs'] ?? true;
    _enableTreeShaking = json['enableTreeShaking'] ?? true;
    _enableAdaptiveTheme = json['enableAdaptiveTheme'] ?? false;
    _enableRTL = json['enableRTL'] ?? true;
    _enableResponsiveLayout = json['enableResponsiveLayout'] ?? true;
    _selectedLanguage = json['selectedLanguage'] ?? 'ar';
    _selectedTheme = json['selectedTheme'] ?? 'light';
    _enableBiometricLogin = json['enableBiometricLogin'] ?? false;
    _enableAppSignatureVerification =
        json['enableAppSignatureVerification'] ?? true;
    _enableDatabaseEncryption = json['enableDatabaseEncryption'] ?? true;
    _disableScreenCapture = json['disableScreenCapture'] ?? true;
    _enableRootDetection = json['enableRootDetection'] ?? true;
    _enableCodeObfuscation = json['enableCodeObfuscation'] ?? true;
    _userPassword = json['userPassword'];
    _enableInvoiceProtection = json['enableInvoiceProtection'] ?? false;
    _requirePasswordForInvoiceAccess = json['requirePasswordForInvoiceAccess'] ?? false;
    _requirePasswordForInvoiceEdit = json['requirePasswordForInvoiceEdit'] ?? true;
    _requirePasswordForInvoiceDelete = json['requirePasswordForInvoiceDelete'] ?? true;
    _enableBackupRestore = json['enableBackupRestore'] ?? true;
    _cacheExpirationDays = json['cacheExpirationDays'] ?? 7;
    _enableDataCompression = json['enableDataCompression'] ?? true;
    _enableSecureStorage = json['enableSecureStorage'] ?? true;
    _enableOfflineMode = json['enableOfflineMode'] ?? true;
    _enablePushNotifications = json['enablePushNotifications'] ?? true;
    _enableAutoSync = json['enableAutoSync'] ?? true;
    _syncIntervalMinutes = json['syncIntervalMinutes'] ?? 30;
    _showOnboarding = json['showOnboarding'] ?? true;
    _enableStatePersistence = json['enableStatePersistence'] ?? true;
    _enablePullToRefresh = json['enablePullToRefresh'] ?? true;
    _enableInAppNotifications = json['enableInAppNotifications'] ?? true;
    _appVersion = json['appVersion'] ?? '1.0.0';
    _buildNumber = json['buildNumber'] ?? 1;
    _enableAppSigning = json['enableAppSigning'] ?? true;
    _enableMinification = json['enableMinification'] ?? true;
  }

  Future<void> save() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_settingsKey, jsonEncode(toJson()));
  }

  Future<void> load() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_settingsKey);
    if (settingsJson != null) {
      try {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        fromJson(settingsMap);
      } catch (e) {
        debugPrint('خطأ في تحميل الإعدادات: $e');
      }
    }
  }

  // دوال إدارة كلمة المرور
  Future<String?> getUserPassword() async {
    if (_userPassword != null) {
      return _userPassword;
    }
    
    // محاولة تحميل كلمة المرور من التخزين المحلي
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('user_password');
    } catch (e) {
      debugPrint('خطأ في تحميل كلمة المرور: $e');
      return null;
    }
  }

  Future<void> setUserPassword(String password) async {
    _userPassword = password;
    
    // حفظ كلمة المرور في التخزين المحلي
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_password', password);
      await save(); // حفظ في الإعدادات العامة
    } catch (e) {
      debugPrint('خطأ في حفظ كلمة المرور: $e');
    }
  }

  Future<void> clearUserPassword() async {
    _userPassword = null;
    
    // حذف كلمة المرور من التخزين المحلي
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_password');
      await save(); // حفظ في الإعدادات العامة
    } catch (e) {
      debugPrint('خطأ في حذف كلمة المرور: $e');
    }
  }

  // دوال حماية الفواتير
  Future<bool> isInvoiceProtectionEnabled() async {
    return _enableInvoiceProtection;
  }

  Future<bool> isPasswordRequiredForInvoiceAccess() async {
    return _enableInvoiceProtection && _requirePasswordForInvoiceAccess;
  }

  Future<bool> isPasswordRequiredForInvoiceEdit() async {
    return _enableInvoiceProtection && _requirePasswordForInvoiceEdit;
  }

  Future<bool> isPasswordRequiredForInvoiceDelete() async {
    return _enableInvoiceProtection && _requirePasswordForInvoiceDelete;
  }

  Future<void> configureInvoiceProtection({
    bool requirePasswordForAccess = false,
    bool requirePasswordForEdit = true,
    bool requirePasswordForDelete = true,
  }) async {
    _enableInvoiceProtection = true;
    _requirePasswordForInvoiceAccess = requirePasswordForAccess;
    _requirePasswordForInvoiceEdit = requirePasswordForEdit;
    _requirePasswordForInvoiceDelete = requirePasswordForDelete;
    await save();
  }

  Future<void> disableInvoiceProtection() async {
    _enableInvoiceProtection = false;
    _requirePasswordForInvoiceAccess = false;
    _requirePasswordForInvoiceEdit = false;
    _requirePasswordForInvoiceDelete = false;
    await save();
  }

  void resetToDefaults() {
    _splashOptimization = true;
    _enableLazyLoading = true;
    _enableImageCompression = true;
    _enablePrecache = true;
    _removeDebugLogs = true;
    _enableTreeShaking = true;
    _enableAdaptiveTheme = false;
    _enableRTL = true;
    _enableResponsiveLayout = true;
    _selectedLanguage = 'ar';
    _selectedTheme = 'light';
    _enableBiometricLogin = false;
    _enableAppSignatureVerification = true;
    _enableDatabaseEncryption = true;
    _disableScreenCapture = true;
    _enableRootDetection = true;
    _enableCodeObfuscation = true;
    _userPassword = '1234'; // كلمة مرور افتراضية
    _enableInvoiceProtection = true;
    _requirePasswordForInvoiceAccess = false;
    _requirePasswordForInvoiceEdit = true;
    _requirePasswordForInvoiceDelete = true;
    _enableBackupRestore = true;
    _cacheExpirationDays = 7;
    _enableDataCompression = true;
    _enableSecureStorage = true;
    _enableOfflineMode = true;
    _enablePushNotifications = true;
    _enableAutoSync = true;
    _syncIntervalMinutes = 30;
    _showOnboarding = true;
    _enableStatePersistence = true;
    _enablePullToRefresh = true;
    _enableInAppNotifications = true;
    _appVersion = '1.0.0';
    _buildNumber = 1;
    _enableAppSigning = true;
    _enableMinification = true;
  }
}
