import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/invoice_model.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/screen_utils.dart';

class InvoiceCard extends StatelessWidget {
  final InvoiceModel invoice;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final Function(InvoiceStatus)? onStatusUpdate;
  final VoidCallback? onCollect; // إضافة callback للتحصيل

  const InvoiceCard({
    Key? key,
    required this.invoice,
    this.onTap,
    this.onDelete,
    this.onStatusUpdate,
    this.onCollect, // إضافة parameter للتحصيل
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);

    return Card(
      elevation: isSmallScreen ? 1 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(isSmallScreen ? 8.r : 12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(isSmallScreen ? 8.r : 12.r),
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 12.w : 16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8.r : 12.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, AppColors.primary.withOpacity(0.05)],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice.invoiceNumber,
                          style: TextStyle(
                            fontSize: ScreenUtils.getResponsiveFontSize(
                              context,
                              smallSize: 16.sp,
                              mediumSize: 18.sp,
                              largeSize: 20.sp,
                            ),
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 2.h : 4.h),
                        Text(
                          invoice.customerName,
                          style: TextStyle(
                            fontSize: ScreenUtils.getResponsiveFontSize(
                              context,
                              smallSize: 14.sp,
                              mediumSize: 16.sp,
                              largeSize: 18.sp,
                            ),
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),

              SizedBox(height: isSmallScreen ? 8.h : 12.h),

              // تفاصيل الفاتورة - صف واحد للشاشات الصغيرة
              if (isSmallScreen) ...[
                _buildCompactInfoRow(context),
              ] else ...[
                // صفين للشاشات المتوسطة والكبيرة
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        icon: Icons.calendar_today,
                        label: 'التاريخ',
                        value: invoice.formattedDate,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        icon: Icons.shopping_cart,
                        label: 'المنتجات',
                        value: '${invoice.itemsCount} منتج',
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        icon: Icons.phone,
                        label: 'الهاتف',
                        value: invoice.customerPhone.isNotEmpty
                            ? invoice.customerPhone
                            : 'غير محدد',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        icon: Icons.attach_money,
                        label: 'الإجمالي',
                        value: '${invoice.total.toStringAsFixed(2)} ر.س',
                        valueColor: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],

              // عرض الخصم إذا كان موجود
              if (invoice.discountAmount > 0 ||
                  invoice.discountPercentage > 0) ...[
                SizedBox(height: isSmallScreen ? 6.h : 8.h),
                if (isSmallScreen)
                  _buildCompactDiscountRow(context)
                else
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          icon: Icons.discount,
                          label: 'الخصم',
                          value: invoice.discountPercentage > 0
                              ? '${invoice.discountPercentage.toStringAsFixed(1)}%'
                              : '${invoice.discountAmount.toStringAsFixed(2)} ر.س',
                          valueColor: Colors.red[600],
                        ),
                      ),
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          icon: Icons.calculate,
                          label: 'قبل الخصم',
                          value: '${invoice.subtotal.toStringAsFixed(2)} ر.س',
                          valueColor: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
              ],

              // عرض معلومات التحصيل
              SizedBox(height: isSmallScreen ? 6.h : 8.h),

              // إضافة معلومات الدفع المحسنة
              if (invoice.paidAmount > 0) ...[
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 8.w : 10.w),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.payment,
                        size: isSmallScreen ? 14.sp : 16.sp,
                        color: Colors.blue,
                      ),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المدفوع: ${invoice.paidAmount.toStringAsFixed(2)} ر.س',
                              style: TextStyle(
                                fontSize: ScreenUtils.getResponsiveFontSize(
                                  context,
                                  smallSize: 12.sp,
                                  mediumSize: 13.sp,
                                  largeSize: 14.sp,
                                ),
                                fontWeight: FontWeight.w600,
                                color: Colors.green[700],
                              ),
                            ),
                            Text(
                              'المتبقي: ${invoice.calculateRemainingAmount().toStringAsFixed(2)} ر.س',
                              style: TextStyle(
                                fontSize: ScreenUtils.getResponsiveFontSize(
                                  context,
                                  smallSize: 11.sp,
                                  mediumSize: 12.sp,
                                  largeSize: 13.sp,
                                ),
                                color: invoice.calculateRemainingAmount() > 0
                                    ? Colors.orange[700]
                                    : Colors.green[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              SizedBox(height: isSmallScreen ? 12.h : 16.h),

              // أزرار الإجراءات
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    // إظهار حالة الدفع فقط إذا كانت الفاتورة ملغية
    if (invoice.status == InvoiceStatus.cancelled) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Text(
          'ملغي',
          style: TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.w600,
            fontSize: 12.sp,
          ),
        ),
      );
    }

    // إظهار معلومات الدفع بدلاً من حالة الدفع
    String paymentInfo;
    Color paymentColor;

    if (invoice.isFullyPaid) {
      paymentInfo = 'تم الدفع بالكامل';
      paymentColor = Colors.green;
    } else if (invoice.paidAmount > 0) {
      paymentInfo = 'مدفوع جزئياً';
      paymentColor = Colors.orange;
    } else {
      paymentInfo = 'لم يتم الدفع';
      paymentColor = Colors.grey;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: paymentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: paymentColor.withOpacity(0.3)),
      ),
      child: Text(
        paymentInfo,
        style: TextStyle(
          color: paymentColor,
          fontWeight: FontWeight.w600,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16.sp, color: Colors.grey[600]),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: ScreenUtils.getResponsiveFontSize(
                    context,
                    smallSize: 14.sp,
                    mediumSize: 14.sp,
                    largeSize: 14.sp,
                  ),
                  fontWeight: FontWeight.w500,
                  color: valueColor ?? Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompactInfoRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(
            context,
            icon: Icons.calendar_today,
            label: 'التاريخ',
            value: invoice.formattedDate,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildInfoItem(
            context,
            icon: Icons.shopping_cart,
            label: 'المنتجات',
            value: '${invoice.itemsCount} منتج',
          ),
        ),
      ],
    );
  }

  Widget _buildCompactDiscountRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(
            context,
            icon: Icons.discount,
            label: 'الخصم',
            value: invoice.discountPercentage > 0
                ? '${invoice.discountPercentage.toStringAsFixed(1)}%'
                : '${invoice.discountAmount.toStringAsFixed(2)} ر.س',
            valueColor: Colors.red[600],
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildInfoItem(
            context,
            icon: Icons.calculate,
            label: 'قبل الخصم',
            value: '${invoice.subtotal.toStringAsFixed(2)} ر.س',
            valueColor: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // زر التحصيل - يظهر فقط للفواتير السابقة التي لم يتم دفعها بالكامل
        if (_shouldShowCollectButton())
          Expanded(
            child: _buildActionButton(
              icon: Icons.payment,
              label: 'تحصيل',
              color: AppColors.primary,
              onPressed: onCollect,
            ),
          ),

        // زر الحذف
        if (invoice.canDelete && onDelete != null)
          Expanded(
            child: _buildActionButton(
              icon: Icons.delete,
              label: 'حذف',
              color: Colors.red,
              onPressed: onDelete,
            ),
          ),
      ],
    );
  }

  // دالة للتحقق من إمكانية عرض زر التحصيل
  bool _shouldShowCollectButton() {
    // التحقق من أن الفاتورة ليست ملغية
    if (invoice.status == InvoiceStatus.cancelled) {
      return false;
    }

    // التحقق من أن الفاتورة لم يتم دفعها بالكامل
    if (invoice.isFullyPaid) {
      return false;
    }

    // التحقق من أن الفاتورة سابقة (أكثر من 15 دقيقة من الإنشاء)
    final now = DateTime.now();
    final timeDifference = now.difference(invoice.createdAt);
    final fifteenMinutes = const Duration(minutes: 15);

    return timeDifference >= fifteenMinutes;
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16.sp),
      label: Text(label, style: TextStyle(fontSize: 12.sp)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
      ),
    );
  }
}
