import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../models/sales_model.dart';
import '../../../models/invoice_model.dart';
import '../../../utils/screen_utils.dart';

class SalesListItem extends StatelessWidget {
  final SalesModel sale;
  final VoidCallback? onTap;

  const SalesListItem({super.key, required this.sale, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey.shade200, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 16.w : 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف الأول: رقم الفاتورة والتاريخ
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'فاتورة رقم: ${sale.invoiceNumber}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 14.sp : 16.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                              fontFamily: 'Cairo',
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            'التاريخ: ${sale.formattedDate}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 12.sp : 13.sp,
                              color: Colors.grey.shade600,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    ),
                    // حالة الدفع
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(sale.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: _getStatusColor(sale.status).withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        sale.paymentStatus,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 10.sp : 11.sp,
                          color: _getStatusColor(sale.status),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),

                // الصف الثاني: اسم العميل
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: isSmallScreen ? 16.sp : 18.sp,
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        sale.customerName,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 13.sp : 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade800,
                          fontFamily: 'Cairo',
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                if (sale.customerPhone != null &&
                    sale.customerPhone!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      Icon(
                        Icons.phone,
                        size: isSmallScreen ? 14.sp : 16.sp,
                        color: Colors.grey.shade500,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        sale.customerPhone!,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 12.sp : 13.sp,
                          color: Colors.grey.shade600,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ],
                SizedBox(height: 12.h),

                // الصف الثالث: المبالغ
                Row(
                  children: [
                    Expanded(
                      child: _AmountItem(
                        title: 'الإجمالي',
                        amount: sale.totalAmount,
                        color: AppColors.primary,
                        isSmallScreen: isSmallScreen,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _AmountItem(
                        title: 'المدفوع',
                        amount: sale.paidAmount,
                        color: Colors.green,
                        isSmallScreen: isSmallScreen,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _AmountItem(
                        title: 'المتبقي',
                        amount: sale.remainingAmount,
                        color: Colors.orange,
                        isSmallScreen: isSmallScreen,
                      ),
                    ),
                  ],
                ),

                // شريط التقدم (نسبة الدفع)
                if (sale.totalAmount > 0) ...[
                  SizedBox(height: 8.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'نسبة الدفع',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 11.sp : 12.sp,
                              color: Colors.grey.shade600,
                              fontFamily: 'Cairo',
                            ),
                          ),
                          Text(
                            '${sale.paymentPercentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 11.sp : 12.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      LinearProgressIndicator(
                        value: sale.paymentPercentage / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          sale.isFullyPaid ? Colors.green : Colors.orange,
                        ),
                        minHeight: 4.h,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partial:
        return Colors.blue;
      case InvoiceStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

class _AmountItem extends StatelessWidget {
  final String title;
  final double amount;
  final Color color;
  final bool isSmallScreen;

  const _AmountItem({
    required this.title,
    required this.amount,
    required this.color,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 8.w : 10.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isSmallScreen ? 10.sp : 11.sp,
              color: color,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            NumberFormat.currency(
              locale: 'ar_SA',
              symbol: 'ر.س ',
              decimalDigits: 2,
            ).format(amount),
            style: TextStyle(
              fontSize: isSmallScreen ? 12.sp : 13.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
