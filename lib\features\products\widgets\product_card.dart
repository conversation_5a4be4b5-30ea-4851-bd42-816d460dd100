import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';

class ProductCard extends StatefulWidget {
  final ProductModel product;
  final Function(String) onAction;

  const ProductCard({super.key, required this.product, required this.onAction});

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onAction('details');
      },
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [AppColors.surface, AppColors.surface.withOpacity(0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: _isPressed
                    ? AppColors.primary.withOpacity(0.3)
                    : AppColors.shadow,
                blurRadius: _isPressed ? 12 : 8,
                offset: Offset(0, _isPressed ? 4 : 6),
              ),
            ],
            border: Border.all(
              color: AppColors.primary.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة - اسم المنتج والفئة
              _buildCardHeader(),

              // محتوى البطاقة - المعلومات الأساسية
              _buildCardContent(),

              // شريط الإجراءات
              _buildActionBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الفئة مع تأثير متحرك
          Hero(
            tag: 'category_${widget.product.id}',
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryDark],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                _getCategoryIcon(widget.product.category),
                color: AppColors.textOnPrimary,
                size: 24,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // اسم المنتج والفئة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.product.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.product.categoryDisplayName,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // حالة المخزون
          _buildStockStatus(),
        ],
      ),
    );
  }

  Widget _buildCardContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // الصف الأول - الكود والباركود
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكود',
                  widget.product.code,
                  Icons.qr_code_scanner,
                  AppColors.info,
                ),
              ),
              if (widget.product.hasBarcode) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    'الباركود',
                    widget.product.barcode!,
                    Icons.qr_code_2,
                    AppColors.secondary,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 20),

          // الصف الثاني - الأسعار مع تصميم محسن
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.success.withOpacity(0.1),
                  AppColors.success.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppColors.success.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildPriceItem(
                    'سعر البيع',
                    widget.product.price,
                    Icons.sell,
                    AppColors.success,
                  ),
                ),
                Container(width: 1, height: 40, color: AppColors.border),
                Expanded(
                  child: _buildPriceItem(
                    'سعر الشراء',
                    widget.product.cost,
                    Icons.shopping_cart,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // الصف الثالث - الكمية والوحدة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكمية المتوفرة',
                  '${widget.product.quantity} ${widget.product.unitDisplayName}',
                  Icons.inventory_2,
                  _getQuantityColor(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'الحد الأدنى',
                  '${widget.product.minQuantity} ${widget.product.unitDisplayName}',
                  Icons.warning_amber,
                  AppColors.warning,
                ),
              ),
            ],
          ),

          // هامش الربح مع تصميم محسن
          if (widget.product.cost > 0) ...[
            const SizedBox(height: 20),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _getProfitMarginColor().withOpacity(0.1),
                    _getProfitMarginColor().withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getProfitMarginColor().withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.trending_up,
                    color: _getProfitMarginColor(),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'هامش الربح: ${widget.product.profitMargin.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _getProfitMarginColor(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant.withOpacity(0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            'تفاصيل',
            Icons.info_outline,
            AppColors.info,
            () => widget.onAction('details'),
          ),
          _buildActionButton(
            'تعديل',
            Icons.edit_outlined,
            AppColors.warning,
            () => widget.onAction('edit'),
          ),
          _buildActionButton(
            'بيع',
            Icons.point_of_sale,
            AppColors.success,
            () => widget.onAction('sell'),
          ),
          _buildActionButton(
            'كمية',
            Icons.inventory_2_outlined,
            AppColors.primary,
            () => widget.onAction('adjust_quantity'),
          ),
          _buildActionButton(
            'حذف',
            Icons.delete_outline,
            AppColors.error,
            () => widget.onAction('delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceItem(
    String label,
    double price,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${price.toStringAsFixed(0)} ${AppStrings.currencySymbol}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 6),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildStockStatus() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (widget.product.quantity == 0) {
      statusColor = AppColors.error;
      statusIcon = Icons.error_outline;
      statusText = 'نفذ';
    } else if (widget.product.isLowStock) {
      statusColor = AppColors.warning;
      statusIcon = Icons.warning_amber_outlined;
      statusText = 'منخفض';
    } else {
      statusColor = AppColors.success;
      statusIcon = Icons.check_circle_outline;
      statusText = 'متوفر';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withOpacity(0.5), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: statusColor.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 18),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'devices':
        return Icons.medical_services;
      case 'consumables':
        return Icons.medical_information;
      case 'sterilization':
        return Icons.cleaning_services;
      case 'laboratory':
        return Icons.science;
      case 'general_supplies':
        return Icons.inventory;
      default:
        return Icons.category;
    }
  }

  Color _getQuantityColor() {
    if (widget.product.quantity == 0) return AppColors.error;
    if (widget.product.isLowStock) return AppColors.warning;
    return AppColors.success;
  }

  Color _getProfitMarginColor() {
    final margin = widget.product.profitMargin;
    if (margin >= 50) return AppColors.success;
    if (margin >= 25) return AppColors.warning;
    return AppColors.error;
  }
}
