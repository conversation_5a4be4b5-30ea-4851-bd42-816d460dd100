import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// زر الرجوع المخصص الذي يكون دائماً ناحية اليمين
class CustomBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? color;
  final double? size;
  final String? tooltip;
  final EdgeInsetsGeometry? padding;

  const CustomBackButton({
    super.key,
    this.onPressed,
    this.color,
    this.size,
    this.tooltip,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed ?? () => Navigator.of(context).pop(),
      icon: Icon(
        Icons.arrow_back_ios,
        color: color ?? Colors.white,
        size: size ?? 24.sp,
      ),
      tooltip: tooltip ?? 'رجوع',
      padding: padding ?? EdgeInsets.zero,
    );
  }
}

/// زر الرجوع مع نص "رجوع"
class CustomBackButtonWithText extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? color;
  final String? text;
  final TextStyle? textStyle;

  const CustomBackButtonWithText({
    super.key,
    this.onPressed,
    this.color,
    this.text,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed ?? () => Navigator.of(context).pop(),
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.arrow_back_ios,
              color: color ?? Colors.white,
              size: 20.sp,
            ),
            SizedBox(width: 4.w),
            Text(
              text ?? 'رجوع',
              style: textStyle ??
                  TextStyle(
                    color: color ?? Colors.white,
                    fontSize: 16.sp,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

/// AppBar مخصص مع زر رجوع ناحية اليمين
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color backgroundColor;
  final Color textColor;
  final bool centerTitle;
  final double elevation;
  final Widget? leading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.onBackPressed,
    this.actions,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.centerTitle = true,
    this.elevation = 0,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontFamily: 'Cairo',
          color: textColor,
          fontWeight: FontWeight.bold,
          fontSize: 18.sp,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor,
      elevation: elevation,
      // زر الرجوع ناحية اليمين
      leading: leading ?? CustomBackButton(
        onPressed: onBackPressed,
        color: textColor,
      ),
      actions: actions,
      // إزالة الاتجاه التلقائي
      automaticallyImplyLeading: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
