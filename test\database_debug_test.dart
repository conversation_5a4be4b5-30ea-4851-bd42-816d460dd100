import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../lib/services/database_service.dart';
import '../lib/services/product_service.dart';
import '../lib/models/product_model.dart';

void main() {
  group('Database Debug Tests', () {
    late DatabaseService databaseService;
    late ProductService productService;

    setUpAll(() async {
      // تهيئة sqflite للاختبار
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;

      // إنشاء مجلد مؤقت للاختبار
      final tempDir = await getTemporaryDirectory();
      final testDbPath = join(tempDir.path, 'test_atlas_medical.db');

      // حذف قاعدة البيانات القديمة إذا كانت موجودة
      if (await File(testDbPath).exists()) {
        await File(testDbPath).delete();
      }
    });

    setUp(() async {
      databaseService = DatabaseService();
      productService = ProductService();
    });

    test('Test database initialization', () async {
      print('🔧 Testing database initialization...');

      try {
        final db = await databaseService.database;
        print('✅ Database initialized successfully');

        // فحص الجداول الموجودة
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table'",
        );
        print('📋 Found ${tables.length} tables:');
        for (var table in tables) {
          print('  - ${table['name']}');
        }

        // فحص جدول المنتجات
        final productsTable = tables
            .where((t) => t['name'] == 'products')
            .toList();
        if (productsTable.isNotEmpty) {
          print('✅ Products table exists');

          // فحص أعمدة جدول المنتجات
          final columns = await db.rawQuery("PRAGMA table_info(products)");
          print('📊 Products table columns:');
          for (var column in columns) {
            print('  - ${column['name']} (${column['type']})');
          }
        } else {
          print('❌ Products table not found');
        }
      } catch (e) {
        print('❌ Database initialization failed: $e');
        fail('Database initialization failed: $e');
      }
    });

    test('Test getAllProducts method', () async {
      print('🔧 Testing getAllProducts method...');

      try {
        final products = await databaseService.getAllProducts();
        print(
          '📦 DatabaseService.getAllProducts() returned ${products.length} products',
        );

        if (products.isNotEmpty) {
          print('📋 Products found:');
          for (int i = 0; i < products.length && i < 5; i++) {
            final product = products[i];
            print(
              '  ${i + 1}. ${product.name} (ID: ${product.id}, Active: ${product.isActive})',
            );
          }
        } else {
          print('⚠️ No products found in database');
        }

        print('✅ DatabaseService.getAllProducts() working correctly');
      } catch (e) {
        print('❌ Error in DatabaseService.getAllProducts() test: $e');
        fail('DatabaseService.getAllProducts() failed: $e');
      }
    });

    test('Test ProductService getAllProducts method', () async {
      print('🔧 Testing ProductService.getAllProducts() method...');

      try {
        final products = await productService.getAllProducts();
        print(
          '📦 ProductService.getAllProducts() returned ${products.length} products',
        );

        if (products.isNotEmpty) {
          print('📋 Products found:');
          for (int i = 0; i < products.length && i < 5; i++) {
            final product = products[i];
            print(
              '  ${i + 1}. ${product.name} (ID: ${product.id}, Active: ${product.isActive})',
            );
          }
        } else {
          print('⚠️ No products found via ProductService');
        }

        print('✅ ProductService.getAllProducts() working correctly');
      } catch (e) {
        print('❌ Error in ProductService.getAllProducts() test: $e');
        fail('ProductService.getAllProducts() failed: $e');
      }
    });

    test('Test adding a test product', () async {
      print('🔧 Testing adding a test product...');

      try {
        final testProduct = ProductModel(
          id: '',
          name: 'منتج اختبار',
          description: 'منتج اختبار للفحص',
          category: 'general_supplies',
          code: 'TEST001',
          price: 100.0,
          cost: 80.0,
          quantity: 50,
          minQuantity: 10,
          piecesPerCarton: 1,
          unit: 'قطعة',
          barcode: '1234567890123',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          isActive: true,
          distributorPrice: 90.0,
          officePrice: 95.0,
        );

        final result = await productService.addProduct(testProduct);
        print('📝 Add product result: $result');

        if (result) {
          print('✅ Test product added successfully');

          // التحقق من وجود المنتج
          final products = await productService.getAllProducts();
          final addedProduct = products
              .where((p) => p.name == 'منتج اختبار')
              .firstOrNull;

          if (addedProduct != null) {
            print(
              '✅ Test product found in database: ${addedProduct.name} (ID: ${addedProduct.id})',
            );
          } else {
            print('❌ Test product not found in database');
          }
        } else {
          print('❌ Failed to add test product');
        }
      } catch (e) {
        print('❌ Error in adding test product: $e');
        fail('Adding test product failed: $e');
      }
    });

    test('Test database table structure', () async {
      print('🔧 Testing database table structure...');

      try {
        final db = await databaseService.database;

        // فحص جدول المنتجات بالتفصيل
        final productsColumns = await db.rawQuery(
          "PRAGMA table_info(products)",
        );
        print('📊 Products table structure:');
        for (var column in productsColumns) {
          print(
            '  - ${column['name']} (${column['type']}) ${column['notnull'] == 1 ? 'NOT NULL' : 'NULL'} ${column['pk'] == 1 ? 'PRIMARY KEY' : ''}',
          );
        }

        // فحص عدد المنتجات
        final countResult = await db.rawQuery(
          "SELECT COUNT(*) as count FROM products",
        );
        final productCount = countResult.first['count'] as int;
        print('📦 Total products in database: $productCount');

        // فحص المنتجات النشطة
        final activeCountResult = await db.rawQuery(
          "SELECT COUNT(*) as count FROM products WHERE isActive = 1",
        );
        final activeProductCount = activeCountResult.first['count'] as int;
        print('✅ Active products in database: $activeProductCount');

        // فحص المنتجات غير النشطة
        final inactiveCountResult = await db.rawQuery(
          "SELECT COUNT(*) as count FROM products WHERE isActive = 0",
        );
        final inactiveProductCount = inactiveCountResult.first['count'] as int;
        print('❌ Inactive products in database: $inactiveProductCount');

        print('✅ Database table structure test completed');
      } catch (e) {
        print('❌ Error in database table structure test: $e');
        fail('Database table structure test failed: $e');
      }
    });
  });
}
