import 'package:flutter_test/flutter_test.dart';
import '../lib/models/collection_model.dart';
import '../lib/models/invoice_model.dart';

void main() {
  group('Invoice Collections Display Tests', () {
    test('should display collection information correctly', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      expect(collection.paymentAmount, 200.0);
      expect(collection.paymentDate, DateTime.parse('2024-01-16T14:20:00.000Z'));
    });

    test('should format collection date correctly', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      expect(collection.formattedPaymentDate, '16/1/2024');
      expect(collection.formattedPaymentTime, '02:20 م');
    });

    test('should calculate collection statistics correctly', () {
      final collections = [
        CollectionModel(
          id: 'test-1',
          invoiceId: 'invoice-123',
          invoiceNumber: 'INV-001',
          customerId: 'customer-456',
          customerName: 'أحمد محمد',
          customerPhone: '0123456789',
          invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
          invoiceTotal: 1000.0,
          previousPaidAmount: 300.0,
          paymentAmount: 200.0,
          remainingAmount: 500.0,
          paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
          notes: 'تحصيل جزئي',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
        ),
        CollectionModel(
          id: 'test-2',
          invoiceId: 'invoice-124',
          invoiceNumber: 'INV-002',
          customerId: 'customer-457',
          customerName: 'فاطمة علي',
          customerPhone: '0123456790',
          invoiceDate: DateTime.parse('2024-01-15T11:30:00.000Z'),
          invoiceTotal: 1500.0,
          previousPaidAmount: 500.0,
          paymentAmount: 300.0,
          remainingAmount: 700.0,
          paymentDate: DateTime.parse('2024-01-16T15:20:00.000Z'),
          notes: 'تحصيل جزئي',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-16T15:20:00.000Z'),
        ),
      ];

      final totalCollections = collections.fold(0.0, (sum, c) => sum + c.paymentAmount);
      expect(totalCollections, 500.0);
    });

    test('should sort collections by date correctly', () {
      final collections = [
        CollectionModel(
          id: 'test-1',
          invoiceId: 'invoice-123',
          invoiceNumber: 'INV-001',
          customerId: 'customer-456',
          customerName: 'أحمد محمد',
          customerPhone: '0123456789',
          invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
          invoiceTotal: 1000.0,
          previousPaidAmount: 300.0,
          paymentAmount: 200.0,
          remainingAmount: 500.0,
          paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
          notes: 'تحصيل جزئي',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
        ),
        CollectionModel(
          id: 'test-2',
          invoiceId: 'invoice-124',
          invoiceNumber: 'INV-002',
          customerId: 'customer-457',
          customerName: 'فاطمة علي',
          customerPhone: '0123456790',
          invoiceDate: DateTime.parse('2024-01-15T11:30:00.000Z'),
          invoiceTotal: 1500.0,
          previousPaidAmount: 500.0,
          paymentAmount: 300.0,
          remainingAmount: 700.0,
          paymentDate: DateTime.parse('2024-01-16T15:20:00.000Z'),
          notes: 'تحصيل جزئي',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-16T15:20:00.000Z'),
        ),
      ];

      collections.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
      expect(collections.first.id, 'test-2');
    });

    test('should display collection notification message correctly', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      final message = collection.notificationMessage;
      expect(message, contains('200.00 ر.س'));
      expect(message, contains('500.00 ر.س'));
      expect(message, contains('INV-001'));
      expect(message, contains('أحمد محمد'));
    });

    test('should handle multiple collections for same invoice', () {
      final collections = [
        CollectionModel(
          id: 'test-1',
          invoiceId: 'invoice-123',
          invoiceNumber: 'INV-001',
          customerId: 'customer-456',
          customerName: 'أحمد محمد',
          customerPhone: '0123456789',
          invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
          invoiceTotal: 1000.0,
          previousPaidAmount: 0.0,
          paymentAmount: 300.0,
          remainingAmount: 700.0,
          paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
          notes: 'الدفعة الأولى',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
        ),
        CollectionModel(
          id: 'test-2',
          invoiceId: 'invoice-123',
          invoiceNumber: 'INV-001',
          customerId: 'customer-456',
          customerName: 'أحمد محمد',
          customerPhone: '0123456789',
          invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
          invoiceTotal: 1000.0,
          previousPaidAmount: 300.0,
          paymentAmount: 400.0,
          remainingAmount: 300.0,
          paymentDate: DateTime.parse('2024-01-17T15:20:00.000Z'),
          notes: 'الدفعة الثانية',
          createdBy: 'admin',
          createdAt: DateTime.parse('2024-01-17T15:20:00.000Z'),
        ),
      ];

      final totalCollected = collections.fold(0.0, (sum, c) => sum + c.paymentAmount);
      expect(totalCollected, 700.0);

      final lastCollection = collections.reduce((a, b) => a.paymentDate.isAfter(b.paymentDate) ? a : b);
      expect(lastCollection.id, 'test-2');
    });
  });
}
