# تحديث جدول عناصر الفاتورة

## الميزات المضافة

تم تحديث صفحة إضافة الفاتورة لتشمل جدول متقدم لإضافة المنتجات/الخدمات مع الميزات التالية:

### 1. الإضافة السريعة للمنتجات
- زر "إضافة سريعة" لتفعيل البحث السريع
- حقل بحث يعمل بالاسم أو الكود أو الباركود
- قائمة منسدلة تعرض المنتجات المطابقة
- إمكانية إضافة المنتج مباشرة من القائمة

### 2. جدول عناصر الفاتورة
- عرض منظم للمنتجات المضافة
- أعمدة: المنتج، الكمية، السعر، الإجمالي
- إمكانية تعديل الكمية والسعر مباشرة في الجدول
- حساب تلقائي للإجمالي لكل بند

### 3. إدارة العناصر
- زر تعديل لكل بند (قلم)
- زر حذف لكل بند (سلة)
- تحديث تلقائي للحسابات عند التعديل
- تأكيد قبل الحذف

### 4. الحسابات التلقائية
- حساب الإجمالي لكل بند تلقائياً
- جمع الإجماليات الفرعية
- حساب الضريبة (14%)
- عرض الإجمالي الكلي

## كيفية الاستخدام

### إضافة منتج جديد
1. اضغط على "إضافة منتج" لفتح نافذة الإضافة الكاملة
2. أو اضغط على "إضافة سريعة" للبحث السريع
3. ابحث عن المنتج بالاسم أو الكود
4. أدخل الكمية والسعر
5. اضغط "إضافة"

### تعديل عنصر موجود
1. اضغط على أيقونة التعديل (قلم) في الصف
2. عدل الكمية أو السعر
3. اضغط على علامة الصح لحفظ التغييرات
4. أو اضغط على علامة الإغلاق للإلغاء

### حذف عنصر
1. اضغط على أيقونة الحذف (سلة) في الصف
2. أكد الحذف في النافذة المنبثقة

## الملفات المحدثة

- `lib/features/invoices/widgets/invoice_items_section.dart` - القسم الرئيسي لعناصر الفاتورة
- `lib/features/invoices/screens/add_invoice_screen.dart` - صفحة إضافة الفاتورة

## الميزات التقنية

- تصميم متجاوب يعمل على جميع أحجام الشاشات
- تحديث تلقائي للحسابات
- إدارة حالة التعديل
- تأكيدات قبل الحذف
- رسائل نجاح وتنبيهات
- دعم البحث المتقدم

## ملاحظات

- يتم حساب الضريبة تلقائياً بنسبة 14%
- يمكن تعديل السعر من القيمة الافتراضية في قاعدة البيانات
- يتم التحقق من صحة القيم قبل الحفظ
- دعم كامل للغة العربية والاتجاه RTL
