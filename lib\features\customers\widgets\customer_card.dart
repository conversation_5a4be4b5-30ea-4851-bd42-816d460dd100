import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';

class CustomerCard extends StatelessWidget {
  final CustomerModel customer;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                customer.name,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                  fontFamily: 'Cairo',
                                ),
                              ),
                            ),
                            _buildCustomerTypeChip(),
                          ],
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          customer.activity,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildActionsMenu(),
                ],
              ),
              SizedBox(height: 8.h),
              _buildContactInfo(),
              SizedBox(height: 8.h),
              _buildLocationInfo(),
              if (customer.balance != 0) ...[
                SizedBox(height: 8.h),
                _buildBalanceInfo(),
              ],
              SizedBox(height: 8.h),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerTypeChip() {
    late Color chipColor;
    late String typeText;

    switch (customer.type) {
      case CustomerType.distributor:
        chipColor = AppColors.info;
        typeText = 'موزع';
        break;
      case CustomerType.medicalOfficeA:
        chipColor = AppColors.success;
        typeText = 'مكتب طبي أ';
        break;
      case CustomerType.medicalOfficeB:
        chipColor = AppColors.warning;
        typeText = 'مكتب طبي ب';
        break;
      case CustomerType.majorClient:
        chipColor = AppColors.primary;
        typeText = 'عميل كبير';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        typeText,
        style: TextStyle(
          fontSize: 10.sp,
          color: chipColor,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildActionsMenu() {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: AppColors.textSecondary, size: 20.sp),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18.sp, color: AppColors.primary),
              SizedBox(width: 8.w),
              Text(
                'تعديل',
                style: TextStyle(fontFamily: 'Cairo', fontSize: 14.sp),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 18.sp, color: AppColors.error),
              SizedBox(width: 8.w),
              Text(
                'حذف',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14.sp,
                  color: AppColors.error,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfo() {
    return Row(
      children: [
        Icon(Icons.phone, size: 14.sp, color: AppColors.textSecondary),
        SizedBox(width: 6.w),
        Expanded(
          child: Text(
            customer.phone1 ?? '',
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        if (customer.phone2 != null && customer.phone2!.isNotEmpty) ...[
          SizedBox(width: 12.w),
          Icon(
            Icons.phone_android,
            size: 14.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(width: 6.w),
          Text(
            customer.phone2!,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocationInfo() {
    List<String> addressParts = [];
    if (customer.governorate != null && customer.governorate!.isNotEmpty) {
      addressParts.add(customer.governorate!);
    }
    if (customer.city != null && customer.city!.isNotEmpty) {
      addressParts.add(customer.city!);
    }
    if (customer.street != null && customer.street!.isNotEmpty) {
      addressParts.add(customer.street!);
    }
    
    final address = addressParts.join(', ');
    
    return Row(
      children: [
        Icon(Icons.location_on, size: 14.sp, color: AppColors.textSecondary),
        SizedBox(width: 6.w),
        Expanded(
          child: Text(
            address.isNotEmpty ? address : 'العنوان غير محدد',
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceInfo() {
    final isDebit = customer.balance > 0;
    final balanceColor = isDebit ? AppColors.error : AppColors.success;
    final balanceText = isDebit ? 'مدين' : 'دائن';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: balanceColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: balanceColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isDebit ? Icons.trending_up : Icons.trending_down,
            size: 14.sp,
            color: balanceColor,
          ),
          SizedBox(width: 6.w),
          Text(
            balanceText,
            style: TextStyle(
              fontSize: 12.sp,
              color: balanceColor,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(width: 6.w),
          Text(
            '${customer.balance.abs().toStringAsFixed(2)} جنيه',
            style: TextStyle(
              fontSize: 12.sp,
              color: balanceColor,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        if (customer.hasLocation) ...[
          Icon(Icons.map, size: 12.sp, color: AppColors.success),
          SizedBox(width: 3.w),
          Text(
            'الموقع محدد',
            style: TextStyle(
              fontSize: 10.sp,
              color: AppColors.success,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(width: 12.w),
        ],
        Icon(Icons.access_time, size: 12.sp, color: AppColors.textSecondary),
        SizedBox(width: 3.w),
        Text(
          'تم الإنشاء: ${_formatDate(customer.createdAt)}',
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.textSecondary,
            fontFamily: 'Cairo',
          ),
        ),
        const Spacer(),
        if (customer.lastOrderDate != null) ...[
          Text(
            'آخر طلب: ${_formatDate(customer.lastOrderDate!)}',
            style: TextStyle(
              fontSize: 10.sp,
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
