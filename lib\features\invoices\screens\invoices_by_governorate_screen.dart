import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/egyptian_governorates.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_service.dart';
import '../../../services/customer_service.dart';
import '../../../widgets/back_button.dart';
import 'governorate_invoices_screen.dart';

class InvoicesByGovernorateScreen extends StatefulWidget {
  const InvoicesByGovernorateScreen({super.key});

  @override
  State<InvoicesByGovernorateScreen> createState() =>
      _InvoicesByGovernorateScreenState();
}

class _InvoicesByGovernorateScreenState
    extends State<InvoicesByGovernorateScreen> {
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();

  Map<String, List<InvoiceModel>> _invoicesByGovernorate = {};
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final invoices = await _invoiceService.getAllInvoices();

      // تجميع الفواتير حسب المحافظة
      final Map<String, List<InvoiceModel>> invoicesByGovernorate = {};

      for (final governorate in EgyptianGovernorates.getAllGovernorates()) {
        invoicesByGovernorate[governorate] = [];
      }

      for (final invoice in invoices) {
        final customer = await _customerService.getCustomerById(
          invoice.customerId,
        );
        if (customer != null && customer.governorate != null) {
          if (invoicesByGovernorate.containsKey(customer.governorate)) {
            invoicesByGovernorate[customer.governorate]!.add(invoice);
          }
        }
      }

      setState(() {
        _invoicesByGovernorate = invoicesByGovernorate;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الفواتير: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.location_on,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'الفواتير حسب المحافظات',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 20,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
          ? _buildErrorWidget()
          : _buildGovernoratesList(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل الفواتير',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _errorMessage,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _loadInvoices,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGovernoratesList() {
    final governorates = EgyptianGovernorates.getAllGovernorates();

    return RefreshIndicator(
      onRefresh: _loadInvoices,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: governorates.length,
        itemBuilder: (context, index) {
          final governorate = governorates[index];
          final invoices = _invoicesByGovernorate[governorate] ?? [];

          return _buildGovernorateCard(governorate, invoices);
        },
      ),
    );
  }

  Widget _buildGovernorateCard(
    String governorate,
    List<InvoiceModel> invoices,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToGovernorateInvoices(governorate, invoices),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // أيقونة المحافظة
                Container(
                  width: 48.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primary, AppColors.primaryDark],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                SizedBox(width: 16.w),

                // معلومات المحافظة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        governorate,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                          fontFamily: 'Cairo',
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '${invoices.length} فاتورة',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ),

                // عدد الفواتير
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: invoices.isNotEmpty
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: invoices.isNotEmpty
                          ? AppColors.primary.withValues(alpha: 0.3)
                          : Colors.grey[300]!,
                    ),
                  ),
                  child: Text(
                    '${invoices.length}',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: invoices.isNotEmpty
                          ? AppColors.primary
                          : Colors.grey[600],
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                // سهم التنقل
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToGovernorateInvoices(
    String governorate,
    List<InvoiceModel> invoices,
  ) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => GovernorateInvoicesScreen(
              governorateName: governorate,
              invoices: invoices,
            ),
          ),
        )
        .then((result) {
          // إعادة تحميل البيانات عند العودة
          if (result != null) {
            _loadInvoices();
          }
        });
  }
}
