import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';
import 'screens/products_list_screen.dart';

/// شاشة تجريبية لعرض صفحة قائمة المنتجات
/// تستخدم لتوضيح كيفية دمج الصفحة في التطبيق
class DemoProductsScreen extends StatelessWidget {
  const DemoProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'تجربة صفحة المنتجات',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: AppColors.primary),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة
            _buildHeader(),

            const SizedBox(height: 32),

            // شرح الميزات
            _buildFeaturesSection(),

            const SizedBox(height: 32),

            // أزرار التجربة
            _buildDemoButtons(context),

            const SizedBox(height: 32),

            // معلومات تقنية
            _buildTechnicalInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.inventory_2,
              color: AppColors.textOnPrimary,
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'صفحة قائمة المنتجات',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.textOnPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'واجهة عصريّة واحترافيّة لإدارة المنتجات',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textOnPrimary.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الميزات الرئيسية',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),

        _buildFeatureItem(
          Icons.search,
          'البحث الفوري',
          'البحث بالاسم، الكود، أو الباركود مع نتائج فورية',
          AppColors.primary,
        ),

        _buildFeatureItem(
          Icons.filter_list,
          'تصفية متقدمة',
          'تصفية حسب الفئة، المخزون، والترتيب حسب معايير متعددة',
          AppColors.info,
        ),

        _buildFeatureItem(
          Icons.add_circle,
          'إضافة سريعة',
          'إضافة منتج جديد بسهولة مع نافذة شاملة',
          AppColors.success,
        ),

        _buildFeatureItem(
          Icons.touch_app,
          'إجراءات سريعة',
          'تعديل، حذف، بيع، وتعديل الكمية من نفس القائمة',
          AppColors.warning,
        ),

        _buildFeatureItem(
          Icons.analytics,
          'إحصائيات فورية',
          'عرض إحصائيات المخزون والمبيعات بشكل مرئي',
          AppColors.accent,
        ),
      ],
    );
  }

  Widget _buildFeatureItem(
    IconData icon,
    String title,
    String description,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDemoButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'جرب الميزات',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProductsListScreen(),
                ),
              );
            },
            icon: const Icon(Icons.play_arrow),
            label: const Text(
              'فتح صفحة المنتجات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showSampleData(context),
                icon: const Icon(Icons.data_usage),
                label: const Text('بيانات تجريبية'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: AppColors.border),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showUsageGuide(context),
                icon: const Icon(Icons.help_outline),
                label: const Text('دليل الاستخدام'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: AppColors.border),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTechnicalInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.code, color: AppColors.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'معلومات تقنية',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          _buildTechInfoItem('إطار العمل', 'Flutter'),
          _buildTechInfoItem('اللغة', 'Dart'),
          _buildTechInfoItem('التصميم', 'Material Design 3'),
          _buildTechInfoItem('دعم RTL', 'نعم - اللغة العربية'),
          _buildTechInfoItem('الأداء', 'مُحسّن للهواتف المحمولة'),
          _buildTechInfoItem('التوافق', 'Android & iOS'),
        ],
      ),
    );
  }

  Widget _buildTechInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _showSampleData(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('بيانات تجريبية'),
        content: const Text(
          'يمكنك إضافة بيانات تجريبية للمنتجات لاختبار جميع الميزات:\n\n'
          '• منتجات بأسماء مختلفة\n'
          '• أسعار متنوعة\n'
          '• كميات مختلفة\n'
          '• فئات متعددة\n'
          '• باركودات متنوعة',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showUsageGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دليل الاستخدام'),
        content: const Text(
          'كيفية استخدام صفحة المنتجات:\n\n'
          '1. اضغط على "فتح صفحة المنتجات"\n'
          '2. استخدم حقل البحث للعثور على منتج\n'
          '3. اضغط على أيقونة التصفية لتصفية النتائج\n'
          '4. اضغط على زر + لإضافة منتج جديد\n'
          '5. اضغط على أيقونات الإجراءات في كل بطاقة منتج',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
