# نظام التحصيل - أطلس للمستلزمات الطبية

## نظرة عامة

تم إضافة نظام تحصيل متكامل إلى التطبيق يتيح إدارة تحصيل المبالغ المستحقة من الفواتير بطريقة سهلة ومنظمة.

## الميزات الرئيسية

### 1. قائمة التحصيل
- عرض جميع الفواتير المستحقة للتحصيل
- عرض تفاصيل كل فاتورة (اسم العميل، رقم الفاتورة، التاريخ، المبالغ)
- البحث في الفواتير المستحقة
- إحصائيات سريعة عن الفواتير المستحقة

### 2. عملية التحصيل
- إدخال مبلغ التحصيل مع التحقق من صحة المبلغ
- إضافة ملاحظات اختيارية
- تحديث تلقائي لحالة الفاتورة
- تسجيل عملية التحصيل في قاعدة البيانات

### 3. مشاركة الإشعارات
- إرسال إشعار تحصيل عبر الواتساب
- إرسال رسالة نصية للعميل
- نص الإشعار يتضمن تفاصيل كاملة عن عملية التحصيل

### 4. قاعدة البيانات
- جدول `collections` لحفظ عمليات التحصيل
- ربط مع جدول الفواتير والعملاء
- تحديث تلقائي لحالة الفواتير

## البنية التقنية

### النماذج (Models)

#### CollectionModel
```dart
class CollectionModel {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime invoiceDate;
  final double invoiceTotal;
  final double previousPaidAmount;
  final double collectionAmount;
  final double remainingAmount;
  final DateTime collectionDate;
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;
}
```

### الخدمات (Services)

#### CollectionService
- `getAllCollections()` - الحصول على جميع عمليات التحصيل
- `getPendingInvoices()` - الحصول على الفواتير المستحقة
- `createCollection()` - إنشاء عملية تحصيل جديدة
- `addCollection()` - إضافة عملية تحصيل إلى قاعدة البيانات
- `getCollectionStatistics()` - إحصائيات التحصيل

### الواجهات (Screens & Widgets)

#### CollectionsScreen
- الشاشة الرئيسية لعرض الفواتير المستحقة
- شريط البحث
- إحصائيات سريعة
- قائمة الفواتير مع إمكانية التحصيل

#### CollectionDialog
- مربع حوار إدخال مبلغ التحصيل
- عرض تفاصيل الفاتورة
- التحقق من صحة المبلغ
- إدخال ملاحظات

#### InvoiceCollectionCard
- بطاقة عرض الفاتورة في قائمة التحصيل
- عرض تفاصيل العميل والفاتورة
- شريط تقدم نسبة الدفع
- زر التحصيل

## قاعدة البيانات

### جدول Collections
```sql
CREATE TABLE collections (
  id TEXT PRIMARY KEY,
  invoiceId TEXT NOT NULL,
  invoiceNumber TEXT NOT NULL,
  customerId TEXT NOT NULL,
  customerName TEXT NOT NULL,
  customerPhone TEXT,
  invoiceDate TEXT NOT NULL,
  invoiceTotal REAL NOT NULL,
  previousPaidAmount REAL NOT NULL,
  collectionAmount REAL NOT NULL,
  remainingAmount REAL NOT NULL,
  collectionDate TEXT NOT NULL,
  notes TEXT,
  createdBy TEXT,
  createdAt TEXT NOT NULL,
  FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE,
  FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
)
```

## كيفية الاستخدام

### 1. الوصول إلى نظام التحصيل
- من لوحة التحكم، اضغط على "التحصيل" في الإجراءات السريعة
- ستظهر قائمة بجميع الفواتير المستحقة

### 2. عملية التحصيل
1. اختر الفاتورة المراد تحصيلها
2. اضغط على زر "تحصيل"
3. أدخل مبلغ التحصيل
4. أضف ملاحظات (اختياري)
5. اضغط "تحصيل" لتأكيد العملية

### 3. مشاركة الإشعار
- بعد نجاح عملية التحصيل، ستظهر خيارات المشاركة
- اختر الواتساب أو الرسالة النصية
- سيتم إنشاء نص الإشعار تلقائياً

## نص الإشعار

يحتوي إشعار التحصيل على:
- اسم الشركة
- اسم العميل
- رقم الفاتورة
- تاريخ ووقت الفاتورة
- إجمالي الفاتورة
- المبلغ المدفوع سابقاً
- مبلغ التحصيل
- المبلغ المتبقي
- تاريخ ووقت التحصيل

## التحديثات المطلوبة

### 1. إضافة url_launcher
لتفعيل مشاركة الواتساب والرسائل النصية، أضف إلى `pubspec.yaml`:
```yaml
dependencies:
  url_launcher: ^6.1.14
```

### 2. إضافة الأذونات
لإرسال الرسائل النصية، أضف الأذونات المطلوبة في `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
```

## الإحصائيات

النظام يوفر إحصائيات شاملة:
- إجمالي التحصيلات
- عدد عمليات التحصيل
- التحصيلات اليومية
- التحصيلات الشهرية
- التحصيلات حسب العميل
- التحصيلات حسب الفترة الزمنية

## الأمان والتحقق

- التحقق من صحة مبلغ التحصيل
- التأكد من عدم تجاوز المبلغ المتبقي
- تسجيل جميع العمليات مع التاريخ والوقت
- ربط العمليات بالمستخدم المنفذ

## الدعم التقني

في حالة وجود أي مشاكل أو استفسارات:
1. تحقق من وجود جدول `collections` في قاعدة البيانات
2. تأكد من تحديث إصدار قاعدة البيانات إلى 17
3. تحقق من صحة البيانات في جدول الفواتير

## التطوير المستقبلي

- إضافة تقارير التحصيل المفصلة
- إضافة إشعارات تلقائية للفواتير المتأخرة
- دعم طرق دفع إضافية
- إضافة نظام تنبيهات للعملاء
