# تحديث عرض عدد الفواتير

## نظرة عامة
تم تحديث شاشة إدارة الفواتير لعرض عدد الفواتير في عدة أماكن، مشابهة لشاشات المنتجات والعملاء.

## التحديثات المضافة

### 1. العنوان الرئيسي (AppBar)
- إضافة عداد الفواتير بجانب عنوان "إدارة الفواتير"
- تصميم أنيق مع أيقونة الفاتورة
- العداد يعكس عدد الفواتير المفلترة

### 2. شريط البحث
- إضافة عداد الفواتير في الجانب الأيمن من شريط البحث
- عرض النص "X فاتورة" مع أيقونة
- تصميم متناسق مع باقي العناصر

### 3. عداد المعلومات
- إضافة شريط معلومات فوق قائمة الفواتير
- عرض "تم العثور على X فاتورة"
- عند استخدام الفلاتر: "من أصل X فاتورة"

## المميزات

### تحديث تلقائي
- العداد يتم تحديثه تلقائياً عند تغيير الفلاتر
- العداد يتم تحديثه عند البحث
- العداد يعكس الفواتير المفلترة وليس الإجمالي

### تصميم متجاوب
- استخدام ألوان متناسقة مع باقي التطبيق
- أيقونات واضحة ومفهومة
- تخطيط متوازن ومريح للعين

### سهولة الاستخدام
- معلومات واضحة ومباشرة
- سهولة فهم عدد الفواتير المتاحة
- تحسين تجربة المستخدم

## الملفات المحدثة
- `lib/features/invoices/screens/invoices_screen.dart`

## كيفية العمل
1. عند فتح شاشة الفواتير، يتم عرض العدد الإجمالي
2. عند البحث أو استخدام الفلاتر، يتم تحديث العداد
3. العداد يعكس دائماً الفواتير المطابقة للبحث والفلاتر
4. يتم عرض معلومات إضافية عند استخدام الفلاتر

## الصور التوضيحية
- العنوان: عداد أنيق مع أيقونة الفاتورة
- شريط البحث: عداد في الجانب الأيمن
- عداد المعلومات: شريط معلومات فوق القائمة

## ملاحظات تقنية
- استخدام `_filteredInvoices.length` للعداد المفلتر
- استخدام `_invoices.length` للعدد الإجمالي
- تحديث تلقائي عند تغيير حالة الفلاتر
- تصميم متجاوب مع مختلف أحجام الشاشات
