import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../constants/app_colors.dart';
import '../../../widgets/back_button.dart';
import '../widgets/customer_list.dart';
import 'add_customer_screen.dart';
import '../widgets/customers_by_governorate_list.dart';
import '../../invoices/screens/invoices_by_date_screen.dart';

class CustomersScreen extends ConsumerStatefulWidget {
  final bool isSelectionMode;

  const CustomersScreen({
    super.key,
    this.isSelectionMode = false,
  });

  @override
  ConsumerState<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends ConsumerState<CustomersScreen> {
  bool _showByGovernorate = true; // عرض العملاء حسب المحافظات افتراضياً

  @override
  void initState() {
    super.initState();
    // تم إلغاء التحديث التلقائي
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        onPressed: () {
          _addNewCustomer();
        },
        child: Icon(Icons.add, color: Colors.white, size: 20.sp),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      body: Column(
        children: [
          // AppBar مخصص مربع في أعلى الصفحة
          Container(
            color: AppColors.primary,
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              bottom: 16.h,
              left: 16.w,
              right: 16.w,
            ),
            child: Row(
              children: [
                CustomBackButton(color: Colors.white, size: 20.sp),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'العملاء',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18.sp,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _showByGovernorate ? Icons.list : Icons.location_on,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      setState(() {
                        _showByGovernorate = !_showByGovernorate;
                      });
                    },
                    tooltip: _showByGovernorate
                        ? 'عرض عادي'
                        : 'عرض حسب المحافظات',
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.calendar_today,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const InvoicesByDateScreen(),
                        ),
                      );
                    },
                    tooltip: 'الفواتير حسب التاريخ',
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // المحتوى
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: AppColors.border),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _showByGovernorate
                  ? CustomersByGovernorateList(searchQuery: '')
                  : CustomerList(
                      searchQuery: '',
                      isSelectionMode: widget.isSelectionMode,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _addNewCustomer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AddCustomerScreen()),
    );

    // إذا تم إضافة عميل جديد، حدث القائمة
    if (result != null && result['added'] == true && mounted) {
      // تحديث القائمة لعرض العميل الجديد
      setState(() {
        // إعادة بناء القائمة
      });

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة العميل "${result['customer']['name']}" بنجاح',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
