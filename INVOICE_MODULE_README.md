# Invoice Module - Atlas Medical Supplies

## Overview
The Invoice Module is a comprehensive billing system integrated into the Atlas Medical Supplies application. It provides complete invoice management capabilities including creation, editing, viewing, and sharing of invoices.

## Features

### 1. Invoice Management
- **Auto-generated Invoice Numbers**: Unique invoice numbers are automatically generated using the format `INV-YYYYMMDD-XXXXXXXX`
- **Customer Integration**: Seamlessly linked with existing customer database
- **Product Integration**: Integrated with product inventory system
- **Real-time Calculations**: Automatic calculation of totals, discounts, and remaining amounts

### 2. Invoice Creation
- **Customer Selection**: Choose from existing customers with auto-fill of customer details
- **Product Selection**: Add multiple products with quantity and price management
- **Dynamic Pricing**: Automatic price selection based on customer type (distributor, medical office, etc.)
- **Discount System**: Support for both percentage and fixed amount discounts
- **Payment Tracking**: Track paid amounts and calculate remaining balances

### 3. Invoice Actions
- **Save**: Store invoices in local SQLite database
- **Share as Text**: Generate text format for sharing via WhatsApp, SMS, etc.
- **Share as Image**: Create image representation of invoice
- **Export PDF**: Generate professional PDF documents
- **Edit**: Modify existing invoices
- **Delete**: Remove invoices with confirmation

### 4. User Interface
- **Arabic RTL Support**: Full right-to-left layout support
- **Modern Design**: Clean, professional interface matching Atlas project theme
- **Responsive Layout**: Optimized for various screen sizes
- **Search & Filter**: Advanced search and filtering capabilities

## Architecture

### Models
- **InvoiceModel**: Main invoice data structure
- **InvoiceItem**: Individual product items within invoices

### Services
- **InvoiceService**: Database operations and business logic
- **CustomerService**: Customer data management
- **ProductService**: Product data management

### Providers
- **InvoiceProvider**: State management using Provider pattern

### Screens
- **InvoicesScreen**: Main invoice listing with search and statistics
- **AddInvoiceScreen**: Invoice creation interface
- **InvoiceDetailsScreen**: Detailed invoice view with actions

### Widgets
- **InvoiceCard**: Compact invoice display for lists
- **InvoiceFilterDialog**: Advanced filtering options
- **CustomerSelectionDialog**: Customer selection interface
- **ProductSelectionDialog**: Product selection with quantity management

## Database Schema

### invoices Table
```sql
CREATE TABLE invoices (
  id TEXT PRIMARY KEY,
  invoiceNumber TEXT UNIQUE NOT NULL,
  customerId TEXT NOT NULL,
  customerName TEXT NOT NULL,
  customerPhone TEXT,
  customerAddress TEXT,
  customerGovernorate TEXT,
  customerCity TEXT,
  invoiceDate TEXT NOT NULL,
  subtotal REAL NOT NULL DEFAULT 0,
  discountAmount REAL,
  discountType TEXT,
  discountPercentage REAL,
  totalAfterDiscount REAL NOT NULL DEFAULT 0,
  paidAmount REAL NOT NULL DEFAULT 0,
  remainingAmount REAL NOT NULL DEFAULT 0,
  paymentMethod TEXT NOT NULL,
  notes TEXT,
  createdBy TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL
);
```

### invoice_items Table
```sql
CREATE TABLE invoice_items (
  id TEXT PRIMARY KEY,
  invoiceId TEXT NOT NULL,
  productId TEXT NOT NULL,
  productName TEXT NOT NULL,
  productCode TEXT,
  unit TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 0,
  unitPrice REAL NOT NULL DEFAULT 0,
  totalPrice REAL NOT NULL DEFAULT 0,
  discountAmount REAL,
  discountType TEXT,
  discountPercentage REAL,
  FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
);
```

## Usage

### Creating a New Invoice
1. Navigate to the Invoices tab in the bottom navigation
2. Tap the floating action button (+)
3. Select a customer from the database
4. Add products with quantities
5. Set discount and payment information
6. Add notes if needed
7. Save the invoice

### Managing Existing Invoices
1. View invoice list with search and filter options
2. Tap on any invoice to view details
3. Use the action menu for sharing, editing, or deleting
4. Export invoices in various formats

### Invoice Statistics
- Total invoice count
- Total sales amount
- Today's invoices and sales
- Payment status tracking

## Technical Implementation

### Dependencies
- **Provider**: State management
- **sqflite**: Local database
- **intl**: Date and number formatting
- **uuid**: Unique identifier generation

### Key Features
- **Auto-save**: Automatic saving of invoice data
- **Validation**: Comprehensive input validation
- **Error Handling**: Graceful error handling with user feedback
- **Performance**: Optimized database queries and UI rendering

### Integration Points
- **Customer System**: Seamless customer data access
- **Product System**: Real-time product information and pricing
- **Database**: Integrated with existing SQLite database
- **Navigation**: Integrated with main app navigation

## Future Enhancements

### Planned Features
- **Cloud Sync**: Synchronization with cloud services
- **Advanced Reporting**: Detailed analytics and reporting
- **Multi-currency**: Support for different currencies
- **Tax Calculation**: Automatic tax computation
- **Payment Integration**: Online payment processing
- **Invoice Templates**: Customizable invoice designs

### Technical Improvements
- **Offline Support**: Enhanced offline functionality
- **Performance**: Further optimization for large datasets
- **Accessibility**: Improved accessibility features
- **Testing**: Comprehensive unit and integration tests

## Configuration

### Database Version
The invoice module requires database version 10 or higher. The upgrade process automatically creates the necessary tables.

### Permissions
- **Read Access**: View invoices and customer/product data
- **Write Access**: Create, edit, and delete invoices
- **Export Access**: Share and export invoice data

## Troubleshooting

### Common Issues
1. **Database Errors**: Ensure database version is 10+
2. **Missing Dependencies**: Verify all required packages are installed
3. **Permission Issues**: Check app permissions for file access
4. **Performance Issues**: Monitor database query performance

### Debug Information
- Enable debug logging for detailed error information
- Check database integrity using built-in validation
- Monitor memory usage for large invoice datasets

## Support

For technical support or feature requests, please refer to the main Atlas Medical Supplies documentation or contact the development team.

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Compatibility**: Atlas Medical Supplies v1.0+
