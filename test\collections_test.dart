import 'package:flutter_test/flutter_test.dart';
import '../lib/models/collection_model.dart';
import '../lib/models/invoice_model.dart';

void main() {
  group('CollectionModel Tests', () {
    test('should create CollectionModel from JSON', () {
      final json = {
        'id': 'test-id',
        'invoiceId': 'invoice-123',
        'invoiceNumber': 'INV-001',
        'customerId': 'customer-456',
        'customerName': 'أحمد محمد',
        'customerPhone': '0123456789',
        'invoiceDate': '2024-01-15T10:30:00.000Z',
        'invoiceTotal': 1000.0,
        'previousPaidAmount': 300.0,
        'paymentAmount': 200.0,
        'remainingAmount': 500.0,
        'paymentDate': '2024-01-16T14:20:00.000Z',
        'notes': 'تحصيل جزئي',
        'createdBy': 'admin',
        'createdAt': '2024-01-16T14:20:00.000Z',
      };

      final collection = CollectionModel.fromJson(json);

      expect(collection.id, 'test-id');
      expect(collection.invoiceNumber, 'INV-001');
      expect(collection.customerName, 'أحمد محمد');
      expect(collection.invoiceTotal, 1000.0);
      expect(collection.paymentAmount, 200.0);
      expect(collection.remainingAmount, 500.0);
      expect(collection.notes, 'تحصيل جزئي');
    });

    test('should convert CollectionModel to JSON', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      final json = collection.toJson();

      expect(json['id'], 'test-id');
      expect(json['invoiceNumber'], 'INV-001');
      expect(json['customerName'], 'أحمد محمد');
      expect(json['invoiceTotal'], 1000.0);
      expect(json['paymentAmount'], 200.0);
      expect(json['remainingAmount'], 500.0);
      expect(json['notes'], 'تحصيل جزئي');
    });

    test('should generate notification message', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      final message = collection.notificationMessage;

      expect(message, contains('أطلس للمستلزمات الطبية'));
      expect(message, contains('أحمد محمد'));
      expect(message, contains('INV-001'));
      expect(message, contains('200.00 ر.س'));
      expect(message, contains('500.00 ر.س'));
    });

    test('should format dates correctly', () {
      final collection = CollectionModel(
        id: 'test-id',
        invoiceId: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.parse('2024-01-15T10:30:00.000Z'),
        invoiceTotal: 1000.0,
        previousPaidAmount: 300.0,
        paymentAmount: 200.0,
        remainingAmount: 500.0,
        paymentDate: DateTime.parse('2024-01-16T14:20:00.000Z'),
        notes: 'تحصيل جزئي',
        createdBy: 'admin',
        createdAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
      );

      expect(collection.formattedInvoiceDate, '15/1/2024');
      expect(collection.formattedPaymentDate, '16/1/2024');
      expect(collection.formattedPaymentTime, '02:20 م');
    });
  });

  group('InvoiceModel Collection Tests', () {
    test('should calculate remaining amount correctly', () {
      final invoice = InvoiceModel(
        id: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 1000.0,
        total: 1000.0,
        paidAmount: 300.0,
        status: InvoiceStatus.partial,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final remainingAmount = invoice.calculateRemainingAmount();
      expect(remainingAmount, 700.0);
    });

    test('should check if invoice is fully paid', () {
      final invoice = InvoiceModel(
        id: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 1000.0,
        total: 1000.0,
        paidAmount: 1000.0,
        status: InvoiceStatus.paid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(invoice.isFullyPaid, true);
    });

    test('should check if invoice is partially paid', () {
      final invoice = InvoiceModel(
        id: 'invoice-123',
        invoiceNumber: 'INV-001',
        customerId: 'customer-456',
        customerName: 'أحمد محمد',
        customerPhone: '0123456789',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 1000.0,
        total: 1000.0,
        paidAmount: 500.0,
        status: InvoiceStatus.partial,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(invoice.paidAmount > 0 && invoice.paidAmount < invoice.total, true);
    });
  });
}
