import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../widgets/customer_card.dart';
import '../screens/customer_details_screen.dart';
import '../screens/add_customer_screen.dart';

class CustomerList extends StatefulWidget {
  final String searchQuery;
  final bool isSelectionMode;

  const CustomerList({
    super.key,
    required this.searchQuery,
    this.isSelectionMode = false,
  });

  @override
  State<CustomerList> createState() => _CustomerListState();
}

class _CustomerListState extends State<CustomerList> {
  final CustomerService _customerService = CustomerService();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<CustomerModel>>(
      stream: _customerService.getActiveCustomers(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
                SizedBox(height: 16.h),
                Text(
                  'حدث خطأ في تحميل البيانات',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.error,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                ElevatedButton(
                  onPressed: () {
                    setState(() {});
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          );
        }

        List<CustomerModel> customers = snapshot.data ?? [];

        // تطبيق البحث
        if (widget.searchQuery.isNotEmpty) {
          customers = customers.where((customer) {
            return customer.name.toLowerCase().contains(
                  widget.searchQuery.toLowerCase(),
                ) ||
                customer.activity.toLowerCase().contains(
                  widget.searchQuery.toLowerCase(),
                ) ||
                (customer.phone1?.contains(widget.searchQuery) ?? false) ||
                (customer.phone2?.contains(widget.searchQuery) ?? false);
          }).toList();
        }

        if (customers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 24.h),
                Text(
                  widget.searchQuery.isNotEmpty
                      ? 'لا توجد نتائج للبحث'
                      : 'لا يوجد عملاء حتى الآن',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  widget.searchQuery.isNotEmpty
                      ? 'جرب تغيير كلمات البحث'
                      : 'ابدأ بإضافة عميل جديد',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: customers.length,
          itemBuilder: (context, index) {
            final customer = customers[index];
            return CustomerCard(
              customer: customer,
              onTap: () async {
                if (widget.isSelectionMode) {
                  // إرسال العميل المحدد للشاشة الأم في وضع الاختيار
                  Navigator.pop(context, customer);
                } else {
                  // فتح صفحة تفاصيل العميل في الوضع العادي
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CustomerDetailsScreen(customer: customer),
                    ),
                  );

                  // إذا تم حذف العميل، قم بإزالته من القائمة فوراً
                  if (result != null && result is Map) {
                    if (result['deleted'] == true) {
                      setState(() {
                        customers.removeWhere(
                          (c) => c.id == result['customerId'],
                        );
                      });

                      // عرض رسالة نجاح
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'تم حذف العميل بنجاح',
                              style: TextStyle(fontFamily: 'Cairo'),
                            ),
                            backgroundColor: AppColors.success,
                          ),
                        );
                      }
                    }
                  }
                }
              },
              onEdit: () => _editCustomer(customer),
              onDelete: () => _deleteCustomer(customer),
            );
          },
        );
      },
    );
  }

  /// تعديل العميل
  Future<void> _editCustomer(CustomerModel customer) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCustomerScreen(customer: customer),
      ),
    );

    // إذا تم تحديث أو إضافة العميل، قم بتحديث القائمة
    if (result != null && result is Map) {
      if (result['updated'] == true || result['added'] == true) {
        setState(() {
          // تحديث القائمة
        });
      }
    }
  }

  /// حذف العميل
  Future<void> _deleteCustomer(CustomerModel customer) async {
    showDeleteProtectionDialog(
      context: context,
      title: 'حذف العميل',
      message:
          'سيتم حذف العميل نهائياً من قاعدة البيانات. لا يمكن التراجع عن هذا الإجراء.',
      itemName: customer.name ?? 'العميل المحدد',
      onConfirm: () async {
        try {
          // عرض مؤشر التحميل
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) =>
                const Center(child: CircularProgressIndicator()),
          );

          // حذف العميل
          final success = await _customerService.deleteCustomer(customer.id);

          // إغلاق مؤشر التحميل
          if (mounted) {
            Navigator.of(context).pop();
          }

          if (success && mounted) {
            // عرض رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم حذف العميل "${customer.name}" بنجاح',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.success,
              ),
            );

            // تحديث القائمة فوراً
            setState(() {});
          }
        } catch (e) {
          // إغلاق مؤشر التحميل
          if (mounted) {
            Navigator.of(context).pop();
          }

          // عرض رسالة خطأ
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'فشل في حذف العميل: ${e.toString()}',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.error,
              ),
            );
          }
        }
      },
    );
  }
}
