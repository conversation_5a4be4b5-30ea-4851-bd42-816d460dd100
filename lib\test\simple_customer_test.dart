import 'package:flutter/material.dart';
import '../models/customer_model.dart';
import '../services/customer_service.dart';
import '../services/database_service.dart';

/// اختبار مبسط لقاعدة بيانات العملاء
class SimpleCustomerTest {
  static final CustomerService _customerService = CustomerService();
  static final DatabaseService _databaseService = DatabaseService();

  /// تشغيل اختبار بسيط
  static Future<void> runSimpleTest() async {
    debugPrint('=== بدء اختبار بسيط للعملاء ===');
    
    try {
      // 1. فحص قاعدة البيانات
      await _checkDatabase();
      
      // 2. إنشاء عميل تجريبي
      await _createTestCustomer();
      
      // 3. جلب العملاء
      await _getCustomers();
      
      // 4. البحث عن العميل
      await _searchCustomer();
      
      // 5. تحديث العميل
      await _updateCustomer();
      
      // 6. حذف العميل
      await _deleteCustomer();
      
      debugPrint('=== الاختبار البسيط اكتمل بنجاح! ===');
      
    } catch (e) {
      debugPrint('=== فشل في الاختبار: $e ===');
    }
  }

  /// فحص قاعدة البيانات
  static Future<void> _checkDatabase() async {
    debugPrint('\n--- فحص قاعدة البيانات ---');
    
    try {
      await _databaseService.checkAndRepairDatabase();
      debugPrint('✅ قاعدة البيانات تعمل بشكل صحيح');
    } catch (e) {
      debugPrint('❌ مشكلة في قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// إنشاء عميل تجريبي
  static Future<void> _createTestCustomer() async {
    debugPrint('\n--- إنشاء عميل تجريبي ---');
    
    try {
      final testCustomer = CustomerModel(
        id: '',
        name: 'عميل اختبار ${DateTime.now().millisecondsSinceEpoch}',
        phone1: '01${DateTime.now().millisecondsSinceEpoch % 100000000}',
        type: CustomerType.medicalOfficeA,
        activity: 'عيادة طبية',
        governorate: 'القاهرة',
        city: 'مدينة نصر',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('إنشاء عميل: ${testCustomer.name}');
      
      final result = await _customerService.addCustomer(testCustomer);
      
      if (result) {
        debugPrint('✅ تم إنشاء العميل بنجاح');
      } else {
        throw Exception('فشل في إنشاء العميل');
      }
      
    } catch (e) {
      debugPrint('❌ فشل في إنشاء العميل: $e');
      rethrow;
    }
  }

  /// جلب العملاء
  static Future<void> _getCustomers() async {
    debugPrint('\n--- جلب العملاء ---');
    
    try {
      final customers = await _customerService.getAllCustomers();
      
      debugPrint('عدد العملاء: ${customers.length}');
      
      if (customers.isNotEmpty) {
        debugPrint('أسماء العملاء:');
        for (var customer in customers.take(3)) {
          debugPrint('- ${customer.name} (${customer.phone1})');
        }
        debugPrint('✅ تم جلب العملاء بنجاح');
      } else {
        debugPrint('⚠️ لا يوجد عملاء');
      }
      
    } catch (e) {
      debugPrint('❌ فشل في جلب العملاء: $e');
      rethrow;
    }
  }

  /// البحث عن عميل
  static Future<void> _searchCustomer() async {
    debugPrint('\n--- البحث عن عميل ---');
    
    try {
      final customers = await _customerService.searchCustomers('اختبار');
      
      if (customers.isNotEmpty) {
        final foundCustomer = customers.first;
        debugPrint('تم العثور على: ${foundCustomer.name}');
        debugPrint('✅ البحث يعمل بشكل صحيح');
      } else {
        debugPrint('⚠️ لم يتم العثور على عملاء بالبحث');
      }
      
    } catch (e) {
      debugPrint('❌ فشل في البحث: $e');
      rethrow;
    }
  }

  /// تحديث عميل
  static Future<void> _updateCustomer() async {
    debugPrint('\n--- تحديث عميل ---');
    
    try {
      final customers = await _customerService.searchCustomers('اختبار');
      
      if (customers.isNotEmpty) {
        final customerToUpdate = customers.first;
        debugPrint('تحديث: ${customerToUpdate.name}');
        
        final updatedCustomer = customerToUpdate.copyWith(
          notes: 'تم التحديث في ${DateTime.now()}',
          updatedAt: DateTime.now(),
        );
        
        final result = await _customerService.updateCustomer(updatedCustomer);
        
        if (result) {
          debugPrint('✅ تم التحديث بنجاح');
        } else {
          throw Exception('فشل في التحديث');
        }
      } else {
        debugPrint('⚠️ لا يوجد عميل للتحديث');
      }
      
    } catch (e) {
      debugPrint('❌ فشل في التحديث: $e');
      rethrow;
    }
  }

  /// حذف عميل
  static Future<void> _deleteCustomer() async {
    debugPrint('\n--- حذف عميل ---');
    
    try {
      final customers = await _customerService.searchCustomers('اختبار');
      
      if (customers.isNotEmpty) {
        final customerToDelete = customers.first;
        debugPrint('حذف: ${customerToDelete.name}');
        
        final result = await _customerService.deleteCustomer(customerToDelete.id);
        
        if (result) {
          debugPrint('✅ تم الحذف بنجاح');
          
          // التحقق من الحذف
          final activeCustomers = await _customerService.getAllCustomers();
          final deletedCustomer = activeCustomers.where((c) => c.id == customerToDelete.id).toList();
          
          if (deletedCustomer.isEmpty) {
            debugPrint('✅ تم التحقق من الحذف');
          } else {
            debugPrint('⚠️ العميل لا يزال يظهر');
          }
        } else {
          throw Exception('فشل في الحذف');
        }
      } else {
        debugPrint('⚠️ لا يوجد عميل للحذف');
      }
      
    } catch (e) {
      debugPrint('❌ فشل في الحذف: $e');
      rethrow;
    }
  }

  /// اختبار سريع
  static Future<void> quickTest() async {
    debugPrint('=== اختبار سريع للعملاء ===');
    
    try {
      // إنشاء عميل
      final customer = CustomerModel(
        id: '',
        name: 'اختبار سريع',
        phone1: '0123456789',
        governorate: 'القاهرة',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final created = await _customerService.addCustomer(customer);
      debugPrint('إنشاء: ${created ? "نجح" : "فشل"}');
      
      if (created) {
        // البحث
        final found = await _customerService.searchCustomers('اختبار سريع');
        debugPrint('البحث: ${found.isNotEmpty ? "نجح" : "فشل"}');
        
        if (found.isNotEmpty) {
          // حذف
          final deleted = await _customerService.deleteCustomer(found.first.id);
          debugPrint('حذف: ${deleted ? "نجح" : "فشل"}');
        }
      }
      
      debugPrint('=== الاختبار السريع اكتمل ===');
      
    } catch (e) {
      debugPrint('❌ فشل في الاختبار السريع: $e');
    }
  }
}
