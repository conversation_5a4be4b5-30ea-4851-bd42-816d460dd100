# إصلاح مشكلة حفظ الأسعار في المنتجات

## المشكلة
كانت هناك مشكلة في حفظ الأسعار عند إضافة منتج جديد. التطبيق كان يحفظ سعر آخر وهمي بدلاً من السعر المدخل من قبل المستخدم.

## سبب المشكلة
1. نموذج `ProductModel` كان يحتوي على حقل `price` واحد فقط
2. الواجهة تطلب 4 أسعار مختلفة (موزع، مكتب طبي أ، مكتب طبي ب، عميل كبير)
3. في دالة `_saveProduct` كان يتم حفظ السعر من `_distributorPriceController` فقط
4. الأسعار الأخرى لم تكن تُحفظ في قاعدة البيانات

## الحل المطبق

### 1. تحديث نموذج المنتج (`ProductModel`)
- إضافة حقول جديدة للأسعار المختلفة:
  - `distributorPrice`
  - `medicalOfficeAPrice`
  - `medicalOfficeBPrice`
  - `majorClientPrice`

### 2. تحديث دوال التحويل
- تحديث `fromJson()` و `toJson()`
- تحديث `fromMap()` و `toMap()`
- تحديث `copyWith()`

### 3. تحديث دالة `getPriceForCustomerType()`
- الآن تستخدم الأسعار المحفوظة بدلاً من حسابها
- إذا لم تكن الأسعار محفوظة، تستخدم الحساب التقليدي

### 4. تحديث شاشة إضافة المنتج
- تحديث `_populateFormData()` لتحميل الأسعار المحفوظة
- تحديث `_saveProduct()` لحفظ جميع الأسعار

### 5. إضافة ميزة تحديث الأسعار تلقائياً
- عند تغيير سعر الموزع، يتم تحديث الأسعار الأخرى تلقائياً:
  - مكتب طبي أ: سعر الموزع × 1.125
  - مكتب طبي ب: سعر الموزع × 1.1875
  - عميل كبير: سعر الموزع × 1.0625

## الملفات المحدثة

### 1. `lib/models/product_model.dart`
- إضافة حقول الأسعار الجديدة
- تحديث جميع دوال التحويل

### 2. `lib/features/products/screens/add_product_screen.dart`
- تحديث دالة `_populateFormData()`
- تحديث دالة `_saveProduct()`
- إضافة دالة `_updatePricesAutomatically()`

### 3. `lib/features/products/widgets/product_pricing_section.dart`
- إضافة callback function لتحديث الأسعار تلقائياً
- إضافة listener لتغيير سعر الموزع

## النتائج
✅ **حل مشكلة حفظ الأسعار** - الآن يتم حفظ جميع الأسعار المدخلة
✅ **تحسين تجربة المستخدم** - تحديث الأسعار تلقائياً
✅ **توافق مع قاعدة البيانات** - جميع الأسعار تُحفظ بشكل صحيح
✅ **دعم التعديل** - يمكن تعديل الأسعار المحفوظة مسبقاً

## كيفية الاستخدام
1. عند إضافة منتج جديد، أدخل سعر الموزع
2. ستتم ملء الأسعار الأخرى تلقائياً
3. يمكنك تعديل أي سعر يدوياً
4. جميع الأسعار ستُحفظ عند الضغط على "إضافة المنتج"

## ملاحظات تقنية
- الأسعار تُحفظ كـ `double` في قاعدة البيانات
- يتم التحقق من صحة الأسعار قبل الحفظ
- دعم الأرقام العشرية في حقول الأسعار
- تحويل تلقائي للنصوص إلى أرقام
