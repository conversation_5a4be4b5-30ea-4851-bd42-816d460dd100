import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/settings/settings_manager.dart';
import '../../../core/settings/app_settings.dart';

import '../../../constants/app_colors.dart';
import '../../../services/auth_service.dart';

import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _enableAdaptiveTheme = false;
  bool _enableRTL = true;
  bool _enableResponsiveLayout = true;
  bool _enableAppSignatureVerification = true;
  bool _enableDatabaseEncryption = true;
  bool _disableScreenCapture = false;
  bool _enableBackupRestore = true;
  bool _enableDataCompression = true;
  bool _enableSecureStorage = true;
  bool _enableOfflineMode = true;
  bool _enablePushNotifications = true;
  bool _enableAutoSync = true;
  bool _showOnboarding = true;
  bool _enableStatePersistence = true;
  bool _enablePullToRefresh = true;
  bool _enableAppSigning = true;
  bool _enableBiometricLogin = false;

  // Invoice Protection Settings
  bool _enableInvoiceProtection = false;
  bool _requirePasswordForInvoiceAccess = false;
  bool _requirePasswordForInvoiceEdit = true;
  bool _requirePasswordForInvoiceDelete = true;

  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';
  double _totalPendingCollections = 0.0; // إضافة متغير لمبالغ التحصيل المستحقة

  @override
  void initState() {
    super.initState();
    _loadPendingCollections();
  }

  // إضافة دالة جديدة لحساب مبالغ التحصيل المستحقة
  Future<void> _loadPendingCollections() async {
    try {
      final collectionService = CollectionService();
      final pendingInvoices = await collectionService.getPendingInvoices();
      double totalAmount = 0.0;

      for (final invoice in pendingInvoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _totalPendingCollections = totalAmount;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مبالغ التحصيل: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'إعدادات التطبيق',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 2,
        leading: CustomBackButton(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, size: 20.sp),
            onPressed: () {
              _resetSettings();
              _loadPendingCollections();
            },
            tooltip: 'إعادة تعيين',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // بطاقة مبالغ التحصيل المستحقة
            if (_totalPendingCollections > 0)
              Container(
                margin: EdgeInsets.only(bottom: 16.h),
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade50, Colors.orange.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: Colors.orange.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.shade200.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.w),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade200,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Icon(
                        Icons.payment,
                        color: Colors.orange.shade800,
                        size: 20.sp,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'مبالغ التحصيل المستحقة',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                          Text(
                            'تحتاج إلى متابعة',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 12.sp,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade200,
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: Text(
                        '${_totalPendingCollections.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // قسم المظهر والتصميم
            _buildSection('المظهر والتصميم', Icons.palette, [
              _buildSwitchTile(
                'المظهر التكيفي',
                'تغيير تلقائي حسب النظام',
                _enableAdaptiveTheme,
                (value) => setState(() => _enableAdaptiveTheme = value),
              ),
              _buildSwitchTile(
                'دعم اللغة العربية',
                'تفعيل الكتابة من اليمين لليسار',
                _enableRTL,
                (value) => setState(() => _enableRTL = value),
              ),
              _buildSwitchTile(
                'تصميم متجاوب',
                'دعم الشاشات المختلفة',
                _enableResponsiveLayout,
                (value) => setState(() => _enableResponsiveLayout = value),
              ),
              _buildLanguageSelector(),
              _buildThemeSelector(),
            ]),

            SizedBox(height: 16.h),

            // قسم الأمان والحماية
            _buildSection('الأمان والحماية', Icons.security, [
              _buildSwitchTile(
                'التحقق من التوقيع',
                'التحقق من سلامة التطبيق',
                _enableAppSignatureVerification,
                (value) =>
                    setState(() => _enableAppSignatureVerification = value),
              ),
              _buildSwitchTile(
                'تشفير قاعدة البيانات',
                'حماية البيانات المحلية',
                _enableDatabaseEncryption,
                (value) => setState(() => _enableDatabaseEncryption = value),
              ),
              _buildSwitchTile(
                'منع التقاط الشاشة',
                'حماية المحتوى الحساس',
                _disableScreenCapture,
                (value) => setState(() => _disableScreenCapture = value),
              ),
              _buildSwitchTile(
                'تسجيل الدخول بالبصمة',
                'السماح بالدخول باستخدام البصمة بعد تسجيل دخول ناجح',
                _enableBiometricLogin,
                (value) => setState(() => _enableBiometricLogin = value),
              ),
              _buildPasswordSection(),
              _buildInvoiceProtectionSection(),
            ]),

            SizedBox(height: 16.h),

            // قسم البيانات والتخزين
            _buildSection('البيانات والتخزين', Icons.storage, [
              _buildSwitchTile(
                'النسخ الاحتياطي',
                'حفظ واستعادة البيانات',
                _enableBackupRestore,
                (value) => setState(() => _enableBackupRestore = value),
              ),
              _buildSwitchTile(
                'ضغط البيانات',
                'تقليل حجم البيانات المحفوظة',
                _enableDataCompression,
                (value) => setState(() => _enableDataCompression = value),
              ),
              _buildSwitchTile(
                'تخزين آمن',
                'تشفير البيانات المحفوظة',
                _enableSecureStorage,
                (value) => setState(() => _enableSecureStorage = value),
              ),
            ]),

            SizedBox(height: 16.h),

            // قسم الاتصال والمزامنة
            _buildSection('الاتصال والمزامنة', Icons.wifi, [
              _buildSwitchTile(
                'الوضع بدون إنترنت',
                'العمل بدون اتصال بالإنترنت',
                _enableOfflineMode,
                (value) => setState(() => _enableOfflineMode = value),
              ),
              _buildSwitchTile(
                'الإشعارات الفورية',
                'استقبال التنبيهات',
                _enablePushNotifications,
                (value) => setState(() => _enablePushNotifications = value),
              ),
              _buildSwitchTile(
                'المزامنة التلقائية',
                'مزامنة البيانات تلقائياً',
                _enableAutoSync,
                (value) => setState(() => _enableAutoSync = value),
              ),
            ]),

            SizedBox(height: 16.h),

            // قسم تجربة المستخدم
            _buildSection('تجربة المستخدم', Icons.psychology, [
              _buildSwitchTile(
                'شاشة الترحيب',
                'عرض دليل الاستخدام للمستخدمين الجدد',
                _showOnboarding,
                (value) => setState(() => _showOnboarding = value),
              ),
              _buildSwitchTile(
                'حفظ الحالة',
                'تذكر حالة التطبيق',
                _enableStatePersistence,
                (value) => setState(() => _enableStatePersistence = value),
              ),
              _buildSwitchTile(
                'السحب للتحديث',
                'تحديث المحتوى بالسحب',
                _enablePullToRefresh,
                (value) => setState(() => _enablePullToRefresh = value),
              ),
            ]),

            SizedBox(height: 16.h),

            // قسم معلومات التطبيق
            _buildSection('معلومات التطبيق', Icons.info, [
              _buildInfoTile(
                'إصدار التطبيق',
                _selectedLanguage == 'ar' ? '1.0.0' : '1.0.0',
              ),
              _buildInfoTile('رقم البناء', '1'),
              _buildSwitchTile(
                'توقيع التطبيق',
                'توقيع التطبيق للنشر',
                _enableAppSigning,
                (value) => setState(() => _enableAppSigning = value),
              ),
            ]),

            SizedBox(height: 16.h),

            // قسم إدارة قاعدة البيانات
            _buildSection('إدارة قاعدة البيانات', Icons.storage, [


            ]),

            SizedBox(height: 24.h),

            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppColors.primary, size: 24.sp),
                SizedBox(width: 12.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
        activeTrackColor: AppColors.primary.withValues(alpha: 0.3),
      ),
    );
  }

  Widget _buildNavigationTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      leading: Icon(icon, color: AppColors.primary, size: 20.sp),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.textSecondary,
        size: 16.sp,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        value,
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return ListTile(
      title: Text(
        'اللغة',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        _selectedLanguage == 'ar' ? 'العربية' : 'English',
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      trailing: DropdownButton<String>(
        value: _selectedLanguage,
        items: const [
          DropdownMenuItem(value: 'ar', child: Text('العربية')),
          DropdownMenuItem(value: 'en', child: Text('English')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() => _selectedLanguage = value);
          }
        },
      ),
    );
  }

  Widget _buildThemeSelector() {
    String labelOf(String theme) {
      switch (theme) {
        case 'light':
          return 'فاتح';
        case 'dark':
          return 'داكن';
        default:
          return 'تلقائي';
      }
    }

    return ListTile(
      title: Text(
        'المظهر',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        labelOf(_selectedTheme),
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      trailing: DropdownButton<String>(
        value: _selectedTheme,
        items: const [
          DropdownMenuItem(value: 'auto', child: Text('تلقائي')),
          DropdownMenuItem(value: 'light', child: Text('فاتح')),
          DropdownMenuItem(value: 'dark', child: Text('داكن')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() => _selectedTheme = value);
          }
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر حفظ الإعدادات
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _saveSettings,
            icon: Icon(Icons.save, size: 20.sp),
            label: Text(
              'حفظ الإعدادات',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              elevation: 2,
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // زر تسجيل الخروج
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _showLogoutDialog,
            icon: Icon(Icons.logout, size: 20.sp),
            label: Text(
              'تسجيل الخروج',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: BorderSide(color: AppColors.error),
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _saveSettings() {
    // حفظ الإعدادات في settingsProvider وتطبيقها
    final notifier = ref.read(settingsProvider.notifier);
    final s = ref.read(settingsProvider);
    s.selectedLanguage = _selectedLanguage;
    s.selectedTheme = _selectedTheme;
    s.disableScreenCapture = _disableScreenCapture;
    // مبدئياً نطبق ما هو ذو تأثير مباشر على الواجهة
    notifier.saveSettings();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ الإعدادات بنجاح',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إعادة تعيين الإعدادات',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        content: Text(
          'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _enableAdaptiveTheme = false;
                _enableRTL = true;
                _enableResponsiveLayout = true;
                _enableAppSignatureVerification = true;
                _enableDatabaseEncryption = true;
                _disableScreenCapture = false;
                _enableBackupRestore = true;
                _enableDataCompression = true;
                _enableSecureStorage = true;
                _enableOfflineMode = true;
                _enablePushNotifications = true;
                _enableAutoSync = true;
                _showOnboarding = true;
                _enableStatePersistence = true;
                _enablePullToRefresh = true;
                _enableAppSigning = true;
                _selectedLanguage = 'ar';
                _selectedTheme = 'light';
              });
              // إعادة الإعدادات المحفوظة
              ref.read(settingsProvider.notifier).resetSettings();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم إعادة تعيين الإعدادات',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: Text('تأكيد', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسجيل الخروج', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'هل أنت متأكد من تسجيل الخروج؟',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authService = provider.Provider.of<AuthService>(
                  context,
                  listen: false,
                );
                await authService.signOut();
                if (context.mounted) {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'حدث خطأ أثناء تسجيل الخروج',
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: Text('تأكيد', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }







  Widget _buildPasswordSection() {
    return Column(
      children: [
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.lock, color: Colors.orange[700], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'كلمة المرور للحذف',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'كلمة المرور مطلوبة لحذف العملاء والفواتير',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _showSetPasswordDialog,
                      icon: const Icon(Icons.edit, color: Colors.white),
                      label: const Text(
                        'تعيين كلمة المرور',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[700],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _showChangePasswordDialog,
                      icon: const Icon(Icons.key, color: Colors.white),
                      label: const Text(
                        'تغيير كلمة المرور',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showSetPasswordDialog() {
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: AppColors.primary),
            const SizedBox(width: 8),
            const Text('تعيين كلمة المرور'),
          ],
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                  hintText: 'أدخل كلمة المرور الجديدة',
                  prefixIcon: Icon(Icons.lock),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'تأكيد كلمة المرور',
                  hintText: 'أعد إدخال كلمة المرور',
                  prefixIcon: Icon(Icons.check_circle),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى تأكيد كلمة المرور';
                  }
                  if (value != passwordController.text) {
                    return 'كلمة المرور غير متطابقة';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                try {
                  final appSettings = AppSettings();
                  await appSettings.setUserPassword(
                    passwordController.text.trim(),
                  );

                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تعيين كلمة المرور بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في تعيين كلمة المرور: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.key, color: AppColors.primary),
            const SizedBox(width: 8),
            const Text('تغيير كلمة المرور'),
          ],
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: currentPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الحالية',
                  hintText: 'أدخل كلمة المرور الحالية',
                  prefixIcon: Icon(Icons.lock),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال كلمة المرور الحالية';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: newPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                  hintText: 'أدخل كلمة المرور الجديدة',
                  prefixIcon: Icon(Icons.lock),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال كلمة المرور الجديدة';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'تأكيد كلمة المرور',
                  hintText: 'أعد إدخال كلمة المرور',
                  prefixIcon: Icon(Icons.check_circle),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى تأكيد كلمة المرور';
                  }
                  if (value != newPasswordController.text) {
                    return 'كلمة المرور غير متطابقة';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                try {
                  final appSettings = AppSettings();

                  // التحقق من كلمة المرور الحالية
                  final currentPassword = await appSettings.getUserPassword();
                  if (currentPassword !=
                      currentPasswordController.text.trim()) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('كلمة المرور الحالية غير صحيحة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                    return;
                  }

                  // حفظ كلمة المرور الجديدة
                  await appSettings.setUserPassword(
                    newPasswordController.text.trim(),
                  );

                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تغيير كلمة المرور بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في تغيير كلمة المرور: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceProtectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.primary.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.receipt, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'حماية الفواتير',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        _buildSwitchTile(
          'تفعيل حماية الفواتير',
          'حماية الفواتير بكلمة مرور',
          _enableInvoiceProtection,
          (value) => setState(() => _enableInvoiceProtection = value),
        ),
        if (_enableInvoiceProtection) ...[
          _buildSwitchTile(
            'كلمة مرور للوصول',
            'طلب كلمة مرور لفتح الفواتير',
            _requirePasswordForInvoiceAccess,
            (value) => setState(() => _requirePasswordForInvoiceAccess = value),
          ),
          _buildSwitchTile(
            'كلمة مرور للتعديل',
            'طلب كلمة مرور لتعديل الفواتير',
            _requirePasswordForInvoiceEdit,
            (value) => setState(() => _requirePasswordForInvoiceEdit = value),
          ),
          _buildSwitchTile(
            'كلمة مرور للحذف',
            'طلب كلمة مرور لحذف الفواتير',
            _requirePasswordForInvoiceDelete,
            (value) => setState(() => _requirePasswordForInvoiceDelete = value),
          ),
        ],
      ],
    );
  }
}
