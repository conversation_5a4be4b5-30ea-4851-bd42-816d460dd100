// ignore_for_file: public_member_api_docs
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_settings.dart';

/// مدير الإعدادات - Provider للوصول للإعدادات من أي مكان في التطبيق
final settingsProvider = StateNotifierProvider<SettingsManager, AppSettings>((
  ref,
) {
  return SettingsManager();
});

/// مدير الإعدادات الرئيسي
class SettingsManager extends StateNotifier<AppSettings> {
  SettingsManager() : super(AppSettings()) {
    _loadSettings();
  }

  /// تحميل الإعدادات عند بدء التطبيق
  Future<void> _loadSettings() async {
    await state.load();
    // Trigger listeners after async load completes
    state = state;
  }

  /// حفظ الإعدادات
  Future<void> saveSettings() async {
    await state.save();
    // إخطار المستمعين لإعادة البناء
    state = state;
  }

  /// إعادة تعيين الإعدادات
  void resetSettings() {
    state.resetToDefaults();
    saveSettings();
  }

  // Performance Methods
  void toggleSplashOptimization() {
    state.splashOptimization = !state.splashOptimization;
    saveSettings();
  }

  void toggleLazyLoading() {
    state.enableLazyLoading = !state.enableLazyLoading;
    saveSettings();
  }

  void toggleImageCompression() {
    state.enableImageCompression = !state.enableImageCompression;
    saveSettings();
  }

  void togglePrecache() {
    state.enablePrecache = !state.enablePrecache;
    saveSettings();
  }

  void toggleDebugLogs() {
    state.removeDebugLogs = !state.removeDebugLogs;
    saveSettings();
  }

  void toggleTreeShaking() {
    state.enableTreeShaking = !state.enableTreeShaking;
    saveSettings();
  }

  // UI/UX Methods
  void toggleAdaptiveTheme() {
    state.enableAdaptiveTheme = !state.enableAdaptiveTheme;
    saveSettings();
  }

  void toggleRTL() {
    state.enableRTL = !state.enableRTL;
    saveSettings();
  }

  void toggleResponsiveLayout() {
    state.enableResponsiveLayout = !state.enableResponsiveLayout;
    saveSettings();
  }

  void setLanguage(String language) {
    state.selectedLanguage = language;
    saveSettings();
  }

  void setTheme(String theme) {
    state.selectedTheme = theme;
    saveSettings();
  }

  void toggleBiometricLogin() {
    state.enableBiometricLogin = !state.enableBiometricLogin;
    saveSettings();
  }

  // Security Methods
  void toggleAppSignatureVerification() {
    state.enableAppSignatureVerification =
        !state.enableAppSignatureVerification;
    saveSettings();
  }

  void toggleDatabaseEncryption() {
    state.enableDatabaseEncryption = !state.enableDatabaseEncryption;
    saveSettings();
  }

  void toggleScreenCapture() {
    state.disableScreenCapture = !state.disableScreenCapture;
    saveSettings();
  }

  void toggleRootDetection() {
    state.enableRootDetection = !state.enableRootDetection;
    saveSettings();
  }

  void toggleCodeObfuscation() {
    state.enableCodeObfuscation = !state.enableCodeObfuscation;
    saveSettings();
  }

  // Data Methods
  void toggleBackupRestore() {
    state.enableBackupRestore = !state.enableBackupRestore;
    saveSettings();
  }

  void setCacheExpirationDays(int days) {
    state.cacheExpirationDays = days;
    saveSettings();
  }

  void toggleDataCompression() {
    state.enableDataCompression = !state.enableDataCompression;
    saveSettings();
  }

  void toggleSecureStorage() {
    state.enableSecureStorage = !state.enableSecureStorage;
    saveSettings();
  }

  // Connectivity Methods
  void toggleOfflineMode() {
    state.enableOfflineMode = !state.enableOfflineMode;
    saveSettings();
  }

  void togglePushNotifications() {
    state.enablePushNotifications = !state.enablePushNotifications;
    saveSettings();
  }

  void toggleAutoSync() {
    state.enableAutoSync = !state.enableAutoSync;
    saveSettings();
  }

  void setSyncIntervalMinutes(int minutes) {
    state.syncIntervalMinutes = minutes;
    saveSettings();
  }

  // UX Methods
  void toggleOnboarding() {
    state.showOnboarding = !state.showOnboarding;
    saveSettings();
  }

  void toggleStatePersistence() {
    state.enableStatePersistence = !state.enableStatePersistence;
    saveSettings();
  }

  void togglePullToRefresh() {
    state.enablePullToRefresh = !state.enablePullToRefresh;
    saveSettings();
  }

  void toggleInAppNotifications() {
    state.enableInAppNotifications = !state.enableInAppNotifications;
    saveSettings();
  }

  // Build Methods
  void setAppVersion(String version) {
    state.appVersion = version;
    saveSettings();
  }

  void setBuildNumber(int buildNumber) {
    state.buildNumber = buildNumber;
    saveSettings();
  }

  void toggleAppSigning() {
    state.enableAppSigning = !state.enableAppSigning;
    saveSettings();
  }

  void toggleMinification() {
    state.enableMinification = !state.enableMinification;
    saveSettings();
  }

  /// تطبيق الإعدادات على التطبيق
  void applySettings() {
    // تطبيق إعدادات المظهر
    if (state.enableRTL) {
      // تفعيل RTL
    }

    if (state.enableAdaptiveTheme) {
      // تفعيل المظهر التكيفي
    }

    // تطبيق إعدادات الأمان
    if (state.disableScreenCapture) {
      // تعطيل التقاط الشاشة
    }

    // تطبيق إعدادات الأداء
    if (state.removeDebugLogs) {
      // إزالة رسائل التصحيح
    }
  }

  /// الحصول على ملخص الإعدادات
  Map<String, dynamic> getSettingsSummary() {
    return {
      'performance': {
        'splashOptimization': state.splashOptimization,
        'lazyLoading': state.enableLazyLoading,
        'imageCompression': state.enableImageCompression,
        'precache': state.enablePrecache,
        'removeDebugLogs': state.removeDebugLogs,
        'treeShaking': state.enableTreeShaking,
      },
      'ui': {
        'adaptiveTheme': state.enableAdaptiveTheme,
        'rtl': state.enableRTL,
        'responsiveLayout': state.enableResponsiveLayout,
        'language': state.selectedLanguage,
        'theme': state.selectedTheme,
      },
      'security': {
        'appSignatureVerification': state.enableAppSignatureVerification,
        'databaseEncryption': state.enableDatabaseEncryption,
        'disableScreenCapture': state.disableScreenCapture,
        'rootDetection': state.enableRootDetection,
        'codeObfuscation': state.enableCodeObfuscation,
      },
      'data': {
        'backupRestore': state.enableBackupRestore,
        'cacheExpirationDays': state.cacheExpirationDays,
        'dataCompression': state.enableDataCompression,
        'secureStorage': state.enableSecureStorage,
      },
      'connectivity': {
        'offlineMode': state.enableOfflineMode,
        'pushNotifications': state.enablePushNotifications,
        'autoSync': state.enableAutoSync,
        'syncIntervalMinutes': state.syncIntervalMinutes,
      },
      'ux': {
        'onboarding': state.showOnboarding,
        'statePersistence': state.enableStatePersistence,
        'pullToRefresh': state.enablePullToRefresh,
        'inAppNotifications': state.enableInAppNotifications,
      },
      'build': {
        'appVersion': state.appVersion,
        'buildNumber': state.buildNumber,
        'appSigning': state.enableAppSigning,
        'minification': state.enableMinification,
      },
    };
  }
}
