import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/invoice_model.dart';
import '../models/customer_model.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();



  /// فتح الهاتف للاتصال بالعميل
  Future<bool> callCustomer(CustomerModel customer) async {
    try {
      final phoneNumber = _getCustomerPhone(customer);
      if (phoneNumber == null) {
        throw Exception('رقم الهاتف غير متوفر للعميل');
      }

      final callUrl = 'tel:$phoneNumber';
      if (await canLaunchUrl(Uri.parse(callUrl))) {
        await launchUrl(Uri.parse(callUrl));
        return true;
      } else {
        throw Exception('لا يمكن فتح الهاتف');
      }
    } catch (e) {
      debugPrint('خطأ في فتح الهاتف: $e');
      return false;
    }
  }

  /// الحصول على رقم هاتف العميل
  String? _getCustomerPhone(CustomerModel customer) {
    // أولوية للهاتف الأول، ثم الثاني
    if (customer.phone1 != null && customer.phone1!.isNotEmpty) {
      return _formatPhoneNumber(customer.phone1!);
    } else if (customer.phone2 != null && customer.phone2!.isNotEmpty) {
      return _formatPhoneNumber(customer.phone2!);
    }
    return null;
  }

  /// تنسيق رقم الهاتف
  String _formatPhoneNumber(String phone) {
    // إزالة المسافات والرموز
    String cleaned = phone.replaceAll(RegExp(r'[\s\-\(\)\+]'), '');
    
    // إضافة رمز البلد إذا لم يكن موجوداً
    if (cleaned.startsWith('0')) {
      cleaned = '20${cleaned.substring(1)}';
    } else if (!cleaned.startsWith('20')) {
      cleaned = '20$cleaned';
    }
    
    return cleaned;
  }



  /// بناء رابط الواتساب
  String _buildWhatsAppUrl(String phoneNumber, String message) {
    final encodedMessage = Uri.encodeComponent(message);
    return 'https://wa.me/$phoneNumber?text=$encodedMessage';
  }

  /// بناء رابط الرسائل النصية
  String _buildSMSUrl(String phoneNumber, String message) {
    final encodedMessage = Uri.encodeComponent(message);
    return 'sms:$phoneNumber?body=$encodedMessage';
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }



  /// إنشاء ملخص الإشعارات المرسلة
  String createNotificationSummary(Map<String, bool> results) {
    final buffer = StringBuffer();
    buffer.writeln('ملخص الإشعارات المرسلة:');
    buffer.writeln('');
    
    if (results['whatsapp'] == true) {
      buffer.writeln('✅ تم إرسال إشعار الواتساب بنجاح');
    } else {
      buffer.writeln('❌ فشل في إرسال إشعار الواتساب');
    }
    
    if (results['sms'] == true) {
      buffer.writeln('✅ تم إرسال الرسالة النصية بنجاح');
    } else {
      buffer.writeln('❌ فشل في إرسال الرسالة النصية');
    }
    
    return buffer.toString();
  }
}
