import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';

class ProductActionsDialog extends StatelessWidget {
  final ProductModel product;

  const ProductActionsDialog({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            _buildDialogHeader(),
            
            // قائمة الإجراءات
            _buildActionsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.more_vert,
              color: AppColors.textOnPrimary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجراءات سريعة',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                Text(
                  product.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsList(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // معلومات سريعة عن المنتج
          _buildQuickInfo(),
          
          const SizedBox(height: 24),
          
          // قائمة الإجراءات
          _buildActionItem(
            context,
            'تفاصيل المنتج',
            'عرض جميع المعلومات التفصيلية',
            Icons.info_outline,
            AppColors.info,
            () => Navigator.of(context).pop('details'),
          ),
          
          _buildActionItem(
            context,
            'تعديل المنتج',
            'تعديل بيانات المنتج',
            Icons.edit,
            AppColors.warning,
            () => Navigator.of(context).pop('edit'),
          ),
          
          _buildActionItem(
            context,
            'بيع المنتج',
            'إنشاء فاتورة بيع',
            Icons.sell,
            AppColors.success,
            () => Navigator.of(context).pop('sell'),
          ),
          
          _buildActionItem(
            context,
            'تعديل الكمية',
            'تعديل كمية المخزون',
            Icons.inventory_2,
            AppColors.primary,
            () => Navigator.of(context).pop('adjust_quantity'),
          ),
          
          if (product.quantity > 0) ...[
            _buildActionItem(
              context,
              'إضافة للمخزون',
              'زيادة كمية المخزون',
              Icons.add_box,
              AppColors.success,
              () => Navigator.of(context).pop('add_stock'),
            ),
          ],
          
          _buildActionItem(
            context,
            'نسخ الكود',
            'نسخ كود المنتج',
            Icons.copy,
            AppColors.secondary,
            () {
              Navigator.of(context).pop('copy_code');
            },
          ),
          
          if (product.hasBarcode) ...[
            _buildActionItem(
              context,
              'نسخ الباركود',
              'نسخ باركود المنتج',
              Icons.qr_code,
              AppColors.accent,
              () {
                Navigator.of(context).pop('copy_barcode');
              },
            ),
          ],
          
          const SizedBox(height: 16),
          
          // إجراءات متقدمة
          _buildAdvancedActions(context),
          
          const SizedBox(height: 24),
          
          // زر الإغلاق
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: AppColors.border),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'إغلاق',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  'السعر',
                  '${product.price} ${AppStrings.currencySymbol}',
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoChip(
                  'المخزون',
                  '${product.quantity} ${product.unitDisplayName}',
                  _getStockColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  'الفئة',
                  product.categoryDisplayName,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoChip(
                  'الحالة',
                  product.stockStatus,
                  _getStockStatusColor(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textHint,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات متقدمة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildAdvancedActionButton(
                context,
                'حذف المنتج',
                Icons.delete_forever,
                AppColors.error,
                () => Navigator.of(context).pop('delete'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAdvancedActionButton(
                context,
                'إيقاف/تفعيل',
                product.isActive ? Icons.pause : Icons.play_arrow,
                product.isActive ? AppColors.warning : AppColors.success,
                () => Navigator.of(context).pop('toggle_status'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdvancedActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStockColor() {
    if (product.quantity == 0) return AppColors.error;
    if (product.isLowStock) return AppColors.warning;
    return AppColors.success;
  }

  Color _getStockStatusColor() {
    switch (product.stockStatus) {
      case 'نفذ':
        return AppColors.error;
      case 'منخفض':
        return AppColors.warning;
      case 'متوفر':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }
}
