import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../widgets/customer_card.dart';
import 'customer_details_screen.dart';

class CustomerActivationScreen extends ConsumerStatefulWidget {
  const CustomerActivationScreen({super.key});

  @override
  ConsumerState<CustomerActivationScreen> createState() => _CustomerActivationScreenState();
}

class _CustomerActivationScreenState extends ConsumerState<CustomerActivationScreen> {
  final CustomerService _customerService = CustomerService();
  
  bool _isLoading = false;
  List<CustomerModel> _allCustomers = [];
  List<CustomerModel> _filteredCustomers = [];
  String _searchQuery = '';
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  Future<void> _loadCustomers() async {
    setState(() => _isLoading = true);
    try {
      _allCustomers = await _customerService.getAllCustomers();
      _applyFilters();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملاء: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _applyFilters() {
    _filteredCustomers = _allCustomers.where((customer) {
      // تطبيق فلتر الحالة
      if (_showActiveOnly && !customer.isActive) return false;
      if (!_showActiveOnly && customer.isActive) return false;
      
      // تطبيق البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return customer.name.toLowerCase().contains(query) ||
               customer.activity.toLowerCase().contains(query) ||
               (customer.phone1?.toLowerCase().contains(query) ?? false) ||
               (customer.governorate?.toLowerCase().contains(query) ?? false);
      }
      
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          'إدارة حالة العملاء',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white, size: 20.sp),
        actions: [
          IconButton(
            onPressed: _loadCustomers,
            icon: Icon(Icons.refresh, color: Colors.white, size: 24.sp),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          _buildSearchAndFilterBar(),
          
          // عداد العملاء
          _buildCustomerCounter(),
          
          // قائمة العملاء
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                  )
                : _buildCustomerList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // حقل البحث
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            decoration: InputDecoration(
              hintText: 'البحث في العملاء...',
              hintStyle: TextStyle(
                fontFamily: 'Cairo',
                color: AppColors.textSecondary,
              ),
              prefixIcon: Icon(Icons.search, color: AppColors.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // أزرار الفلترة
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _showActiveOnly = true;
                      _applyFilters();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _showActiveOnly ? AppColors.success : AppColors.border,
                    foregroundColor: _showActiveOnly ? Colors.white : AppColors.textSecondary,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle, size: 18.sp),
                      SizedBox(width: 8.w),
                      Text(
                        'العملاء النشطين',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(width: 12.w),
              
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _showActiveOnly = false;
                      _applyFilters();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: !_showActiveOnly ? AppColors.error : AppColors.border,
                    foregroundColor: !_showActiveOnly ? Colors.white : AppColors.textSecondary,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.cancel, size: 18.sp),
                      SizedBox(width: 8.w),
                      Text(
                        'العملاء غير النشطين',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCounter() {
    final activeCount = _allCustomers.where((c) => c.isActive).length;
    final inactiveCount = _allCustomers.where((c) => !c.isActive).length;
    final filteredCount = _filteredCustomers.length;
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          _buildCounterItem(
            icon: Icons.check_circle,
            label: 'نشطين',
            count: activeCount,
            color: AppColors.success,
          ),
          SizedBox(width: 16.w),
          _buildCounterItem(
            icon: Icons.cancel,
            label: 'غير نشطين',
            count: inactiveCount,
            color: AppColors.error,
          ),
          SizedBox(width: 16.w),
          _buildCounterItem(
            icon: Icons.filter_list,
            label: 'المعروضين',
            count: filteredCount,
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildCounterItem({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerList() {
    if (_filteredCustomers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _showActiveOnly ? Icons.check_circle : Icons.cancel,
              size: 64.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 24.h),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'لا يوجد عملاء ${_showActiveOnly ? 'نشطين' : 'غير نشطين'}',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              _searchQuery.isNotEmpty
                  ? 'جرب تغيير كلمات البحث'
                  : 'جرب تغيير الفلتر أو أضف عملاء جدد',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _filteredCustomers.length,
      itemBuilder: (context, index) {
        final customer = _filteredCustomers[index];
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          child: CustomerCard(
            customer: customer,
            onTap: () => _viewCustomerDetails(customer),
            onEdit: () => _editCustomer(customer),
            onDelete: () => _deleteCustomer(customer),
          ),
        );
      },
    );
  }

  void _viewCustomerDetails(CustomerModel customer) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailsScreen(customer: customer),
      ),
    );
    
    // إذا تم حذف العميل، قم بتحديث القائمة
    if (result != null && result is Map) {
      if (result['deleted'] == true) {
        // إزالة العميل من القائمة فوراً
        setState(() {
          _allCustomers.removeWhere((c) => c.id == result['customerId']);
        });
        
        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف العميل بنجاح',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    }
  }

  void _editCustomer(CustomerModel customer) async {
    // يمكن إضافة شاشة تعديل سريع هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة ميزة التعديل السريع قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        backgroundColor: AppColors.info,
      ),
    );
  }

  Future<void> _deleteCustomer(CustomerModel customer) async {
    showDeleteProtectionDialog(
      context: context,
      title: 'حذف العميل',
      message: 'سيتم حذف العميل نهائياً من قاعدة البيانات. لا يمكن التراجع عن هذا الإجراء.',
      itemName: customer.name ?? 'العميل المحدد',
      onConfirm: () async {
        try {
          setState(() => _isLoading = true);
          final success = await _customerService.deleteCustomer(customer.id);
          
          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم حذف العميل "${customer.name}" بنجاح',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.success,
              ),
            );
            _loadCustomers();
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'فشل في حذف العميل: $e',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                backgroundColor: AppColors.error,
              ),
            );
          }
        } finally {
          if (mounted) {
            setState(() => _isLoading = false);
          }
        }
      },
    );
  }
}
