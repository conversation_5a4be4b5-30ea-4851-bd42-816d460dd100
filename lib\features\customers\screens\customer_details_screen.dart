import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/customer_model.dart';
import '../../../constants/app_colors.dart';
import '../../../services/customer_service.dart';
import '../../../widgets/delete_protection_dialog.dart';

import 'add_customer_screen.dart';
import '../../invoices/screens/add_invoice_screen.dart';
import '../../invoices/screens/customer_invoices_screen.dart';

class CustomerDetailsScreen extends StatelessWidget {
  final CustomerModel customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'تفاصيل العميل',
          style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
        ),
        backgroundColor: Color(0xFF00CED1),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
      ),
      body: SafeArea(
        child: Container(
          color: Colors.white,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقة العميل الرئيسية
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Color(0xFF00CED1),
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // أيقونة العميل
                      Container(
                        width: 50.w,
                        height: 50.w,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.person,
                          size: 24.sp,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      // معلومات العميل
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // اسم العميل
                            Text(
                              customer.name ?? 'غير محدد',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontFamily: 'Cairo',
                              ),
                            ),
                            SizedBox(height: 4.h),
                            // النشاط
                            if (customer.activity?.isNotEmpty == true)
                              Text(
                                customer.activity!,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.white.withOpacity(0.9),
                                  fontFamily: 'Cairo',
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16.h),

                // معلومات الاتصال
                _buildInfoSection(
                  title: 'معلومات الاتصال',
                  icon: Icons.phone,
                  children: [
                    _buildInfoRow(
                      'رقم الهاتف 1',
                      customer.phone1 ?? 'غير محدد',
                    ),
                    if (customer.phone2?.isNotEmpty == true)
                      _buildInfoRow('رقم الهاتف 2', customer.phone2!),
                    if (customer.email?.isNotEmpty == true)
                      _buildInfoRow('البريد الإلكتروني', customer.email!),
                    SizedBox(height: 16.h),
                  ],
                ),

                SizedBox(height: 16.h),

                // معلومات الموقع
                _buildInfoSection(
                  title: 'معلومات الموقع',
                  icon: Icons.location_on,
                  children: [
                    _buildInfoRow(
                      'المحافظة',
                      customer.governorate ?? 'غير محدد',
                    ),
                    if (customer.city?.isNotEmpty == true)
                      _buildInfoRow('المدينة', customer.city!),
                    if (customer.street?.isNotEmpty == true)
                      _buildInfoRow('الشارع', customer.street!),
                    if (customer.building?.isNotEmpty == true)
                      _buildInfoRow('رقم المبنى', customer.building!),
                    if (customer.floor?.isNotEmpty == true)
                      _buildInfoRow('الطابق', customer.floor!),
                    if (customer.apartment?.isNotEmpty == true)
                      _buildInfoRow('الشقة', customer.apartment!),
                    if (customer.landmark?.isNotEmpty == true)
                      _buildInfoRow('علامة مميزة', customer.landmark!),
                  ],
                ),

                SizedBox(height: 16.h),

                // معلومات إضافية
                _buildInfoSection(
                  title: 'معلومات إضافية',
                  icon: Icons.info,
                  children: [
                    if (customer.notes?.isNotEmpty == true)
                      _buildInfoRow('ملاحظات', customer.notes!),
                    _buildInfoRow(
                      'تاريخ الإنشاء',
                      customer.createdAt != null
                          ? '${customer.createdAt!.day}/${customer.createdAt!.month}/${customer.createdAt!.year}'
                          : 'غير محدد',
                    ),
                    _buildInfoRow(
                      'آخر تحديث',
                      customer.updatedAt != null
                          ? '${customer.updatedAt!.day}/${customer.updatedAt!.month}/${customer.updatedAt!.year}'
                          : 'غير محدد',
                    ),
                  ],
                ),

                SizedBox(height: 24.h),

                // أزرار الإجراءات
                Column(
                  children: [
                    // صف الأزرار الأول - تعديل وحذف العميل
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      AddCustomerScreen(customer: customer),
                                ),
                              );

                              // إذا تم تحديث العميل، أرسل إشارة للشاشة السابقة
                              if (result != null &&
                                  result is Map &&
                                  result['updated'] == true) {
                                Navigator.pop(context, {
                                  'updated': true,
                                  'customerId': customer.id,
                                });
                              }
                            },
                            icon: Icon(Icons.edit, color: Colors.white),
                            label: Text(
                              'تعديل',
                              style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _showDeleteCustomerDialog(context),
                            icon: Icon(
                              Icons.delete_forever,
                              color: Colors.white,
                            ),
                            label: Text(
                              'حذف العميل',
                              style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[700],
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 12.h),

                    // صف الأزرار الثاني - إنشاء فاتورة وعرض الفواتير
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => AddInvoiceScreen(
                                    initialCustomer: customer,
                                  ),
                                ),
                              );
                            },
                            icon: Icon(Icons.receipt, color: Colors.white),
                            label: Text(
                              'إنشاء فاتورة',
                              style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF00CED1),
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => CustomerInvoicesScreen(
                                    customer: customer,
                                  ),
                                ),
                              );
                            },
                            icon: Icon(Icons.list_alt, color: Colors.white),
                            label: Text(
                              'عرض الفواتير',
                              style: TextStyle(
                                color: Colors.white,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Color(0xFF00CED1), size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: 'Cairo',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[800],
                fontFamily: 'Cairo',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteCustomerDialog(BuildContext context) {
    showDeleteProtectionDialog(
      context: context,
      title: 'حذف العميل',
      message:
          'سيتم حذف العميل نهائياً من قاعدة البيانات. لا يمكن التراجع عن هذا الإجراء.',
      itemName: customer.name ?? 'العميل المحدد',
      onConfirm: () async {
        try {
          final customerService = CustomerService();
          final success = await customerService.deleteCustomer(customer.id!);

          if (context.mounted) {
            if (success) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف العميل بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              Navigator.of(context).pop(); // العودة للشاشة السابقة
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('فشل في حذف العميل'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } catch (e) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في حذف العميل: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
    );
  }
}
