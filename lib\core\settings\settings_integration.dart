import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'settings_manager.dart';

/// دمج الإعدادات مع التطبيق الرئيسي
class SettingsIntegration {
  static final SettingsIntegration _instance = SettingsIntegration._internal();
  factory SettingsIntegration() => _instance;
  SettingsIntegration._internal();

  /// تهيئة الإعدادات عند بدء التطبيق
  Future<void> initializeSettings(WidgetRef ref) async {
    try {
      // تحميل الإعدادات المحفوظة
      final settings = ref.read(settingsProvider);
      await settings.load();

      debugPrint('تم تهيئة الإعدادات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة الإعدادات: $e');
    }
  }

  /// تطبيق الإعدادات عند تغييرها
  Future<void> applySettingsChange(WidgetRef ref) async {
    try {
      final settings = ref.read(settingsProvider);
      debugPrint('تم تطبيق الإعدادات الجديدة');
    } catch (e) {
      debugPrint('خطأ في تطبيق الإعدادات: $e');
    }
  }

  /// الحصول على إعدادات المظهر
  ThemeData getThemeData(WidgetRef ref) {
    final settings = ref.read(settingsProvider);

    // تطبيق إعدادات المظهر
    if (settings.enableAdaptiveTheme) {
      return _getAdaptiveTheme();
    } else {
      return _getDefaultTheme();
    }
  }

  /// الحصول على إعدادات اللغة
  Locale getLocale(WidgetRef ref) {
    final settings = ref.read(settingsProvider);

    switch (settings.selectedLanguage) {
      case 'ar':
        return const Locale('ar', 'SA');
      case 'en':
        return const Locale('en', 'US');
      default:
        return const Locale('ar', 'SA');
    }
  }

  /// الحصول على اتجاه النص
  TextDirection getTextDirection(WidgetRef ref) {
    final settings = ref.read(settingsProvider);

    if (settings.enableRTL) {
      return TextDirection.rtl;
    } else {
      return TextDirection.ltr;
    }
  }

  /// تطبيق إعدادات الأمان
  void applySecuritySettings() {
    // منع التقاط الشاشة
    _disableScreenCapture();

    // فحص الأجهزة المعدلة
    _checkRootStatus();
  }

  /// تطبيق إعدادات الأداء
  void applyPerformanceSettings() {
    // إزالة رسائل التصحيح
    _removeDebugLogs();

    // تفعيل ضغط الصور
    _enableImageCompression();
  }

  // Private Methods

  ThemeData _getAdaptiveTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2196F3),
        brightness: Brightness.light,
      ),
      fontFamily: 'Cairo',
      textTheme: const TextTheme(
        bodyLarge: TextStyle(fontFamily: 'Cairo'),
        bodyMedium: TextStyle(fontFamily: 'Cairo'),
        titleLarge: TextStyle(fontFamily: 'Cairo'),
        titleMedium: TextStyle(fontFamily: 'Cairo'),
      ),
    );
  }

  ThemeData _getDefaultTheme() {
    return ThemeData(
      useMaterial3: true,
      primarySwatch: Colors.blue,
      fontFamily: 'Cairo',
      textTheme: const TextTheme(
        bodyLarge: TextStyle(fontFamily: 'Cairo'),
        bodyMedium: TextStyle(fontFamily: 'Cairo'),
        titleLarge: TextStyle(fontFamily: 'Cairo'),
        titleMedium: TextStyle(fontFamily: 'Cairo'),
      ),
    );
  }

  void _disableScreenCapture() {
    // منع التقاط الشاشة
    debugPrint('تم تفعيل منع التقاط الشاشة');
  }

  void _checkRootStatus() {
    // فحص حالة الجهاز
    debugPrint('تم تفعيل فحص الأجهزة المعدلة');
  }

  void _removeDebugLogs() {
    // إزالة رسائل التصحيح
    debugPrint = (String? message, {int? wrapWidth}) {
      // لا تفعل شيئاً في الإنتاج
    };
  }

  void _enableImageCompression() {
    // تفعيل ضغط الصور
    debugPrint('تم تفعيل ضغط الصور');
  }
}
