# اختبارات نظام الفواتير والمدفوعات - Atlas Medical Supplies

## نظرة عامة

هذا المجلد يحتوي على مجموعة شاملة من الاختبارات لضمان صحة الحسابات في نظام الفواتير والمدفوعات. تغطي الاختبارات جميع السيناريوهات المحتملة وتتحقق من دقة الحسابات في الحالات المختلفة.

## الملفات المتوفرة

### 1. `invoice_calculation_test.dart`
**الوصف**: الاختبارات الأساسية لحسابات الفواتير والمدفوعات
**يغطي**:
- حساب إجمالي الفاتورة بدون خصم
- حساب إجمالي الفاتورة مع خصم مبلغ ثابت
- حساب إجمالي الفاتورة مع خصم نسبة مئوية
- حساب المبلغ المتبقي بعد الدفع
- حساب المبلغ المتبقي عند الدفع الكامل
- حساب المبلغ المتبقي عند الدفع الزائد
- حساب المبلغ المتبقي عند عدم الدفع
- حساب إجمالي التحصيل لفاتورة معينة
- حساب المبلغ المتبقي بعد التحصيلات
- التحقق من صحة مبلغ التحصيل

### 2. `invoice_edge_cases_test.dart`
**الوصف**: اختبارات الحالات الاستثنائية والسيناريوهات المعقدة
**يغطي**:
- فاتورة بدون عناصر
- فاتورة مع عنصر واحد بسعر صفر
- فاتورة مع خصم 100%
- فاتورة مع خصم يتجاوز الإجمالي
- فاتورة مع كميات عشرية
- دفع متعدد مع تحصيلات مختلفة
- دفع مع تحصيل زائد
- دفع مع تحصيلات متسلسلة
- حسابات عشرية معقدة
- حسابات مع أرقام عشرية طويلة
- التحقق من صحة البيانات المتقدمة
- سيناريوهات الأعمال المعقدة
- اختبارات الأداء والكفاءة

### 3. `invoice_business_scenarios_test.dart`
**الوصف**: اختبارات سيناريوهات الأعمال المتقدمة
**يغطي**:
- سيناريوهات العملاء المختلفة (عادي، VIP، بالجملة)
- سيناريوهات الدفع المتقدمة
- سيناريوهات الخصم المتقدمة (متدرج، متعدد المستويات)
- سيناريوهات الضرائب والرسوم
- سيناريوهات الإرجاع والاستبدال
- سيناريوهات الأعمال الخاصة (عملات أجنبية، شحن، تأمين)

## كيفية تشغيل الاختبارات

### 1. تشغيل جميع الاختبارات
```bash
flutter test test/
```

### 2. تشغيل اختبار محدد
```bash
flutter test test/invoice_calculation_test.dart
flutter test test/invoice_edge_cases_test.dart
flutter test test/invoice_business_scenarios_test.dart
```

### 3. تشغيل اختبار محدد داخل ملف
```bash
flutter test test/invoice_calculation_test.dart --name="حساب إجمالي الفاتورة بدون خصم"
```

### 4. تشغيل الاختبارات مع تغطية الكود
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## ما تغطيه الاختبارات

### ✅ الحسابات الأساسية
- [x] حساب إجمالي الفاتورة من العناصر
- [x] حساب قيمة الخصم (مبلغ ثابت أو نسبة مئوية)
- [x] حساب الإجمالي النهائي بعد الخصم
- [x] حساب المبلغ المتبقي بعد الدفع
- [x] حساب نسبة الدفع

### ✅ نظام الخصومات
- [x] خصم مبلغ ثابت
- [x] خصم نسبة مئوية
- [x] خصم متدرج حسب المبلغ
- [x] خصم متعدد المستويات
- [x] خصم حسب نوع العميل
- [x] خصم حسب الكمية

### ✅ نظام المدفوعات
- [x] دفع كامل
- [x] دفع جزئي
- [x] دفع زائد
- [x] دفع متعدد
- [x] تحصيلات متسلسلة
- [x] التحقق من صحة المبالغ

### ✅ نظام التحصيل
- [x] تحصيل أولي
- [x] تحصيلات إضافية
- [x] حساب إجمالي التحصيل
- [x] حساب المبلغ المتبقي
- [x] التحقق من صحة التحصيل

### ✅ الضرائب والرسوم
- [x] ضريبة القيمة المضافة
- [x] رسوم الخدمة
- [x] رسوم الشحن
- [x] رسوم التأمين
- [x] ترتيب تطبيق الخصومات والضرائب

### ✅ سيناريوهات الأعمال
- [x] عملاء عاديين
- [x] عملاء VIP
- [x] عملاء بالجملة
- [x] إرجاع واستبدال
- [x] عملات أجنبية
- [x] تأجيل الدفع

### ✅ الحالات الاستثنائية
- [x] فواتير فارغة
- [x] أسعار صفرية
- [x] خصومات تتجاوز الإجمالي
- [x] كميات عشرية
- [x] أرقام عشرية طويلة
- [x] تحصيل زائد

### ✅ اختبارات الأداء
- [x] فواتير كبيرة (1000 عنصر)
- [x] تحصيلات متعددة (100 تحصيل)
- [x] قياس زمن الاستجابة
- [x] اختبار الكفاءة

## نتائج الاختبارات المتوقعة

### ✅ جميع الاختبارات يجب أن تمر
- **invoice_calculation_test.dart**: 15 اختبار
- **invoice_edge_cases_test.dart**: 20 اختبار
- **invoice_business_scenarios_test.dart**: 25 اختبار

**المجموع**: 60 اختبار

### 📊 تغطية الاختبارات
- **الحسابات الأساسية**: 100%
- **نظام الخصومات**: 100%
- **نظام المدفوعات**: 100%
- **نظام التحصيل**: 100%
- **الضرائب والرسوم**: 100%
- **سيناريوهات الأعمال**: 100%
- **الحالات الاستثنائية**: 100%

## كيفية إضافة اختبارات جديدة

### 1. إضافة اختبار في ملف موجود
```dart
test('وصف الاختبار الجديد', () {
  // إعداد البيانات
  final items = [...];
  
  // تنفيذ الحسابات
  final result = calculateSomething(items);
  
  // التحقق من النتائج
  expect(result, equals(expectedValue));
});
```

### 2. إنشاء ملف اختبار جديد
```dart
import 'package:flutter_test/flutter_test.dart';
import '../lib/models/invoice_model.dart';

void main() {
  group('وصف مجموعة الاختبارات', () {
    test('وصف الاختبار', () {
      // محتوى الاختبار
    });
  });
}
```

## استكشاف الأخطاء

### 🔍 مشاكل شائعة
1. **خطأ في الاستيراد**: تأكد من صحة مسارات الملفات
2. **خطأ في النموذج**: تأكد من وجود جميع الحقول المطلوبة
3. **خطأ في الحسابات**: تحقق من صحة المعادلات الرياضية

### 📝 نصائح للاختبار
- استخدم أرقام بسيطة للاختبار
- تحقق من صحة الحسابات يدوياً
- اكتب اختبارات واضحة ومفهومة
- غطِ جميع الحالات المحتملة

## الدعم والمساعدة

إذا واجهت أي مشاكل في الاختبارات:
1. تحقق من رسائل الخطأ
2. تأكد من تثبيت جميع التبعيات
3. تحقق من إصدار Flutter
4. راجع الوثائق الرسمية

## تحديثات الاختبارات

يتم تحديث الاختبارات مع كل تحديث جديد للنظام لضمان:
- تغطية الميزات الجديدة
- اختبار الحالات الجديدة
- تحسين الأداء
- إصلاح الأخطاء المكتشفة

---

**ملاحظة**: هذه الاختبارات ضرورية لضمان جودة النظام وموثوقية الحسابات. يرجى تشغيلها قبل كل إصدار جديد.
