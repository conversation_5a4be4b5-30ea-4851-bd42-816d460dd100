import 'package:flutter_test/flutter_test.dart';
import '../lib/models/invoice_model.dart';
import '../lib/models/customer_model.dart';

void main() {
  group('Invoice Sharing Phone Number Tests', () {
    test('should get best phone number from customer data', () {
      // إنشاء عميل تجريبي مع أرقام هواتف
      final customer = CustomerModel(
        id: 'test-customer-1',
        name: 'عميل تجريبي',
        phone1: '01234567890',
        phone2: '09876543210',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إنشاء فاتورة تجريبية
      final invoice = InvoiceModel(
        id: 'test-invoice-1',
        invoiceNumber: 'INV-2024-001',
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: '01111111111', // رقم مختلف عن العميل
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 100.0,
        total: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // اختبار الحصول على أفضل رقم هاتف
      String bestPhone = _getBestPhoneNumberForTest(customer, invoice);

      // يجب أن يعطي الأولوية لـ phone1
      expect(bestPhone, equals('01234567890'));
    });

    test('should fallback to phone2 when phone1 is empty', () {
      // إنشاء عميل تجريبي مع phone1 فارغ
      final customer = CustomerModel(
        id: 'test-customer-2',
        name: 'عميل تجريبي 2',
        phone1: '', // فارغ
        phone2: '09876543210',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إنشاء فاتورة تجريبية
      final invoice = InvoiceModel(
        id: 'test-invoice-2',
        invoiceNumber: 'INV-2024-002',
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: '01111111111',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 100.0,
        total: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // اختبار الحصول على أفضل رقم هاتف
      String bestPhone = _getBestPhoneNumberForTest(customer, invoice);

      // يجب أن يعطي الأولوية لـ phone2
      expect(bestPhone, equals('09876543210'));
    });

    test('should fallback to invoice phone when customer phones are empty', () {
      // إنشاء عميل تجريبي بدون أرقام هواتف
      final customer = CustomerModel(
        id: 'test-customer-3',
        name: 'عميل تجريبي 3',
        phone1: null,
        phone2: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إنشاء فاتورة تجريبية
      final invoice = InvoiceModel(
        id: 'test-invoice-3',
        invoiceNumber: 'INV-2024-003',
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: '01111111111',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 100.0,
        total: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // اختبار الحصول على أفضل رقم هاتف
      String bestPhone = _getBestPhoneNumberForTest(customer, invoice);

      // يجب أن يستخدم رقم الفاتورة
      expect(bestPhone, equals('01111111111'));
    });

    test('should format phone number correctly', () {
      // اختبار تنسيق أرقام الهاتف
      expect(_formatPhoneNumberForTest('01234567890'), equals('2001234567890'));
      expect(_formatPhoneNumberForTest('201234567890'), equals('201234567890'));
      expect(
        _formatPhoneNumberForTest('+201234567890'),
        equals('201234567890'),
      );
      expect(
        _formatPhoneNumberForTest('00 201234567890'),
        equals('201234567890'),
      );
      expect(_formatPhoneNumberForTest('************'), equals('200123456789'));
      expect(_formatPhoneNumberForTest(''), equals(''));
      expect(_formatPhoneNumberForTest('abc'), equals(''));
    });

    test('should handle null customer gracefully', () {
      // إنشاء فاتورة تجريبية بدون عميل
      final invoice = InvoiceModel(
        id: 'test-invoice-4',
        invoiceNumber: 'INV-2024-004',
        customerId: 'unknown',
        customerName: 'عميل غير معروف',
        customerPhone: '01111111111',
        invoiceDate: DateTime.now(),
        items: [],
        subtotal: 100.0,
        total: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // اختبار الحصول على أفضل رقم هاتف مع عميل null
      String bestPhone = _getBestPhoneNumberForTest(null, invoice);

      // يجب أن يستخدم رقم الفاتورة
      expect(bestPhone, equals('01111111111'));
    });
  });
}

// دوال مساعدة للاختبار (نسخة مبسطة من الكود الحقيقي)
String _getBestPhoneNumberForTest(
  CustomerModel? customer,
  InvoiceModel invoice,
) {
  // محاولة الحصول على رقم الهاتف من بيانات العميل إذا كانت متوفرة
  if (customer != null) {
    // أولوية للهاتف الأول
    if (customer.phone1 != null && customer.phone1!.isNotEmpty) {
      return customer.phone1!;
    }
    // ثم الهاتف الثاني
    if (customer.phone2 != null && customer.phone2!.isNotEmpty) {
      return customer.phone2!;
    }
  }

  // إذا لم يكن هناك رقم هاتف من العميل، استخدم الرقم المخزن في الفاتورة
  return invoice.customerPhone;
}

String _formatPhoneNumberForTest(String phoneNumber) {
  // تنظيف رقم الهاتف من الرموز غير الرقمية
  String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

  // التحقق من أن رقم الهاتف صحيح
  if (cleaned.isEmpty) {
    return '';
  }

  // إزالة الرموز الدولية إذا كانت موجودة
  if (cleaned.startsWith('00')) {
    cleaned = cleaned.substring(2);
  } else if (cleaned.startsWith('+')) {
    cleaned = cleaned.substring(1);
  }

  // إزالة الرمز الدولي لمصر إذا كان موجوداً
  if (cleaned.startsWith('20')) {
    cleaned = cleaned.substring(2);
  }

  // إضافة رمز الدولة لمصر
  cleaned = '20$cleaned';

  return cleaned;
}
