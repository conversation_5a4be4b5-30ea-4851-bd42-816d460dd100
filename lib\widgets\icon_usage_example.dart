import 'package:flutter/material.dart';
import 'app_icon.dart';

class IconUsageExample extends StatelessWidget {
  const IconUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('App Icons Example')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic usage
            const Text(
              'Basic Icons (24px)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AppIcon(type: AppIconType.home),
                AppIcon(type: AppIconType.profile),
                AppIcon(type: AppIconType.settings),
                AppIcon(type: AppIconType.notifications),
                AppIcon(type: AppIconType.search),
                AppIcon(type: AppIconType.camera),
              ],
            ),

            const SizedBox(height: 32),

            // Different sizes
            const Text(
              'Different Sizes',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                AppIconSmall(type: AppIconType.home),
                const SizedBox(width: 16),
                AppIconMedium(type: AppIconType.home),
                const SizedBox(width: 16),
                AppIconLarge(type: AppIconType.home),
                const SizedBox(width: 16),
                AppIcon(type: AppIconType.home, size: 48),
              ],
            ),

            const SizedBox(height: 32),

            // Active vs Inactive states
            const Text(
              'Active vs Inactive States',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                AppIcon(type: AppIconType.home, isActive: true),
                const SizedBox(width: 16),
                AppIcon(type: AppIconType.home, isActive: false),
                const SizedBox(width: 16),
                AppIcon(type: AppIconType.profile, isActive: true),
                const SizedBox(width: 16),
                AppIcon(type: AppIconType.profile, isActive: false),
              ],
            ),

            const SizedBox(height: 32),

            // Custom colors
            const Text(
              'Custom Colors',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AppIcon(type: AppIconType.edit, color: Colors.blue),
                AppIcon(type: AppIconType.delete, color: Colors.red),
                AppIcon(type: AppIconType.save, color: Colors.green),
                AppIcon(type: AppIconType.share, color: Colors.orange),
                AppIcon(type: AppIconType.location, color: Colors.purple),
                AppIcon(type: AppIconType.camera, color: Colors.teal),
              ],
            ),

            const SizedBox(height: 32),

            // Action icons
            const Text(
              'Action Icons',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AppIcon(type: AppIconType.edit),
                AppIcon(type: AppIconType.delete),
                AppIcon(type: AppIconType.save),
                AppIcon(type: AppIconType.share),
                AppIcon(type: AppIconType.login),
                AppIcon(type: AppIconType.logout),
              ],
            ),

            const SizedBox(height: 32),

            // Communication icons
            const Text(
              'Communication Icons',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AppIcon(type: AppIconType.messages),
                AppIcon(type: AppIconType.notifications),
                AppIcon(type: AppIconType.share),
              ],
            ),

            const SizedBox(height: 32),

            // Interactive example
            const Text(
              'Interactive Example',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _InteractiveIconExample(),
          ],
        ),
      ),
    );
  }
}

class _InteractiveIconExample extends StatefulWidget {
  @override
  State<_InteractiveIconExample> createState() =>
      _InteractiveIconExampleState();
}

class _InteractiveIconExampleState extends State<_InteractiveIconExample> {
  bool isLiked = false;
  bool isBookmarked = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              isLiked = !isLiked;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isLiked ? Colors.red.shade100 : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: AppIcon(
              type: AppIconType.save,
              color: isLiked ? Colors.red : Colors.grey.shade600,
              isActive: isLiked,
            ),
          ),
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: () {
            setState(() {
              isBookmarked = !isBookmarked;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isBookmarked ? Colors.blue.shade100 : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: AppIcon(
              type: AppIconType.share,
              color: isBookmarked ? Colors.blue : Colors.grey.shade600,
              isActive: isBookmarked,
            ),
          ),
        ),
      ],
    );
  }
}
