import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../models/sales_model.dart';
import '../../../utils/screen_utils.dart';

class SalesExportDialog extends StatelessWidget {
  final List<SalesModel> sales;
  final Map<String, dynamic> statistics;

  const SalesExportDialog({
    super.key,
    required this.sales,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: isSmallScreen ? double.infinity : 400.w,
        padding: EdgeInsets.all(isSmallScreen ? 20.w : 24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.file_download,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تصدير المبيعات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                    fontFamily: 'Cairo',
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  iconSize: 20.sp,
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // معلومات التصدير
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات التصدير',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  SizedBox(height: 8.h),
                  _InfoRow(
                    label: 'عدد الفواتير:',
                    value: '${sales.length} فاتورة',
                    isSmallScreen: isSmallScreen,
                  ),
                  _InfoRow(
                    label: 'إجمالي المبيعات:',
                    value: NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س ',
                      decimalDigits: 2,
                    ).format(statistics['totalSales'] ?? 0.0),
                    isSmallScreen: isSmallScreen,
                  ),
                  _InfoRow(
                    label: 'المدفوع:',
                    value: NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س ',
                      decimalDigits: 2,
                    ).format(statistics['totalPaid'] ?? 0.0),
                    isSmallScreen: isSmallScreen,
                  ),
                  _InfoRow(
                    label: 'المتبقي:',
                    value: NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س ',
                      decimalDigits: 2,
                    ).format(statistics['totalRemaining'] ?? 0.0),
                    isSmallScreen: isSmallScreen,
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.h),

            // خيارات التصدير
            Text(
              'اختر نوع التصدير',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 12.h),

            // زر تصدير Excel
            _ExportButton(
              title: 'تصدير إلى Excel',
              subtitle: 'ملف Excel مع تفاصيل كاملة',
              icon: Icons.table_chart,
              color: Colors.green,
              onTap: () => _exportToExcel(context),
              isSmallScreen: isSmallScreen,
            ),
            SizedBox(height: 8.h),

            // زر تصدير PDF
            _ExportButton(
              title: 'تصدير إلى PDF',
              subtitle: 'تقرير PDF منسق',
              icon: Icons.picture_as_pdf,
              color: Colors.red,
              onTap: () => _exportToPDF(context),
              isSmallScreen: isSmallScreen,
            ),
            SizedBox(height: 8.h),

            // زر تصدير CSV
            _ExportButton(
              title: 'تصدير إلى CSV',
              subtitle: 'ملف نصي بسيط',
              icon: Icons.text_snippet,
              color: Colors.blue,
              onTap: () => _exportToCSV(context),
              isSmallScreen: isSmallScreen,
            ),
            SizedBox(height: 20.h),

            // ملاحظة
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'سيتم حفظ الملف في مجلد التنزيلات',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.blue.shade700,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _exportToExcel(BuildContext context) {
    // هنا سيتم إضافة منطق تصدير Excel
    _showExportMessage(context, 'Excel');
  }

  void _exportToPDF(BuildContext context) {
    // هنا سيتم إضافة منطق تصدير PDF
    _showExportMessage(context, 'PDF');
  }

  void _exportToCSV(BuildContext context) {
    // هنا سيتم إضافة منطق تصدير CSV
    _showExportMessage(context, 'CSV');
  }

  void _showExportMessage(BuildContext context, String format) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم إضافة تصدير $format قريباً'),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final bool isSmallScreen;

  const _InfoRow({
    required this.label,
    required this.value,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey.shade600,
              fontFamily: 'Cairo',
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }
}

class _ExportButton extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final bool isSmallScreen;

  const _ExportButton({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            border: Border.all(color: color.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8.r),
            color: color.withOpacity(0.05),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: color,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey.shade600,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16.sp),
            ],
          ),
        ),
      ),
    );
  }
}
