import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/egyptian_governorates.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../services/image_service.dart';
import '../../../utils/auto_save_manager.dart';
import '../../../widgets/back_button.dart';
import '../widgets/customer_form_section.dart';
import '../widgets/address_form_section.dart';
import '../../../services/database_service.dart';
import 'customer_details_screen.dart';

class AddCustomerScreen extends StatefulWidget {
  final CustomerModel? customer;

  const AddCustomerScreen({super.key, this.customer});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final CustomerService _customerService = CustomerService();
  final AutoSaveManager _autoSaveManager = AutoSaveManager();

  // Form controllers
  final _nameController = TextEditingController();
  final _activityController = TextEditingController();
  final _phone1Controller = TextEditingController();
  final _phone2Controller = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _notesController = TextEditingController();

  // Address controllers
  final _streetController = TextEditingController();
  final _buildingController = TextEditingController();
  final _floorController = TextEditingController();
  final _apartmentController = TextEditingController();
  final _landmarkController = TextEditingController();

  // Form data
  CustomerType _selectedType = CustomerType.medicalOfficeA;
  String? _selectedGovernorate;
  String? _selectedCity;

  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    if (widget.customer != null) {
      _isEditing = true;
      _populateFormData();
    }
  }

  void _populateFormData() {
    final customer = widget.customer!;
    _nameController.text = customer.name;
    _activityController.text = customer.activity;
    _phone1Controller.text = customer.phone1 ?? '';
    _phone2Controller.text = customer.phone2 ?? '';
    _taxNumberController.text = customer.taxNumber ?? '';
    _notesController.text = customer.notes ?? '';

    _streetController.text = customer.street ?? '';
    _buildingController.text = customer.building ?? '';
    _floorController.text = customer.floor ?? '';
    _apartmentController.text = customer.apartment ?? '';
    _landmarkController.text = customer.landmark ?? '';

    _selectedType = customer.type;

    // طباعة تشخيصية
    print('Debug: customer.governorate = "${customer.governorate}"');
    print('Debug: customer.city = "${customer.city}"');
    print('Debug: customer.village = "${customer.village}"');

    // إعادة تعيين قيم العنوان
    _selectedGovernorate = null;
    _selectedCity = null;

    // التحقق من صحة المحافظة وإعادة تعيينها إذا كانت غير صحيحة
    if (customer.governorate != null &&
        customer.governorate!.isNotEmpty &&
        customer.governorate!.trim().isNotEmpty) {
      final governorates = EgyptianGovernorates.getAllGovernorates();
      if (governorates.contains(customer.governorate!.trim())) {
        _selectedGovernorate = customer.governorate!.trim();
        print('Debug: _selectedGovernorate set to "${_selectedGovernorate}"');

        // التحقق من صحة المدينة وإعادة تعيينها إذا كانت غير صحيحة
        if (customer.city != null &&
            customer.city!.isNotEmpty &&
            customer.city!.trim().isNotEmpty) {
          final cities = EgyptianGovernorates.getCitiesForGovernorate(
            _selectedGovernorate!,
          );
          if (cities.contains(customer.city!.trim())) {
            _selectedCity = customer.city!.trim();
            print('Debug: _selectedCity set to "${_selectedCity}"');
          } else {
            print('Debug: _selectedCity reset to null (invalid city)');
          }
        }
      } else {
        print(
          'Debug: _selectedGovernorate reset to null (invalid governorate)',
        );
      }
    } else {
      print('Debug: All address values reset to null (no valid governorate)');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _activityController.dispose();
    _phone1Controller.dispose();
    _phone2Controller.dispose();
    _taxNumberController.dispose();
    _notesController.dispose();
    _streetController.dispose();
    _buildingController.dispose();
    _floorController.dispose();
    _apartmentController.dispose();
    _landmarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          _isEditing ? 'تعديل العميل' : 'إضافة عميل جديد',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white, size: 20.sp),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomerFormSection(
                nameController: _nameController,
                activityController: _activityController,
                phone1Controller: _phone1Controller,
                phone2Controller: _phone2Controller,
                taxNumberController: _taxNumberController,
                notesController: _notesController,
                selectedType: _selectedType,
                onTypeChanged: (type) {
                  setState(() {
                    _selectedType = type;
                  });
                },
              ),
              SizedBox(height: 24.h),
              AddressFormSection(
                streetController: _streetController,
                buildingController: _buildingController,
                floorController: _floorController,
                apartmentController: _apartmentController,
                landmarkController: _landmarkController,
                selectedGovernorate: _selectedGovernorate,
                selectedCity: _selectedCity,
                onGovernorateChanged: (value) {
                  setState(() {
                    _selectedGovernorate = value;
                    _selectedCity = null;
                  });
                },
                onCityChanged: (value) {
                  setState(() {
                    _selectedCity = value;
                  });
                },
              ),
              SizedBox(height: 32.h),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveCustomer,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _isEditing ? 'حفظ التعديلات' : 'إضافة العميل',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Cairo',
                    ),
                  ),
          ),
        ),
        SizedBox(height: 12.h),
        // زر اختبار قاعدة البيانات (للتطوير فقط)
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _isLoading ? null : _testDatabase,
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12.h),
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'اختبار قاعدة البيانات',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ),
      ],
    );
  }

  // اختبار قاعدة البيانات
  Future<void> _testDatabase() async {
    try {
      print('=== بدء اختبار قاعدة البيانات ===');
      final databaseService = DatabaseService();

      // فحص قاعدة البيانات
      await databaseService.checkAndRepairDatabase();

      // محاولة إنشاء عميل تجريبي
      print('محاولة إنشاء عميل تجريبي...');
      final testResult = await databaseService.createTestCustomer();

      if (testResult) {
        print('✓ تم إنشاء العميل التجريبي بنجاح');

        // التحقق من وجود العملاء
        final customers = await databaseService.getAllCustomers();
        print('عدد العملاء في قاعدة البيانات: ${customers.length}');

        if (customers.isNotEmpty) {
          print(
            'آخر عميل تم إضافته: ${customers.last.name} (${customers.last.phone1})',
          );
        }

        if (mounted) {
          _showSuccessSnackBar(
            'تم اختبار قاعدة البيانات بنجاح - عدد العملاء: ${customers.length}',
          );
        }
      } else {
        print('✗ فشل في إنشاء العميل التجريبي');
        if (mounted) {
          _showErrorSnackBar('فشل في اختبار قاعدة البيانات');
        }
      }

      // اختبار إضافة عميل من خلال الخدمة
      print('اختبار إضافة عميل من خلال الخدمة...');
      final testCustomer = CustomerModel(
        id: '',
        name: 'عميل اختبار',
        type: CustomerType.medicalOfficeA,
        activity: 'اختبار',
        address: 'عنوان اختبار',
        governorate: 'القاهرة',
        city: 'القاهرة',
        street: 'شارع اختبار',
        phone1: '01111111111',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'test',
      );

      try {
        final success = await _customerService.addCustomer(testCustomer);
        if (success) {
          print('✓ تم اختبار خدمة العملاء بنجاح');
        } else {
          print('✗ فشل في اختبار خدمة العملاء');
        }
      } catch (e) {
        print('خطأ في اختبار خدمة العملاء: $e');
      }
    } catch (e) {
      print('=== خطأ في اختبار قاعدة البيانات ===');
      print('نوع الخطأ: ${e.runtimeType}');
      print('رسالة الخطأ: $e');
      if (mounted) {
        _showErrorSnackBar('خطأ في اختبار قاعدة البيانات: $e');
      }
    }
  }

  Future<void> _saveCustomer() async {
    print('=== بدء عملية حفظ العميل ===');

    // التحقق من صحة النموذج
    if (!_formKey.currentState!.validate()) {
      print('فشل في التحقق من صحة النموذج');
      _showErrorSnackBar('يرجى التأكد من صحة البيانات المدخلة');
      return;
    }

    // التحقق من البيانات المطلوبة
    if (_nameController.text.trim().isEmpty) {
      print('اسم العميل فارغ');
      _showErrorSnackBar('يرجى إدخال اسم العميل');
      return;
    }

    if (_phone1Controller.text.trim().isEmpty) {
      print('رقم الهاتف فارغ');
      _showErrorSnackBar('يرجى إدخال رقم الهاتف');
      return;
    }

    if (_selectedGovernorate == null || _selectedGovernorate!.isEmpty) {
      print('لم يتم اختيار المحافظة');
      _showErrorSnackBar('يرجى اختيار المحافظة');
      return;
    }

    // التحقق من أن المحافظة موجودة في القائمة
    if (!EgyptianGovernorates.hasGovernorate(_selectedGovernorate!)) {
      print('المحافظة المختارة غير صحيحة: ${_selectedGovernorate}');
      _showErrorSnackBar('المحافظة المختارة غير صحيحة');
      return;
    }

    // التحقق من أن المدينة (إذا تم اختيارها) موجودة في المحافظة المختارة
    if (_selectedCity != null && _selectedCity!.isNotEmpty) {
      if (!EgyptianGovernorates.hasCityInGovernorate(
        _selectedGovernorate!,
        _selectedCity!,
      )) {
        print(
          'المدينة المختارة غير موجودة في المحافظة: ${_selectedCity} في ${_selectedGovernorate}',
        );
        _showErrorSnackBar('المدينة المختارة غير موجودة في المحافظة المختارة');
        return;
      }
    }

    // تحقق إضافي من صحة البيانات بعد إصلاح المحافظات
    print('=== تحقق إضافي من صحة البيانات ===');
    print('المحافظة المختارة: "${_selectedGovernorate}"');
    print('المدينة المختارة: "${_selectedCity}"');

    // التحقق من أن المحافظة صحيحة
    final allGovernorates = EgyptianGovernorates.getAllGovernorates();
    if (!allGovernorates.contains(_selectedGovernorate!)) {
      print('خطأ: المحافظة غير موجودة في القائمة الكاملة');
      _showErrorSnackBar('خطأ في بيانات المحافظة');
      return;
    }

    // التحقق من أن المدينة صحيحة (إذا تم اختيارها)
    if (_selectedCity != null && _selectedCity!.isNotEmpty) {
      final cities = EgyptianGovernorates.getCitiesForGovernorate(
        _selectedGovernorate!,
      );
      if (!cities.contains(_selectedCity!)) {
        print('خطأ: المدينة غير موجودة في قائمة مدن المحافظة');
        _showErrorSnackBar('خطأ في بيانات المدينة');
        return;
      }
    }

    // التحقق من صحة تنسيق أرقام الهاتف
    if (!_customerService.isValidPhoneFormat(_phone1Controller.text.trim())) {
      _showErrorSnackBar(
        'تنسيق رقم الهاتف الأول غير صحيح (يجب أن يبدأ بـ 01 ويتكون من 10-11 رقم)',
      );
      return;
    }

    if (_phone2Controller.text.trim().isNotEmpty &&
        !_customerService.isValidPhoneFormat(_phone2Controller.text.trim())) {
      _showErrorSnackBar(
        'تنسيق رقم الهاتف الثاني غير صحيح (يجب أن يبدأ بـ 01 ويتكون من 10-11 رقم)',
      );
      return;
    }

    // التحقق من عدم التكرار
    print('التحقق من عدم التكرار...');
    setState(() {
      _isLoading = true;
    });

    try {
      // تنظيف وتأكيد صحة البيانات قبل إنشاء النموذج
      final cleanGovernorate = _selectedGovernorate!.trim();
      final cleanCity = _selectedCity?.trim() ?? '';

      // التحقق النهائي من صحة المحافظة والمدينة
      if (!EgyptianGovernorates.hasGovernorate(cleanGovernorate)) {
        print('خطأ نهائي: المحافظة غير صحيحة: "$cleanGovernorate"');
        _showErrorSnackBar('خطأ في بيانات المحافظة');
        return;
      }

      if (cleanCity.isNotEmpty &&
          !EgyptianGovernorates.hasCityInGovernorate(
            cleanGovernorate,
            cleanCity,
          )) {
        print(
          'خطأ نهائي: المدينة غير صحيحة: "$cleanCity" في "$cleanGovernorate"',
        );
        _showErrorSnackBar('خطأ في بيانات المدينة');
        return;
      }

      // إنشاء نموذج مؤقت للتحقق
      final tempCustomer = CustomerModel(
        id: _isEditing ? widget.customer!.id : '',
        name: _nameController.text.trim(),
        phone1: _phone1Controller.text.trim(),
        phone2: _phone2Controller.text.trim().isEmpty
            ? null
            : _phone2Controller.text.trim(),
        email: null, // سيتم إضافته لاحقاً إذا كان موجوداً
        type: _selectedType,
        activity: _activityController.text.trim(),
        governorate: cleanGovernorate,
        city: cleanCity,
        street: _streetController.text.trim().isEmpty
            ? null
            : _streetController.text.trim(),
        building: _buildingController.text.trim().isEmpty
            ? null
            : _buildingController.text.trim(),
        floor: _floorController.text.trim().isEmpty
            ? null
            : _floorController.text.trim(),
        apartment: _apartmentController.text.trim().isEmpty
            ? null
            : _apartmentController.text.trim(),
        landmark: _landmarkController.text.trim().isEmpty
            ? null
            : _landmarkController.text.trim(),
        taxNumber: _taxNumberController.text.trim().isEmpty
            ? null
            : _taxNumberController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        balance: _isEditing ? widget.customer!.balance : 0.0,
        latitude: null,
        longitude: null,
        isActive: _isEditing ? widget.customer!.isActive : true,
        createdAt: _isEditing ? widget.customer!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        lastOrderDate: _isEditing ? widget.customer!.lastOrderDate : null,
        createdBy: _isEditing ? widget.customer!.createdBy : 'current_user',
      );

      // التحقق من التكرار
      final duplicates = await _customerService.checkAllDuplicates(
        tempCustomer,
        excludeId: _isEditing ? widget.customer!.id : null,
      );

      // عرض رسائل الخطأ للتكرار
      List<String> duplicateMessages = [];

      if (duplicates['name'] == true) {
        duplicateMessages.add('اسم العميل موجود بالفعل');
      }

      if (duplicates['phone1'] == true) {
        duplicateMessages.add('رقم الهاتف الأول مستخدم بالفعل');
      }

      if (duplicates['phone2'] == true) {
        duplicateMessages.add('رقم الهاتف الثاني مستخدم بالفعل');
      }

      if (duplicates['duplicate_phones'] == true) {
        duplicateMessages.add('لا يمكن استخدام نفس رقم الهاتف في الحقلين');
      }

      if (duplicates['email'] == true) {
        duplicateMessages.add('البريد الإلكتروني مستخدم بالفعل');
      }

      if (duplicates['tax_number'] == true) {
        duplicateMessages.add('الرقم الضريبي مستخدم بالفعل');
      }

      if (duplicateMessages.isNotEmpty) {
        final message =
            'لا يمكن إضافة العميل:\n${duplicateMessages.join('\n')}';
        _showErrorSnackBar(message);
        setState(() {
          _isLoading = false;
        });
        return;
      }

      print('✓ لا يوجد تكرار، يمكن المتابعة...');

      print('البيانات المدخلة صحيحة، بدء عملية الحفظ...');
      print('الاسم: ${_nameController.text.trim()}');
      print('رقم الهاتف: ${_phone1Controller.text.trim()}');
      print('المحافظة: $_selectedGovernorate');
      print('المدينة: $_selectedCity');
      print('النوع: $_selectedType');

      // إنشاء نموذج العميل النهائي
      print('إنشاء نموذج العميل...');
      final phoneNumber = _phone1Controller.text.trim();
      final customer = CustomerModel(
        id: _isEditing ? widget.customer!.id : '',
        name: _nameController.text.trim(),
        type: _selectedType,
        activity: _activityController.text.trim().isEmpty
            ? ''
            : _activityController.text.trim(),
        address: _streetController.text.trim().isEmpty
            ? null
            : _streetController.text.trim(),
        governorate: _selectedGovernorate!,
        city: _selectedCity ?? '',
        street: _streetController.text.trim().isEmpty
            ? null
            : _streetController.text.trim(),
        building: _buildingController.text.trim().isEmpty
            ? null
            : _buildingController.text.trim(),
        floor: _floorController.text.trim().isEmpty
            ? null
            : _floorController.text.trim(),
        apartment: _apartmentController.text.trim().isEmpty
            ? null
            : _apartmentController.text.trim(),
        landmark: _landmarkController.text.trim().isEmpty
            ? null
            : _landmarkController.text.trim(),
        phone1: phoneNumber,
        phone2: _phone2Controller.text.trim().isEmpty
            ? null
            : _phone2Controller.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        balance: _isEditing ? widget.customer!.balance : 0.0,
        latitude: null,
        longitude: null,
        isActive: _isEditing ? widget.customer!.isActive : true,
        createdAt: _isEditing ? widget.customer!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        lastOrderDate: _isEditing ? widget.customer!.lastOrderDate : null,
        createdBy: _isEditing ? widget.customer!.createdBy : 'current_user',
      );

      print('تم إنشاء نموذج العميل: ${customer.name}');
      print('بيانات العميل: ${customer.toMap()}');

      if (_isEditing) {
        print('تحديث العميل الموجود...');
        final success = await _autoSaveManager.updateCustomerWithAutoSave(
          customer,
          context: context,
          showNotification: false,
        );
        if (success && mounted) {
          _showSuccessSnackBar('تم تحديث العميل بنجاح');
          Navigator.pop(context, {'updated': true, 'customerId': customer.id});
        } else if (mounted) {
          _showErrorSnackBar('فشل في تحديث العميل');
        }
      } else {
        print('إضافة عميل جديد...');
        final success = await _autoSaveManager.addCustomerWithAutoSave(
          customer,
          context: context,
          showNotification: false,
        );
        if (success && mounted) {
          _showSuccessSnackBar('تم إضافة العميل بنجاح');

          // إرسال إشارة للشاشة السابقة مع العميل الجديد
          Navigator.pop(context, {'added': true, 'customer': customer});
        } else if (mounted) {
          _showErrorSnackBar('فشل في إضافة العميل - تحقق من البيانات المدخلة');
        }
      }
    } catch (e) {
      print('=== خطأ في حفظ العميل ===');
      print('نوع الخطأ: ${e.runtimeType}');
      print('رسالة الخطأ: $e');

      String errorMessage = 'حدث خطأ غير متوقع';

      if (e.toString().contains('رقم الهاتف مستخدم بالفعل')) {
        errorMessage = 'رقم الهاتف مستخدم بالفعل';
      } else if (e.toString().contains('اسم العميل مطلوب')) {
        errorMessage = 'اسم العميل مطلوب';
      } else if (e.toString().contains('رقم الهاتف مطلوب')) {
        errorMessage = 'رقم الهاتف مطلوب';
      } else if (e.toString().contains('المحافظة مطلوبة')) {
        errorMessage = 'المحافظة مطلوبة';
      } else if (e.toString().contains('بيانات العميل غير صحيحة')) {
        errorMessage = 'بيانات العميل غير صحيحة';
      } else if (e.toString().contains('معرف العميل مكرر')) {
        errorMessage = 'معرف العميل مكرر';
      } else if (e.toString().contains('يوجد حقل مطلوب فارغ')) {
        errorMessage = 'يوجد حقل مطلوب فارغ';
      } else if (e.toString().contains('خطأ في العلاقات بين الجداول')) {
        errorMessage = 'خطأ في قاعدة البيانات';
      } else if (e.toString().contains('يوجد تضارب في البيانات')) {
        errorMessage = 'يوجد تضارب في البيانات';
      }

      if (mounted) {
        _showErrorSnackBar(
          _isEditing
              ? 'فشل في تحديث العميل: $errorMessage'
              : 'فشل في إضافة العميل: $errorMessage',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(fontFamily: 'Cairo')),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(fontFamily: 'Cairo')),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
