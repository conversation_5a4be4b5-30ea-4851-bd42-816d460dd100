# نظام منع تكرار أرقام الهاتف

## نظرة عامة
تم تطوير نظام شامل لمنع تكرار أرقام الهاتف في تطبيق ATLAS2. هذا النظام يضمن عدم إمكانية إضافة نفس رقم الهاتف لأكثر من عميل واحد.

## الميزات المضافة

### 1. التحقق من تنسيق رقم الهاتف
- التحقق من أن الرقم يبدأ بـ `01` (أرقام مصر)
- التحقق من أن الرقم يتكون من 10-11 رقم
- دعم تنسيقات مختلفة: `01012345678`، `+201012345678`، `00201012345678`

### 2. منع التكرار بين العملاء
- التحقق من عدم استخدام نفس رقم الهاتف في `phone1` لأكثر من عميل
- التحقق من عدم استخدام نفس رقم الهاتف في `phone2` لأكثر من عميل
- التحقق من عدم استخدام نفس الرقم في `phone1` و `phone2` لنفس العميل

### 3. تطبيع أرقام الهاتف
- إزالة المسافات والرموز غير الرقمية
- إزالة الرموز الدولية (`+`، `00`)
- إزالة الرمز الدولي لمصر (`20`)

## كيفية العمل

### في شاشة إضافة العميل
1. **التحقق من التنسيق**: يتم التحقق من صحة تنسيق رقم الهاتف قبل الإرسال
2. **التحقق من التكرار**: يتم فحص قاعدة البيانات للتأكد من عدم وجود تكرار
3. **رسائل الخطأ**: عرض رسائل واضحة في حالة وجود تكرار

### في خدمة العملاء
- `isValidPhoneFormat()`: التحقق من صحة تنسيق الرقم
- `isPhoneExists()`: التحقق من وجود الرقم في قاعدة البيانات
- `checkAllDuplicates()`: فحص شامل لجميع أنواع التكرار
- `_normalizePhoneNumber()`: تطبيع الرقم للمقارنة

## أمثلة على الرسائل

### رسائل التكرار
- "رقم الهاتف الأول مستخدم بالفعل"
- "رقم الهاتف الثاني مستخدم بالفعل"
- "لا يمكن استخدام نفس رقم الهاتف في الحقلين"

### رسائل التنسيق
- "تنسيق رقم الهاتف الأول غير صحيح (يجب أن يبدأ بـ 01 ويتكون من 10-11 رقم)"

## الفوائد

1. **منع التكرار**: لا يمكن إضافة نفس الرقم لأكثر من عميل
2. **تحسين الجودة**: التأكد من صحة تنسيق الأرقام
3. **تجربة مستخدم أفضل**: رسائل خطأ واضحة ومفيدة
4. **حماية البيانات**: منع تضارب البيانات في النظام

## الاستخدام

```dart
// التحقق من صحة تنسيق الرقم
if (!customerService.isValidPhoneFormat(phoneNumber)) {
  // عرض رسالة خطأ
}

// التحقق من عدم التكرار
final duplicates = await customerService.checkAllDuplicates(customer);
if (duplicates['phone1'] == true) {
  // رقم الهاتف الأول مستخدم بالفعل
}
```

## التحديثات المستقبلية

- إضافة دعم لأرقام هواتف من دول أخرى
- تحسين أداء فحص التكرار لقواعد البيانات الكبيرة
- إضافة خيارات تخصيص لقواعد التحقق
