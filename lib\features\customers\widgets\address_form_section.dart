import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/egyptian_governorates.dart';

class AddressFormSection extends StatelessWidget {
  final TextEditingController streetController;
  final TextEditingController buildingController;
  final TextEditingController floorController;
  final TextEditingController apartmentController;
  final TextEditingController landmarkController;
  final String? selectedGovernorate;
  final String? selectedCity;
  final ValueChanged<String?> onGovernorateChanged;
  final ValueChanged<String?> onCityChanged;

  const AddressFormSection({
    super.key,
    required this.streetController,
    required this.buildingController,
    required this.floorController,
    required this.apartmentController,
    required this.landmarkController,
    required this.selectedGovernorate,
    required this.selectedCity,
    required this.onGovernorateChanged,
    required this.onCityChanged,
  });

  // دالة مساعدة للتحقق من صحة القيم
  String? _getValidValue(String? value, List<String> validOptions) {
    if (value == null || value.isEmpty || value.trim().isEmpty) {
      print('Debug: _getValidValue - value is null/empty: "$value"');
      return null;
    }

    final trimmedValue = value.trim();

    // طباعة تشخيصية
    print('Debug: _getValidValue - value: "$value", trimmed: "$trimmedValue"');
    print(
      'Debug: _getValidValue - validOptions contains: ${validOptions.contains(trimmedValue)}',
    );
    print('Debug: _getValidValue - validOptions: $validOptions');

    // التحقق من أن القائمة ليست فارغة
    if (validOptions.isEmpty) {
      print('Debug: _getValidValue - validOptions is empty');
      return null;
    }

    // التحقق من أن القيمة موجودة في القائمة
    if (validOptions.contains(trimmedValue)) {
      print('Debug: _getValidValue - value is valid: "$trimmedValue"');
      return trimmedValue;
    }

    print('Debug: _getValidValue - value is NOT valid: "$trimmedValue"');
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primary, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'العنوان',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildGovernorateDropdown(),
          SizedBox(height: 16.h),
          _buildCityDropdown(),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: streetController,
            label: 'الشارع',
            hint: 'أدخل اسم الشارع (اختياري)',
            isRequired: false,
            icon: Icons.streetview,
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  controller: buildingController,
                  label: 'رقم المبنى',
                  hint: 'رقم المبنى',
                  icon: Icons.apartment,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildTextField(
                  controller: floorController,
                  label: 'الطابق',
                  hint: 'رقم الطابق',
                  icon: Icons.layers,
                  keyboardType: TextInputType.number,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildTextField(
                  controller: apartmentController,
                  label: 'الشقة',
                  hint: 'رقم الشقة',
                  icon: Icons.home,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            controller: landmarkController,
            label: 'علامة مميزة',
            hint: 'مثال: بجوار مسجد النور، أمام مدرسة...',
            icon: Icons.place,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    bool isRequired = false,
    IconData? icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: 4.w),
              Text(
                '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.error,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          textAlign: TextAlign.right,
          textDirection: TextDirection.rtl,
          keyboardType: keyboardType,
          enableInteractiveSelection: true,
          obscureText: false,
          autocorrect: false,
          enableSuggestions: false,
          readOnly: false,
          enabled: true,
          showCursor: true,
          cursorColor: AppColors.primary,
          cursorWidth: 2.0,
          cursorRadius: const Radius.circular(1.0),
          cursorHeight: 20.0,
          cursorOpacityAnimates: true,
          style: TextStyle(fontSize: 14.sp, fontFamily: 'Cairo'),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            isDense: true,
            prefixIcon: icon != null
                ? Icon(icon, color: AppColors.textSecondary, size: 20.sp)
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.error),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildGovernorateDropdown() {
    // طباعة تشخيصية
    print(
      'Debug: _buildGovernorateDropdown - selectedGovernorate = "$selectedGovernorate"',
    );

    // التحقق من صحة القيمة المحددة باستخدام الدالة المساعدة
    final validGovernorate = _getValidValue(
      selectedGovernorate,
      EgyptianGovernorates.getAllGovernorates(),
    );

    // طباعة تشخيصية
    if (selectedGovernorate != null && selectedGovernorate!.isNotEmpty) {
      print(
        'Debug: _buildGovernorateDropdown - validGovernorate = "$validGovernorate"',
      );
    }

    // التحقق من أن القيمة صحيحة قبل عرض القائمة
    if (validGovernorate != null &&
        !EgyptianGovernorates.getAllGovernorates().contains(validGovernorate)) {
      print(
        'Debug: _buildGovernorateDropdown - WARNING: validGovernorate not in list!',
      );
      print(
        'Debug: _buildGovernorateDropdown - validGovernorate: "$validGovernorate"',
      );
      print(
        'Debug: _buildGovernorateDropdown - allGovernorates: ${EgyptianGovernorates.getAllGovernorates()}',
      );
      return Container(); // إرجاع حاوية فارغة لتجنب الخطأ
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'المحافظة',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              '*',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.error,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.border),
          ),
          child: DropdownButtonFormField<String>(
            value: validGovernorate,
            hint: Text(
              'اختر المحافظة',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14.sp,
                fontFamily: 'Cairo',
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(
                Icons.location_city,
                color: AppColors.textSecondary,
                size: 20.sp,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
            ),
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
            dropdownColor: Colors.white,
            isExpanded: true,
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.textSecondary,
              size: 20.sp,
            ),
            items: EgyptianGovernorates.getAllGovernorates()
                .toSet()
                .toList()
                .map((governorate) {
                  return DropdownMenuItem<String>(
                    value: governorate,
                    child: Text(
                      governorate,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: 'Cairo',
                        color: AppColors.textPrimary,
                      ),
                    ),
                  );
                })
                .toList(),
            onChanged: (value) {
              onGovernorateChanged?.call(value);
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'المحافظة مطلوبة';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCityDropdown() {
    // طباعة تشخيصية
    print(
      'Debug: _buildCityDropdown - selectedGovernorate = "$selectedGovernorate"',
    );

    final cities = selectedGovernorate != null
        ? EgyptianGovernorates.getCitiesForGovernorate(selectedGovernorate!)
        : <String>[];

    // إزالة التكرار من القائمة
    final uniqueCities = cities.toSet().toList();

    // طباعة تشخيصية
    print('Debug: _buildCityDropdown - cities = $cities');
    print('Debug: _buildCityDropdown - uniqueCities = $uniqueCities');

    // التحقق من صحة القيمة المحددة باستخدام الدالة المساعدة
    final validCity = _getValidValue(selectedCity, uniqueCities);

    // طباعة تشخيصية
    if (selectedCity != null && selectedCity!.isNotEmpty) {
      print('Debug: _buildCityDropdown - selectedCity = "$selectedCity"');
      print('Debug: _buildCityDropdown - validCity = "$validCity"');
    }

    // التحقق من أن القيمة صحيحة قبل عرض القائمة
    if (validCity != null && !uniqueCities.contains(validCity)) {
      print(
        'Debug: _buildCityDropdown - WARNING: validCity not in cities list!',
      );
      print('Debug: _buildCityDropdown - validCity: "$validCity"');
      print('Debug: _buildCityDropdown - uniqueCities: $uniqueCities');
      return Container(); // إرجاع حاوية فارغة لتجنب الخطأ
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المدينة / المركز',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.border),
          ),
          child: DropdownButtonFormField<String>(
            value: validCity,
            decoration: InputDecoration(
              hintText: 'اختر المدينة',
              hintStyle: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14.sp,
                fontFamily: 'Cairo',
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
            ),
            items: cities
                .map(
                  (city) => DropdownMenuItem<String>(
                    value: city,
                    child: Text(
                      city,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 14.sp,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                )
                .toList(),
            onChanged: (value) {
              onCityChanged?.call(value);
            },
            validator: (value) {
              // المدينة اختيارية، لا نحتاج للتحقق منها
              // لكن إذا تم اختيارها، نتأكد من أنها صحيحة
              if (value != null && value.isNotEmpty) {
                if (selectedGovernorate != null &&
                    !EgyptianGovernorates.hasCityInGovernorate(
                      selectedGovernorate!,
                      value,
                    )) {
                  return 'المدينة المختارة غير موجودة في المحافظة المختارة';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}
