import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';

class CustomersAnalyticsMenu extends StatefulWidget {
  const CustomersAnalyticsMenu({super.key});

  @override
  State<CustomersAnalyticsMenu> createState() => _CustomersAnalyticsMenuState();
}

class _CustomersAnalyticsMenuState extends State<CustomersAnalyticsMenu>
    with TickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _heightAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس القائمة مع السهم
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    size: 24.sp,
                    color: AppColors.primary,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'قائمة الإحصائيات والمؤشرات',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const Spacer(),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      size: 24.sp,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // محتوى القائمة (قابل للطي)
          AnimatedBuilder(
            animation: _heightAnimation,
            builder: (context, child) {
              return SizeTransition(
                sizeFactor: _heightAnimation,
                child: _isExpanded
                    ? _buildAnalyticsContent()
                    : const SizedBox.shrink(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsContent() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // إحصائيات متقدمة
          _buildAnalyticsSection(
            'إحصائيات متقدمة',
            Icons.bar_chart,
            AppColors.primary,
            _buildAdvancedStatsContent(),
          ),
          SizedBox(height: 16.h),

          // مؤشرات الأداء
          _buildAnalyticsSection(
            'مؤشرات الأداء',
            Icons.trending_up,
            AppColors.success,
            _buildPerformanceKPIsContent(),
          ),
          SizedBox(height: 16.h),

          // التصنيف التلقائي
          _buildAnalyticsSection(
            'التصنيف التلقائي',
            Icons.category,
            AppColors.warning,
            _buildAutoClassificationContent(),
          ),
          SizedBox(height: 16.h),

          // مؤشرات المخزون
          _buildAnalyticsSection(
            'مؤشرات المخزون',
            Icons.inventory_2,
            AppColors.info,
            _buildInventoryMetricsContent(),
          ),
          SizedBox(height: 16.h),

          // إحصائيات الأرباح
          _buildAnalyticsSection(
            'إحصائيات الأرباح',
            Icons.monetization_on,
            AppColors.secondary,
            _buildProfitStatsContent(),
          ),
          SizedBox(height: 16.h),

          // التنبيهات الذكية
          _buildAnalyticsSection(
            'التنبيهات الذكية',
            Icons.notifications_active,
            AppColors.error,
            _buildSmartAlertsContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsSection(
    String title,
    IconData icon,
    Color color,
    Widget content,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: ExpansionTile(
        title: Row(
          children: [
            Icon(icon, color: color, size: 20.sp),
            SizedBox(width: 8.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: color,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        children: [Padding(padding: EdgeInsets.all(16.w), child: content)],
      ),
    );
  }

  Widget _buildAdvancedStatsContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        if (customers.isEmpty) {
          return const Text('لا توجد عملاء لعرض الإحصائيات');
        }

        final governorateStats = <String, int>{};
        final typeStats = <String, int>{};

        for (final customer in customers) {
          governorateStats[customer.governorate ?? 'غير محدد'] =
              (governorateStats[customer.governorate ?? 'غير محدد'] ?? 0) + 1;
          typeStats[customer.type.toString().split('.').last] =
              (typeStats[customer.type.toString().split('.').last] ?? 0) + 1;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجمالي العملاء: ${customers.length}',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'التوزيع حسب المحافظات:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            ...governorateStats.entries.map(
              (entry) =>
                  _buildStatItem(entry.key, entry.value, customers.length),
            ),
            SizedBox(height: 12.h),
            Text(
              'التوزيع حسب النوع:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            ...typeStats.entries.map(
              (entry) => _buildStatItem(
                _getCustomerTypeDisplayName(entry.key),
                entry.value,
                customers.length,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPerformanceKPIsContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        final activeCustomers = customers.where((c) => c.isActive).length;
        final newCustomers = customers.where((c) {
          final daysSinceCreation = DateTime.now()
              .difference(c.createdAt)
              .inDays;
          return daysSinceCreation <= 30;
        }).length;

        return Column(
          children: [
            _buildKPICard(
              'العملاء النشطين',
              activeCustomers,
              Icons.check_circle,
            ),
            SizedBox(height: 8.h),
            _buildKPICard('عملاء جدد (30 يوم)', newCustomers, Icons.person_add),
          ],
        );
      },
    );
  }

  Widget _buildAutoClassificationContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        final governorates = customers
            .map((c) => c.governorate ?? 'غير محدد')
            .toSet();
        final types = customers
            .map((c) => c.type.toString().split('.').last)
            .toSet();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المحافظات المتاحة:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: governorates
                  .map(
                    (governorate) => Chip(
                      label: Text(
                        governorate,
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                    ),
                  )
                  .toList(),
            ),
            SizedBox(height: 12.h),
            Text(
              'أنواع العملاء:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 8.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: types
                  .map(
                    (type) => Chip(
                      label: Text(
                        _getCustomerTypeDisplayName(type),
                        style: TextStyle(fontFamily: 'Cairo'),
                      ),
                      backgroundColor: AppColors.info.withValues(alpha: 0.1),
                    ),
                  )
                  .toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInventoryMetricsContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        final totalCreditLimit = customers.fold<double>(
          0,
          (sum, customer) => sum + customer.balance,
        );
        final avgCreditLimit = totalCreditLimit / customers.length;

        return Column(
          children: [
            _buildKPICard(
              'إجمالي حدود الائتمان',
              totalCreditLimit.round(),
              Icons.credit_card,
            ),
            SizedBox(height: 8.h),
            _buildKPICard(
              'متوسط حد الائتمان',
              avgCreditLimit.round(),
              Icons.analytics,
            ),
          ],
        );
      },
    );
  }

  Widget _buildProfitStatsContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        final premiumCustomers = customers
            .where((c) => c.type == CustomerType.majorClient)
            .length;
        final regularCustomers = customers
            .where(
              (c) =>
                  c.type == CustomerType.medicalOfficeA ||
                  c.type == CustomerType.medicalOfficeB,
            )
            .length;

        return Column(
          children: [
            _buildKPICard('عملاء مميزون', premiumCustomers, Icons.star),
            SizedBox(height: 8.h),
            _buildKPICard('عملاء عاديون', regularCustomers, Icons.person),
            SizedBox(height: 8.h),
            Text(
              'العملاء المميزون يمثلون ${(premiumCustomers / customers.length * 100).toStringAsFixed(1)}% من إجمالي العملاء',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSmartAlertsContent() {
    return FutureBuilder<List<CustomerModel>>(
      future: CustomerService().getCustomers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = snapshot.data!;
        final inactiveCustomers = customers.where((c) => !c.isActive).toList();
        final highCreditCustomers = customers
            .where((c) => c.balance > 10000)
            .toList();

        return Column(
          children: [
            if (inactiveCustomers.isNotEmpty)
              _buildAlertCard(
                'عملاء غير نشطين',
                '${inactiveCustomers.length} عميل',
                Icons.person_off,
                AppColors.warning,
              ),
            if (highCreditCustomers.isNotEmpty) ...[
              SizedBox(height: 8.h),
              _buildAlertCard(
                'عملاء بحدود ائتمان عالية',
                '${highCreditCustomers.length} عميل',
                Icons.warning,
                AppColors.error,
              ),
            ],
            if (inactiveCustomers.isEmpty && highCreditCustomers.isEmpty)
              _buildAlertCard(
                'جميع العملاء بخير',
                'لا توجد تنبيهات',
                Icons.check_circle,
                AppColors.success,
              ),
          ],
        );
      },
    );
  }

  Widget _buildStatItem(String label, int value, int total) {
    final percentage = (value / total * 100).toStringAsFixed(1);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(fontSize: 12.sp, fontFamily: 'Cairo'),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '$value',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '$percentage%',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKPICard(String title, int value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20.sp, color: AppColors.primary),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontSize: 12.sp, fontFamily: 'Cairo'),
                ),
                Text(
                  value.toString(),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard(
    String title,
    String message,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20.sp, color: color),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: color,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: color,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getCustomerTypeDisplayName(String type) {
    switch (type) {
      case 'distributor':
        return 'موزع';
      case 'medicalofficea':
        return 'مكتب طبي أ';
      case 'medicalofficeb':
        return 'مكتب طبي ب';
      case 'majorclient':
        return 'عميل كبير';
      default:
        return type;
    }
  }
}
