import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/settings/settings_manager.dart';

import 'constants/app_colors.dart';
import 'constants/app_strings.dart';
import 'features/auth/screens/login_screen.dart';
import 'features/dashboard/screens/dashboard_screen.dart';
import 'features/settings/screens/settings_screen.dart';
import 'features/profile/screens/profile_screen.dart';


import 'services/auth_service.dart';
import 'services/database_service.dart';
import 'services/sales_service.dart';
import 'utils/auto_save_manager.dart';

import 'utils/theme_fix.dart'; // إضافة استيراد ThemeFix

void main() {
  // تحسين الأداء عند بدء التطبيق
  WidgetsFlutterBinding.ensureInitialized();

  // تعيين وضع الأداء العالي
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // تعيين اتجاه الشاشة مسبقاً
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    ProviderScope(
      child: provider.MultiProvider(
        providers: [
          provider.ChangeNotifierProvider(create: (_) => AuthService()),
        ],
        child: const AtlasMedicalApp(),
      ),
    ),
  );
}

// شاشة التحميل الأولية محسنة للأداء
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // تقليل مدد الحركة بشكل كبير لتسريع الافتتاح
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400), // تقليل من 900ms
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300), // تقليل من 700ms
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _scaleController, curve: Curves.easeOut));

    _startAnimations();
  }

  void _startAnimations() {
    _fadeController.forward();
    _scaleController.forward();

    // بدء التهيئة في الخلفية فوراً
    _initializeAppInBackground();

    // تقليل وقت الانتظار قبل فحص الحالة
    Future.delayed(const Duration(milliseconds: 600), () {
      // تقليل من 1200ms
      if (mounted) {
        _checkAuthStatus();
      }
    });
  }

  // تهيئة التطبيق في الخلفية محسنة
  void _initializeAppInBackground() async {
    try {
      // تهيئة قاعدة البيانات بشكل متوازي مع الحركات
      unawaited(_initializeDatabase());

      // تأخير تهيئة الخدمات غير الأساسية
      Future.delayed(const Duration(milliseconds: 150), () {
        unawaited(_initializeNonEssentialServices());
      });
    } catch (e) {
      debugPrint('Error initializing app: $e');
    }
  }

  // تهيئة قاعدة البيانات
  Future<void> _initializeDatabase() async {
    try {
      final databaseService = DatabaseService();
      await databaseService.database;

      // فحص وإصلاح قاعدة البيانات
      await databaseService.checkAndFixDatabase();

      debugPrint('Database initialized and checked successfully');
    } catch (e) {
      debugPrint('Database initialization error: $e');
    }
  }

  // تهيئة الخدمات غير الأساسية
  Future<void> _initializeNonEssentialServices() async {
    try {
      final autoSaveManager = AutoSaveManager();
      final salesService = SalesService();

      // بدء نظام الحفظ التلقائي
      autoSaveManager.startAutoSave();

      // مزامنة المبيعات من الفواتير الموجودة
      try {
        await salesService.syncSalesFromInvoices();
        debugPrint('Sales synchronized successfully');
      } catch (e) {
        debugPrint('Sales synchronization error: $e');
      }
    } catch (e) {
      debugPrint('Non-essential services initialization error: $e');
    }
  }

  // فحص حالة المصادقة محسن
  void _checkAuthStatus() async {
    try {
      final authService = provider.Provider.of<AuthService>(
        context,
        listen: false,
      );

      // التحقق من تفعيل التسجيل التلقائي
      final autoLoginEnabled = await authService.isAutoLoginEnabled();

      if (autoLoginEnabled) {
        // محاولة تسجيل الدخول التلقائي
        try {
          final user = await authService.autoSignIn();
          if (user != null && mounted) {
            Navigator.of(context).pushReplacementNamed('/dashboard');
            return;
          }
        } catch (e) {
          debugPrint('Auto sign-in failed: $e');
        }
      }

      // الانتقال إلى شاشة تسجيل الدخول
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppColors.primary, AppColors.secondary],
          ),
        ),
        child: Center(
          child: AnimatedBuilder(
            animation: Listenable.merge([_fadeController, _scaleController]),
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // شعار التطبيق محسن
                      Container(
                        width: 280.w,
                        height: 140.w,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Color(0xFF64B5F6), Color(0xFF26C6DA)],
                          ),
                          borderRadius: BorderRadius.circular(28.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 24,
                              offset: const Offset(0, 12),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'ATLAS',
                              style: TextStyle(
                                fontSize: 34.sp,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                                letterSpacing: 6,
                                fontFamily: 'Cairo',
                              ),
                            ),
                            SizedBox(height: 6.h),
                            Container(
                              width: 80.w,
                              height: 3.h,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(2.r),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            Text(
                              'MEDICAL SUPPLIES',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w700,
                                letterSpacing: 2,
                                color: Colors.white.withValues(alpha: 0.95),
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 24.h), // تقليل المسافات
                      // اسم التطبيق
                      Text(
                        'ATLAS',
                        style: TextStyle(
                          fontSize: 28.sp, // تقليل حجم الخط
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1.5,
                          fontFamily: 'Cairo',
                        ),
                      ),

                      SizedBox(height: 6.h),

                      // وصف التطبيق
                      Text(
                        'نظام إدارة توزيع المستلزمات الطبية',
                        style: TextStyle(
                          fontSize: 14.sp, // تقليل حجم الخط
                          color: Colors.white.withValues(alpha: 0.9),
                          fontFamily: 'Cairo',
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: 32.h), // تقليل المسافة
                      // مؤشر التحميل محسن
                      SizedBox(
                        width: 32.w, // تقليل الحجم
                        height: 32.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5.w, // تقليل سمك الخط
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class AtlasMedicalApp extends ConsumerStatefulWidget {
  const AtlasMedicalApp({super.key});

  @override
  ConsumerState<AtlasMedicalApp> createState() => _AtlasMedicalAppState();
}

// احتياطي لإلغاء كتم السجلات لاحقاً إذا أضفنا خياراً لذلك
// typedef _DebugPrintFn = void Function(String? message, {int? wrapWidth});
// _DebugPrintFn? _originalDebugPrint;

class _AtlasMedicalAppState extends ConsumerState<AtlasMedicalApp> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(settingsProvider);
    final locale = settings.selectedLanguage == 'ar'
        ? const Locale('ar', 'EG')
        : const Locale('en', 'US');

    // تبسيط منطق اختيار المظهر لضمان عدم ظهور الخلفية السوداء
    final themeMode = settings.selectedTheme == 'dark'
        ? ThemeMode.dark
        : ThemeMode.light; // دائماً نستخدم المظهر الفاتح كقيمة افتراضية

    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: settings.enableResponsiveLayout,
      builder: (context, child) {
        return MaterialApp(
          title: AppStrings.appName,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar', 'EG'), Locale('en', 'US')],
          locale: locale,
          theme: _buildLightTheme(),
          darkTheme: _buildDarkTheme(),
          themeMode: themeMode,
          builder: (context, appChild) {
            final direction = settings.enableRTL
                ? TextDirection.rtl
                : TextDirection.ltr;
            return Directionality(
              textDirection: direction,
              child: appChild ?? const SizedBox(),
            );
          },
          home: const AuthWrapper(),
          routes: {
            // '/welcome': (context) => const WelcomeScreen(),
            '/login': (context) => const LoginScreen(),
            '/dashboard': (context) => const DashboardScreen(),
            '/settings': (context) => const SettingsScreen(),
            '/profile': (context) => const ProfileScreen(),

          },
        );
      },
    );
  }
}

// ودجت للتحقق من حالة المصادقة
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isLoading = true;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      // استخدام Provider العادي
      final authService = provider.Provider.of<AuthService>(
        context,
        listen: false,
      );

      // التحقق من وجود بيانات اعتماد محفوظة
      final hasCredentials = await authService.hasSavedCredentials();

      if (hasCredentials) {
        // محاولة تسجيل الدخول التلقائي
        try {
          final user = await authService.autoSignIn();
          if (user != null && mounted) {
            setState(() {
              _isAuthenticated = true;
              _isLoading = false;
            });
            return;
          }
        } catch (e) {
          debugPrint('Auto sign-in failed: $e');
        }
      }

      if (mounted) {
        setState(() {
          _isAuthenticated = false;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
      if (mounted) {
        setState(() {
          _isAuthenticated = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SplashScreen();
    }

    if (_isAuthenticated) {
      return const DashboardScreen();
    }

    return const LoginScreen();
  }
}

ThemeData _buildLightTheme() {
  return ThemeFix.createLightTheme().copyWith(
    primaryColor: AppColors.primary,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      headlineSmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        color: AppColors.textPrimary,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        color: AppColors.textSecondary,
      ),
      bodySmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        color: AppColors.textHint,
      ),
    ),
  );
}

ThemeData _buildDarkTheme() {
  return ThemeFix.createDarkTheme().copyWith(
    primaryColor: AppColors.primary,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
      headlineSmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        color: AppColors.textPrimary,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        color: AppColors.textSecondary,
      ),
      bodySmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        color: AppColors.textSecondary,
      ),
    ),
  );
}

MaterialColor _createMaterialColor(Color color) {
  List<double> strengths = <double>[.05];
  Map<int, Color> swatch = {};
  final int r = (color.r * 255.0).round() & 0xff,
      g = (color.g * 255.0).round() & 0xff,
      b = (color.b * 255.0).round() & 0xff;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.toARGB32(), swatch);
}
