import 'package:flutter/material.dart';
import '../widgets/back_button.dart';

/// مساعد لإصلاح مشكلة Material widget
/// يستخدم لتغليف المحتوى بـ Scaffold عند الحاجة
class MaterialFix {
  /// تغليف المحتوى بـ Scaffold إذا لم يكن موجوداً
  static Widget wrapWithScaffoldIfNeeded({
    required Widget child,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Color? backgroundColor,
  }) {
    return Scaffold(
      appBar: appBar,
      body: child,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      backgroundColor: backgroundColor,
    );
  }

  /// إنشاء AppBar مخصص مع Material widget
  static PreferredSizeWidget createCustomAppBar({
    required String title,
    required VoidCallback onMenuPressed,
    VoidCallback? onBackPressed,
    List<Widget>? actions,
    Color backgroundColor = Colors.blue,
    Color textColor = Colors.white,
  }) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        color: backgroundColor,
        padding: const EdgeInsets.only(
          top: 8,
          bottom: 8,
          left: 16,
          right: 16,
        ),
        child: Row(
          children: [
            Builder(
              builder: (context) => Container(
                decoration: BoxDecoration(
                  color: textColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: Icon(Icons.menu, color: textColor),
                  onPressed: onMenuPressed,
                  tooltip: 'القائمة الرئيسية',
                  iconSize: 24,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Cairo',
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const Spacer(),
            if (actions != null) ...actions,
            if (onBackPressed != null)
              CustomBackButton(
                onPressed: onBackPressed,
                color: textColor,
              ),
          ],
        ),
      ),
    );
  }
}
