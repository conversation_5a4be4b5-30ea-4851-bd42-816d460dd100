import 'package:flutter/services.dart';

class ScreenSecurityService {
  static const MethodChannel _channel = MethodChannel(
    'com.example.atlas2/screen_security',
  );

  static Future<void> enableSecure() async {
    try {
      await _channel.invokeMethod('enableSecure');
    } catch (e) {
      // تجاهل الأخطاء في حالة عدم توفر الميزة
      print('Screen security not available: $e');
    }
  }

  static Future<void> disableSecure() async {
    try {
      await _channel.invokeMethod('disableSecure');
    } catch (e) {
      // تجاهل الأخطاء في حالة عدم توفر الميزة
      print('Screen security not available: $e');
    }
  }
}
