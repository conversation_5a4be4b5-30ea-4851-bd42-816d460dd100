import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';

class AddProductDialog extends StatefulWidget {
  const AddProductDialog({super.key});

  @override
  State<AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<AddProductDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minQuantityController = TextEditingController();
  final _piecesPerCartonController = TextEditingController();
  final _distributorPriceController = TextEditingController();
  final _officePriceController = TextEditingController();

  String _selectedCategory = 'general_supplies';
  String _selectedUnit = 'piece';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _piecesPerCartonController.text = '1';
    _minQuantityController.text = '0';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _barcodeController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _quantityController.dispose();
    _minQuantityController.dispose();
    _piecesPerCartonController.dispose();
    _distributorPriceController.dispose();
    _officePriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          children: [
            // رأس النافذة
            _buildDialogHeader(),
            
            // محتوى النافذة
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // المعلومات الأساسية
                      _buildSectionTitle('المعلومات الأساسية'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _nameController,
                              label: 'اسم المنتج *',
                              hint: 'أدخل اسم المنتج',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'اسم المنتج مطلوب';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: _codeController,
                              label: 'كود المنتج *',
                              hint: 'أدخل كود المنتج',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'كود المنتج مطلوب';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _barcodeController,
                              label: 'الباركود',
                              hint: 'أدخل الباركود (اختياري)',
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildDropdownField(
                              label: 'الفئة *',
                              value: _selectedCategory,
                              items: _getCategoryItems(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategory = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      _buildTextField(
                        controller: _descriptionController,
                        label: 'وصف المنتج',
                        hint: 'أدخل وصف المنتج (اختياري)',
                        maxLines: 3,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // الأسعار
                      _buildSectionTitle('الأسعار'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _priceController,
                              label: 'سعر البيع *',
                              hint: '0.00',
                              keyboardType: TextInputType.number,
                              prefix: AppStrings.currencySymbol,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'سعر البيع مطلوب';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'أدخل سعر صحيح';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: _costController,
                              label: 'سعر التكلفة *',
                              hint: '0.00',
                              keyboardType: TextInputType.number,
                              prefix: AppStrings.currencySymbol,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'سعر التكلفة مطلوب';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'أدخل سعر صحيح';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _distributorPriceController,
                              label: 'سعر الموزع',
                              hint: '0.00',
                              keyboardType: TextInputType.number,
                              prefix: AppStrings.currencySymbol,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: _officePriceController,
                              label: 'سعر المكتب الطبي',
                              hint: '0.00',
                              keyboardType: TextInputType.number,
                              prefix: AppStrings.currencySymbol,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // المخزون
                      _buildSectionTitle('المخزون'),
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _quantityController,
                              label: 'الكمية الأولية *',
                              hint: '0',
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الكمية مطلوبة';
                                }
                                if (int.tryParse(value) == null) {
                                  return 'أدخل كمية صحيحة';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: _minQuantityController,
                              label: 'الحد الأدنى',
                              hint: '0',
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            child: _buildDropdownField(
                              label: 'الوحدة *',
                              value: _selectedUnit,
                              items: _getUnitItems(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedUnit = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: _piecesPerCartonController,
                              label: 'قطع في الكرتونة',
                              hint: '1',
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.add_shopping_cart,
              color: AppColors.textOnPrimary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.addProduct,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                Text(
                  'أدخل بيانات المنتج الجديد',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    String? prefix,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppColors.textHint,
              fontSize: 14,
            ),
            prefixText: prefix,
            prefixStyle: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.error, width: 2),
            ),
            filled: true,
            fillColor: AppColors.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(12),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            items: items,
            onChanged: onChanged,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary,
            ),
            dropdownColor: AppColors.surface,
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: AppColors.border),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                AppStrings.cancel,
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveProduct,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.textOnPrimary,
                        ),
                      ),
                    )
                  : Text(
                      AppStrings.save,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  List<DropdownMenuItem<String>> _getCategoryItems() {
    return [
      const DropdownMenuItem(
        value: 'general_supplies',
        child: Text('مستلزمات عامة'),
      ),
      const DropdownMenuItem(
        value: 'devices',
        child: Text('أجهزة طبية'),
      ),
      const DropdownMenuItem(
        value: 'consumables',
        child: Text('مستهلكات'),
      ),
      const DropdownMenuItem(
        value: 'sterilization',
        child: Text('معقمات'),
      ),
      const DropdownMenuItem(
        value: 'laboratory',
        child: Text('مستلزمات معمل'),
      ),
    ];
  }

  List<DropdownMenuItem<String>> _getUnitItems() {
    return AppStrings.allUnits.map((unit) {
      return DropdownMenuItem(
        value: unit,
        child: Text(_getUnitDisplayName(unit)),
      );
    }).toList();
  }

  String _getUnitDisplayName(String unit) {
    switch (unit) {
      case 'piece':
        return 'قطعة';
      case 'carton':
        return 'كرتونة';
      case 'box':
        return 'علبة';
      case 'pack':
        return 'عبوة';
      case 'bottle':
        return 'زجاجة';
      case 'tube':
        return 'أنبوبة';
      case 'roll':
        return 'لفة';
      case 'meter':
        return 'متر';
      case 'liter':
        return 'لتر';
      case 'gram':
        return 'جرام';
      case 'milliliter':
        return 'ملليلتر';
      case 'cm':
        return 'سم';
      case 'mm':
        return 'مم';
      case 'set':
        return 'مجموعة';
      case 'dozen':
        return 'دستة';
      case 'strip':
        return 'شريط';
      case 'tablet':
        return 'قرص';
      case 'capsule':
        return 'كبسولة';
      case 'syringe':
        return 'حقنة';
      case 'ampoule':
        return 'أمبول';
      default:
        return unit;
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // إنشاء المنتج الجديد
      final product = ProductModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        code: _codeController.text.trim(),
        barcode: _barcodeController.text.trim().isEmpty
            ? null
            : _barcodeController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        price: double.parse(_priceController.text),
        cost: double.parse(_costController.text),
        quantity: int.parse(_quantityController.text),
        minQuantity: int.parse(_minQuantityController.text),
        piecesPerCarton: int.parse(_piecesPerCartonController.text),
        unit: _selectedUnit,
        distributorPrice: _distributorPriceController.text.isEmpty
            ? 0.0
            : double.parse(_distributorPriceController.text),
        officePrice: _officePriceController.text.isEmpty
            ? 0.0
            : double.parse(_officePriceController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: حفظ المنتج في قاعدة البيانات
      await Future.delayed(const Duration(seconds: 1)); // محاكاة حفظ البيانات

      if (mounted) {
        Navigator.of(context).pop(product);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المنتج: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
