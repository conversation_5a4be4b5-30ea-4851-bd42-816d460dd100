import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../widgets/back_button.dart';

class ActivitiesScreen extends StatelessWidget {
  const ActivitiesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 1,
        title: const Text(
          'الأنشطة الأخيرة',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        centerTitle: true,
        leading: CustomBackButton(
          color: Colors.white,
        ),
      ),
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            // تحديث البيانات
            await Future.delayed(const Duration(seconds: 1));
          },
          child: <PERSON>View(
            padding: EdgeInsets.all(16.w),
            children: [
              Text(
                'سجل الأنشطة',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
              SizedBox(height: 16.h),

              // عنصر نشاط تجريبي
              _buildActivityItem(
                icon: Icons.person_add,
                title: 'إضافة عميل جديد',
                subtitle: 'تم إضافة عميل: أحمد محمد',
                time: 'منذ 5 دقائق',
                color: AppColors.success,
              ),

              _buildActivityItem(
                icon: Icons.receipt_long,
                title: 'إنشاء فاتورة',
                subtitle: 'فاتورة جديدة',
                time: 'منذ 15 دقيقة',
                color: AppColors.primary,
              ),

              _buildActivityItem(
                icon: Icons.inventory_2,
                title: 'تحديث المخزون',
                subtitle: 'تم تحديث كمية منتج: قفازات طبية',
                time: 'منذ ساعة',
                color: AppColors.warning,
              ),

              _buildActivityItem(
                icon: Icons.login,
                title: 'تسجيل دخول',
                subtitle: 'تم تسجيل الدخول بنجاح',
                time: 'منذ ساعتين',
                color: AppColors.info,
              ),

              SizedBox(height: 20.h),

              Center(
                child: Text(
                  'ستتوفر المزيد من الأنشطة قريباً',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(icon, color: color, size: 20.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: TextStyle(
              fontSize: 11.sp,
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }
}
