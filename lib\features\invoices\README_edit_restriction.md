# تقييد تعديل الفواتير بعد 15 دقيقة

## نظرة عامة
تم تطبيق تقييد يمنع تعديل الفواتير بعد مرور 15 دقيقة من إنشائها. هذا التقييد يطبق على جميع عمليات التعديل بما في ذلك:
- تحديث بيانات الفاتورة
- تغيير حالة الفاتورة
- تحديث المبلغ المدفوع

## الميزات المضافة

### 1. في نموذج الفاتورة (`InvoiceModel`)
تم إضافة الخصائص التالية:

- `canEditAfterTimeLimit`: تحقق من إمكانية التعديل بعد 15 دقيقة
- `remainingEditTime`: الوقت المتبقي للتعديل
- `editStatusMessage`: رسالة حالة التعديل

### 2. في خدمة الفواتير (`InvoiceService`)
تم تحديث الطرق التالية لتشمل التحقق من الوقت:

- `updateInvoice()`: تحديث بيانات الفاتورة
- `updateInvoiceStatus()`: تحديث حالة الفاتورة
- `updatePaidAmount()`: تحديث المبلغ المدفوع

### 3. ويدجت عرض حالة التعديل (`InvoiceEditStatusWidget`)
ويدجت جديد يعرض:
- العد التنازلي للوقت المتبقي للتعديل
- رسالة عندما ينتهي الوقت
- تحديث تلقائي كل ثانية

## كيفية الاستخدام

### إضافة ويدجت حالة التعديل في الشاشات

```dart
import '../widgets/invoice_edit_status_widget.dart';

// في شاشة تفاصيل الفاتورة
Widget build(BuildContext context) {
  return Scaffold(
    body: Column(
      children: [
        // معلومات الفاتورة
        // ...
        
        // ويدجت حالة التعديل
        InvoiceEditStatusWidget(invoice: invoice),
        
        // باقي المحتوى
        // ...
      ],
    ),
  );
}
```

### التحقق من إمكانية التعديل في الكود

```dart
// التحقق قبل محاولة التعديل
if (invoice.canEditAfterTimeLimit) {
  // يمكن التعديل
  await invoiceService.updateInvoice(invoice);
} else {
  // لا يمكن التعديل
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(invoice.editStatusMessage),
      backgroundColor: Colors.red,
    ),
  );
}
```

### عرض الوقت المتبقي

```dart
final remainingTime = invoice.remainingEditTime;
if (remainingTime != null) {
  final minutes = remainingTime.inMinutes;
  final seconds = remainingTime.inSeconds % 60;
  print('الوقت المتبقي: ${minutes}:${seconds.toString().padLeft(2, '0')}');
}
```

## رسائل الخطأ

### عندما لا يمكن التعديل
- "لا يمكن تعديل الفاتورة بعد مرور 15 دقيقة من إنشائها"
- "لا يمكن تعديل الفاتورة الملغية"

### عندما يمكن التعديل
- "يمكن التعديل خلال: MM:SS" (مع عد تنازلي)

## الاستثناءات

### الفواتير الملغية
لا يمكن تعديل الفواتير الملغية بغض النظر عن الوقت المنقضي.

### الفواتير المدفوعة بالكامل
يمكن تعديل الفواتير المدفوعة بالكامل خلال الـ 15 دقيقة الأولى فقط.

## ملاحظات تقنية

1. **الوقت المحلي**: يتم استخدام الوقت المحلي للجهاز
2. **التحديث التلقائي**: ويدجت `InvoiceEditStatusWidget` يحدث نفسه كل ثانية
3. **الأداء**: التحقق من الوقت يتم محلياً ولا يحتاج اتصال بالخادم
4. **التوافق**: يعمل مع جميع أنواع الفواتير الموجودة

## اختبار الميزة

### اختبار التعديل خلال 15 دقيقة
1. إنشاء فاتورة جديدة
2. محاولة تعديلها خلال 15 دقيقة
3. التأكد من نجاح التعديل

### اختبار التعديل بعد 15 دقيقة
1. إنشاء فاتورة جديدة
2. الانتظار أكثر من 15 دقيقة
3. محاولة تعديلها
4. التأكد من ظهور رسالة الخطأ

### اختبار العد التنازلي
1. إنشاء فاتورة جديدة
2. مراقبة العد التنازلي في الويدجت
3. التأكد من توقف العد عند الصفر
