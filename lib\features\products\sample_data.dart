import '../../../models/product_model.dart';

/// بيانات تجريبية للمنتجات لاختبار صفحة قائمة المنتجات
class SampleProductsData {
  static List<ProductModel> getSampleProducts() {
    return [
      // أجهزة طبية
      ProductModel(
        id: '1',
        name: 'مقيا<PERSON> ضغط الدم الرقمي',
        description: 'مقياس ضغط دم رقمي دقيق مع شاشة LCD كبيرة',
        category: 'devices',
        code: 'BP001',
        price: 299.99,
        cost: 180.00,
        quantity: 25,
        minQuantity: 5,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890123',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '2',
        name: 'مقياس حرارة طبي',
        description: 'مقياس حرارة رقمي سريع ودقيق',
        category: 'devices',
        code: 'TEMP001',
        price: 89.99,
        cost: 45.00,
        quantity: 50,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890124',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now(),
      ),

      // مستهلكات
      ProductModel(
        id: '3',
        name: 'قفازات طبية لاتكس',
        description: 'قفازات طبية لاتكس خالية من البودرة',
        category: 'consumables',
        code: 'GLOVE001',
        price: 15.99,
        cost: 8.00,
        quantity: 200,
        minQuantity: 50,
        piecesPerCarton: 100,
        unit: 'box',
        barcode: '1234567890125',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '4',
        name: 'كمامات طبية',
        description: 'كمامات طبية ثلاثية الطبقات',
        category: 'consumables',
        code: 'MASK001',
        price: 25.99,
        cost: 12.00,
        quantity: 150,
        minQuantity: 30,
        piecesPerCarton: 50,
        unit: 'pack',
        barcode: '1234567890126',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),

      // معقمات
      ProductModel(
        id: '5',
        name: 'معقم اليدين',
        description: 'معقم يدين كحولي 70%',
        category: 'sterilization',
        code: 'SANIT001',
        price: 45.99,
        cost: 22.00,
        quantity: 80,
        minQuantity: 20,
        piecesPerCarton: 12,
        unit: 'bottle',
        barcode: '1234567890127',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '6',
        name: 'محلول تعقيم الأسطح',
        description: 'محلول تعقيم فعال للأسطح والأدوات',
        category: 'sterilization',
        code: 'SURF001',
        price: 35.99,
        cost: 18.00,
        quantity: 60,
        minQuantity: 15,
        piecesPerCarton: 6,
        unit: 'bottle',
        barcode: '1234567890128',
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now(),
      ),

      // مستلزمات معمل
      ProductModel(
        id: '7',
        name: 'أنابيب اختبار',
        description: 'أنابيب اختبار زجاجية 10 مل',
        category: 'laboratory',
        code: 'TUBE001',
        price: 12.99,
        cost: 6.00,
        quantity: 300,
        minQuantity: 100,
        piecesPerCarton: 50,
        unit: 'pack',
        barcode: '1234567890129',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '8',
        name: 'شرائح مجهرية',
        description: 'شرائح مجهرية زجاجية شفافة',
        category: 'laboratory',
        code: 'SLIDE001',
        price: 8.99,
        cost: 4.00,
        quantity: 500,
        minQuantity: 150,
        piecesPerCarton: 100,
        unit: 'pack',
        barcode: '1234567890130',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now(),
      ),

      // مستلزمات عامة
      ProductModel(
        id: '9',
        name: 'ضمادات طبية',
        description: 'ضمادات طبية معقمة بأحجام مختلفة',
        category: 'general_supplies',
        code: 'BAND001',
        price: 18.99,
        cost: 9.00,
        quantity: 120,
        minQuantity: 30,
        piecesPerCarton: 24,
        unit: 'pack',
        barcode: '1234567890131',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '10',
        name: 'شريط طبي',
        description: 'شريط طبي لاصق مقاوم للماء',
        category: 'general_supplies',
        code: 'TAPE001',
        price: 22.99,
        cost: 11.00,
        quantity: 90,
        minQuantity: 25,
        piecesPerCarton: 12,
        unit: 'roll',
        barcode: '1234567890132',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // منتجات ذات مخزون منخفض
      ProductModel(
        id: '11',
        name: 'مقص طبي',
        description: 'مقص طبي حاد لقطع الضمادات',
        category: 'devices',
        code: 'SCISS001',
        price: 55.99,
        cost: 28.00,
        quantity: 3,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890133',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '12',
        name: 'ملقط طبي',
        description: 'ملقط طبي دقيق للعمليات الصغيرة',
        category: 'devices',
        code: 'FORCEPS001',
        price: 75.99,
        cost: 38.00,
        quantity: 2,
        minQuantity: 8,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890134',
        createdAt: DateTime.now().subtract(const Duration(days: 40)),
        updatedAt: DateTime.now(),
      ),

      // منتجات نفذ مخزونها
      ProductModel(
        id: '13',
        name: 'سماعة طبية',
        description: 'سماعة طبية عالية الجودة',
        category: 'devices',
        code: 'STETH001',
        price: 199.99,
        cost: 100.00,
        quantity: 0,
        minQuantity: 5,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890135',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
      ),

      ProductModel(
        id: '14',
        name: 'مصباح طبي LED',
        description: 'مصباح طبي LED ساطع للفحص',
        category: 'devices',
        code: 'LIGHT001',
        price: 89.99,
        cost: 45.00,
        quantity: 0,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '1234567890136',
        createdAt: DateTime.now().subtract(const Duration(days: 55)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// الحصول على منتجات حسب الفئة
  static List<ProductModel> getProductsByCategory(String category) {
    if (category == 'all') {
      return getSampleProducts();
    }
    return getSampleProducts()
        .where((product) => product.category == category)
        .toList();
  }

  /// الحصول على منتجات المخزون المنخفض
  static List<ProductModel> getLowStockProducts() {
    return getSampleProducts().where((product) => product.isLowStock).toList();
  }

  /// الحصول على منتجات نفذ مخزونها
  static List<ProductModel> getOutOfStockProducts() {
    return getSampleProducts()
        .where((product) => product.quantity == 0)
        .toList();
  }

  /// البحث في المنتجات
  static List<ProductModel> searchProducts(String query) {
    if (query.isEmpty) return getSampleProducts();

    query = query.toLowerCase();
    return getSampleProducts().where((product) {
      return product.name.toLowerCase().contains(query) ||
          product.code.toLowerCase().contains(query) ||
          (product.barcode?.toLowerCase().contains(query) ?? false) ||
          product.description.toLowerCase().contains(query);
    }).toList();
  }
}
