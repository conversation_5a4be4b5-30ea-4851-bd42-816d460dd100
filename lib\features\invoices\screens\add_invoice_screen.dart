import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../models/invoice_model.dart';
import '../../../models/customer_model.dart';
import '../../../models/product_model.dart';
import '../../../services/invoice_service.dart';
import '../../../services/customer_service.dart';
import '../../../services/product_service.dart';

import '../../../widgets/delete_protection_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/back_button.dart';
import '../widgets/invoice_item_form.dart';
import '../widgets/customer_selector.dart';
import '../../products/widgets/product_selector.dart';

class AddInvoiceScreen extends StatefulWidget {
  final CustomerModel? initialCustomer;

  const AddInvoiceScreen({Key? key, this.initialCustomer}) : super(key: key);

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();
  final ProductService _productService = ProductService();

  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _discountAmountController = TextEditingController();
  final _discountPercentageController = TextEditingController();
  final _paidAmountController = TextEditingController();

  CustomerModel? _selectedCustomer;
  DateTime _invoiceDate = DateTime.now();
  List<InvoiceItem> _invoiceItems = [];
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isPercentageDiscount = false;

  @override
  void dispose() {
    _notesController.dispose();
    _discountAmountController.dispose();
    _discountPercentageController.dispose();
    _paidAmountController.dispose();
    super.dispose();
  }

  Future<void> _selectCustomer() async {
    // إذا كان العميل محدد مسبقاً، لا يمكن تغييره
    if (widget.initialCustomer != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'لا يمكن تغيير العميل - الفاتورة مرتبطة بالعميل المحدد',
          ),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final customer = await showDialog<CustomerModel>(
      context: context,
      builder: (context) => const CustomerSelector(),
    );

    if (customer != null) {
      setState(() {
        _selectedCustomer = customer;
      });
    }
  }

  Future<void> _addProduct() async {
    try {
      final product = await showDialog<ProductModel>(
        context: context,
        builder: (context) => const ProductSelector(),
      );

      if (product != null) {
        // التحقق من أن المنتج صالح
        if (product.id.isEmpty || product.name.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('خطأ: بيانات المنتج غير صحيحة'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        // التحقق من أن المنتج لم يتم إضافته مسبقاً
        final existingItem = _invoiceItems
            .where((item) => item.productId == product.id)
            .firstOrNull;
        if (existingItem != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'المنتج "${product.name}" موجود بالفعل في الفاتورة',
              ),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        // التحقق من المخزون المتوفر
        if (product.quantity <= 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('المنتج "${product.name}" نفد من المخزون'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        final item = _invoiceService.createInvoiceItem(
          product: product,
          quantity: 1,
          customer: _selectedCustomer!, // تمرير العميل
          unit: product.unit, // تمرير وحدة المنتج
        );

        setState(() {
          _invoiceItems.add(item);
        });

        // رسالة نجاح
        String unitText = item.unit == AppStrings.carton ? 'كرتونة' : 'قطعة';
        String priceText = item.unitPrice.toStringAsFixed(2);
        String totalText = item.total.toStringAsFixed(2);

        String message;
        if (item.unit == AppStrings.carton && product.piecesPerCarton > 1) {
          message =
              'تم إضافة "${product.name}" للفاتورة\n$priceText ر.س للكرتونة (${product.piecesPerCarton} قطعة) - الإجمالي: $totalText ر.س';
        } else {
          message =
              'تم إضافة "${product.name}" للفاتورة\n$priceText ر.س لل$unitText - الإجمالي: $totalText ر.س';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(message, style: const TextStyle(fontSize: 14)),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إضافة المنتج: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة المنتج: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _updateInvoiceItem(int index, InvoiceItem updatedItem) {
    setState(() {
      _invoiceItems[index] = updatedItem;
    });
  }

  void _removeInvoiceItem(int index) {
    setState(() {
      _invoiceItems.removeAt(index);
    });
  }

  Future<void> _selectDate() async {
    // لا يمكن تغيير تاريخ الفاتورة - يجب أن يكون اليوم الحالي فقط
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تاريخ الفاتورة هو اليوم الحالي ولا يمكن تغييره'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_invoiceItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('يرجى إضافة منتج واحد على الأقل للفاتورة'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'إضافة منتج',
            textColor: Colors.white,
            onPressed: () {
              // يمكن إضافة منطق للانتقال إلى قسم المنتجات
            },
          ),
        ),
      );
      return;
    }

    // تاريخ الفاتورة هو اليوم الحالي دائماً، لا حاجة للتحقق

    // التحقق من الكميات المتوفرة في المخزون
    final List<String> insufficientStockItems = [];

    for (final item in _invoiceItems) {
      try {
        final product = await _productService.getProductById(item.productId);
        if (product != null && item.quantity > product.quantity) {
          insufficientStockItems.add(
            '${item.productName}: المطلوب ${item.quantity}، المتوفر ${product.quantity}',
          );
        }
      } catch (e) {
        debugPrint('خطأ في التحقق من مخزون المنتج ${item.productName}: $e');
      }
    }

    if (insufficientStockItems.isNotEmpty) {
      final errorMessage =
          'الكميات المطلوبة تتجاوز المخزون المتوفر:\n${insufficientStockItems.join('\n')}';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final invoice = await _invoiceService.createInvoice(
        customer: _selectedCustomer!,
        items: _invoiceItems,
        invoiceDate: _invoiceDate,
        discountAmount: _isPercentageDiscount ? 0.0 : _discountValue,
        discountPercentage: _isPercentageDiscount
            ? double.tryParse(_discountPercentageController.text) ?? 0.0
            : 0.0,
        paidAmount: _paidAmount, // إضافة المبلغ المدفوع
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'تم إنشاء الفاتورة رقم ${invoice.invoiceNumber} بنجاح',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في حفظ الفاتورة: $e';
        _isLoading = false;
      });

      // عرض رسالة خطأ أكثر وضوحاً للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في حفظ الفاتورة. يرجى المحاولة مرة أخرى أو إعادة تشغيل التطبيق.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () {
                setState(() {
                  _errorMessage = '';
                });
                _saveInvoice();
              },
            ),
          ),
        );
      }
    }
  }

  double get _subtotal {
    return _invoiceItems.fold(0.0, (sum, item) => sum + item.total);
  }

  double get _discountValue {
    if (_isPercentageDiscount) {
      final percentage =
          double.tryParse(_discountPercentageController.text) ?? 0.0;
      return _subtotal * (percentage / 100);
    } else {
      return double.tryParse(_discountAmountController.text) ?? 0.0;
    }
  }

  double get _totalAmount {
    return _subtotal - _discountValue;
  }

  double get _paidAmount {
    return double.tryParse(_paidAmountController.text) ?? 0.0;
  }

  double get _remainingAmount {
    return _totalAmount - _paidAmount;
  }

  String _formatTime12Hour(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'م' : 'ص'; // م = PM, ص = AM
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  @override
  void initState() {
    super.initState();
    if (widget.initialCustomer != null) {
      _selectedCustomer = widget.initialCustomer;
    } else {
      // إذا لم يتم تمرير عميل، العودة للشاشة السابقة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب اختيار عميل لإنشاء فاتورة جديدة'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
          Navigator.of(context).pop();
        }
      });
      return;
    }

    // فحص وإصلاح قاعدة البيانات عند بدء الشاشة
    _checkAndFixDatabase();
  }

  /// فحص وإصلاح قاعدة البيانات
  Future<void> _checkAndFixDatabase() async {
    try {
      await _invoiceService.checkAndFixDatabase();
    } catch (e) {
      debugPrint('خطأ في فحص قاعدة البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'إضافة فاتورة جديدة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // ملاحظة مهمة إذا كان العميل محدد مسبقاً
                    if (widget.initialCustomer != null) ...[
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.green.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'إنشاء فاتورة جديدة للعميل: ${widget.initialCustomer!.name}',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // معلومات العميل
                    _buildSection(
                      title: 'معلومات العميل',
                      icon: Icons.person,
                      child: _buildCustomerSection(),
                    ),

                    const SizedBox(height: 24),

                    // تاريخ الفاتورة
                    _buildSection(
                      title: 'تاريخ الفاتورة',
                      icon: Icons.calendar_today,
                      child: _buildDateSection(),
                    ),

                    const SizedBox(height: 24),

                    // المنتجات
                    _buildSection(
                      title: 'المنتجات',
                      icon: Icons.shopping_cart,
                      child: _buildProductsSection(),
                    ),

                    if (_invoiceItems.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.green.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.green),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'تم إضافة ${_invoiceItems.length} منتج${_invoiceItems.length > 1 ? 'ات' : ''} للفاتورة',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 24),

                    // الخصم
                    _buildSection(
                      title: 'الخصم',
                      icon: Icons.discount,
                      child: _buildDiscountSection(),
                    ),

                    const SizedBox(height: 24),

                    // المبلغ المدفوع
                    _buildSection(
                      title: 'المبلغ المدفوع',
                      icon: Icons.payment,
                      child: _buildPaidAmountSection(),
                    ),

                    const SizedBox(height: 24),

                    // الملاحظات
                    _buildSection(
                      title: 'ملاحظات',
                      icon: Icons.note,
                      child: CustomTextField(
                        controller: _notesController,
                        labelText: 'ملاحظات',
                        hintText: 'أضف ملاحظات للفاتورة (اختياري)',
                        prefixIcon: Icons.note,
                        maxLines: 3,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // إجمالي الفاتورة
                    _buildTotalSection(),

                    if (_errorMessage.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red.withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.error, color: Colors.red),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _errorMessage,
                                    style: const TextStyle(color: Colors.red),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _isLoading
                                        ? null
                                        : () async {
                                            setState(() {
                                              _errorMessage = '';
                                              _isLoading = true;
                                            });

                                            try {
                                              await _invoiceService
                                                  .checkAndFixDatabase();
                                              setState(() {
                                                _isLoading = false;
                                              });

                                              if (mounted) {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'تم إصلاح قاعدة البيانات بنجاح',
                                                    ),
                                                    backgroundColor:
                                                        Colors.green,
                                                  ),
                                                );
                                              }
                                            } catch (e) {
                                              setState(() {
                                                _errorMessage =
                                                    'فشل في إصلاح قاعدة البيانات: $e';
                                                _isLoading = false;
                                              });
                                            }
                                          },
                                    icon: const Icon(
                                      Icons.build,
                                      color: Colors.white,
                                    ),
                                    label: const Text(
                                      'إصلاح قاعدة البيانات',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _isLoading
                                        ? null
                                        : () {
                                            setState(() {
                                              _errorMessage = '';
                                            });
                                          },
                                    icon: const Icon(
                                      Icons.clear,
                                      color: Colors.white,
                                    ),
                                    label: const Text(
                                      'إخفاء الخطأ',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // أزرار الحفظ والإلغاء
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'إلغاء',
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.of(context).pop(),
                      backgroundColor: Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: _isLoading ? 'جاري الحفظ...' : 'حفظ الفاتورة',
                      onPressed: _isLoading ? null : _saveInvoice,
                      backgroundColor: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Column(
      children: [
        if (_selectedCustomer == null)
          CustomButton(
            text: 'اختيار العميل',
            onPressed: _selectCustomer,
            backgroundColor: AppColors.primary,
            icon: Icons.person_add,
          )
        else
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedCustomer!.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (_selectedCustomer!.phone1 != null)
                            Text(
                              _selectedCustomer!.phone1!,
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          if (widget.initialCustomer != null) ...[
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: Colors.orange.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.lock,
                                    color: Colors.orange,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'لا يمكن تغيير العميل',
                                    style: TextStyle(
                                      color: Colors.orange[700],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (widget.initialCustomer == null)
                      IconButton(
                        onPressed: _selectCustomer,
                        icon: const Icon(Icons.edit, color: AppColors.primary),
                      )
                    else
                      const Icon(Icons.lock, color: Colors.orange, size: 20),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildDateSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.calendar_today, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '${_invoiceDate.day}/${_invoiceDate.month}/${_invoiceDate.year}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTime12Hour(_invoiceDate),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'تاريخ إنشاء الفاتورة (اليوم الحالي)',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.lock, color: Colors.grey, size: 20),
        ],
      ),
    );
  }

  Widget _buildProductsSection() {
    return Column(
      children: [
        CustomButton(
          text: 'إضافة منتج',
          onPressed: _addProduct,
          backgroundColor: AppColors.primary,
          icon: Icons.add_shopping_cart,
        ),
        if (_invoiceItems.isNotEmpty) ...[
          const SizedBox(height: 16),
          ...List.generate(_invoiceItems.length, (index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: InvoiceItemForm(
                item: _invoiceItems[index],
                onUpdate: (updatedItem) =>
                    _updateInvoiceItem(index, updatedItem),
                onRemove: () => _removeInvoiceItem(index),
                customerType: _selectedCustomer!.type, // تمرير نوع العميل
              ),
            );
          }),
        ],
      ],
    );
  }

  Widget _buildDiscountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // نوع الخصم
        Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Radio<bool>(
                    value: false,
                    groupValue: _isPercentageDiscount,
                    onChanged: (value) {
                      setState(() {
                        _isPercentageDiscount = value!;
                        _discountPercentageController.clear();
                      });
                    },
                  ),
                  const Text('مبلغ ثابت'),
                  const SizedBox(width: 20),
                  Radio<bool>(
                    value: true,
                    groupValue: _isPercentageDiscount,
                    onChanged: (value) {
                      setState(() {
                        _isPercentageDiscount = value!;
                        _discountAmountController.clear();
                      });
                    },
                  ),
                  const Text('نسبة مئوية'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // حقول الخصم
        if (!_isPercentageDiscount) ...[
          CustomTextField(
            controller: _discountAmountController,
            labelText: 'قيمة الخصم',
            hintText: 'أدخل قيمة الخصم بالجنيه',
            prefixIcon: Icons.money_off,
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
        ] else ...[
          CustomTextField(
            controller: _discountPercentageController,
            labelText: 'نسبة الخصم',
            hintText: 'أدخل نسبة الخصم (مثال: 10)',
            prefixIcon: Icons.percent,
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
        ],

        const SizedBox(height: 16),

        // عرض تفاصيل الخصم
        if (_discountValue > 0) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.discount, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'قيمة الخصم: ${_discountValue.toStringAsFixed(2)} ر.س',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      if (_isPercentageDiscount)
                        Text(
                          'الإجمالي قبل الخصم: ${_subtotal.toStringAsFixed(2)} ر.س',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaidAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextField(
          controller: _paidAmountController,
          labelText: 'المبلغ المدفوع',
          hintText: 'أدخل المبلغ المدفوع بالجنيه',
          prefixIcon: Icons.payment,
          keyboardType: TextInputType.number,
          onChanged: (value) => setState(() {}),
        ),
        const SizedBox(height: 16),

        // عرض تفاصيل المبلغ المدفوع
        if (_paidAmount > 0) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.payment, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'المبلغ المدفوع: ${_paidAmount.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المبلغ المتبقي: ${_remainingAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: _remainingAmount > 0
                            ? Colors.orange
                            : Colors.green,
                      ),
                    ),
                  ],
                ),

                if (_remainingAmount < 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        'المبلغ المدفوع يتجاوز إجمالي الفاتورة!',
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTotalSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.attach_money,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'ملخص الفاتورة:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('المجموع الفرعي:'),
              const Spacer(),
              Text('${_subtotal.toStringAsFixed(2)} ر.س'),
            ],
          ),
          if (_discountValue > 0) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Text('الخصم:', style: TextStyle(color: Colors.red[600])),
                const Spacer(),
                Text(
                  '-${_discountValue.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    color: Colors.red[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
          const Divider(),
          Row(
            children: [
              const Text(
                'الإجمالي النهائي:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${_totalAmount.toStringAsFixed(2)} ر.س',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          // إضافة قسم معلومات الدفع المحسنة
          if (_paidAmount > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.payment, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'معلومات الدفع',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Text(
                        'المبلغ المدفوع:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_paidAmount.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        'المبلغ المتبقي:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_remainingAmount.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _remainingAmount > 0
                              ? Colors.orange[700]
                              : Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        'نسبة الدفع:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_calculatePaymentPercentage().toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _calculatePaymentPercentage() >= 100
                              ? Colors.green[700]
                              : Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // دالة مساعدة لحساب نسبة الدفع
  double _calculatePaymentPercentage() {
    if (_totalAmount > 0) {
      return (_paidAmount / _totalAmount) * 100;
    }
    return 0.0;
  }
}
