import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../lib/services/product_service.dart';
import '../lib/services/database_service.dart';
import '../lib/models/product_model.dart';

void main() {
  group('Products Debug Test', () {
    late ProductService productService;
    late DatabaseService databaseService;

    setUpAll(() async {
      // Initialize database factory for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;

      productService = ProductService();
      databaseService = DatabaseService();
    });

    test('Test database connection and products table', () async {
      try {
        // Test database connection
        final db = await databaseService.database;
        expect(db, isNotNull);
        print('✅ Database connection successful');

        // Check if products table exists
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'",
        );
        expect(tables.isNotEmpty, true);
        print('✅ Products table exists');

        // Check products table structure
        final columns = await db.rawQuery("PRAGMA table_info(products)");
        print('📋 Products table columns:');
        for (var column in columns) {
          print('  - ${column['name']}: ${column['type']}');
        }

        // Check if isActive column exists
        final hasIsActiveColumn = columns.any((col) => col['name'] == 'isActive');
        print('🔍 isActive column exists: $hasIsActiveColumn');

        // Check if there are any products
        final products = await productService.getAllProducts();
        print('📦 Number of products in database: ${products.length}');

        if (products.isNotEmpty) {
          print('📋 Products found:');
          for (var product in products.take(3)) {
            print(
              '  - ${product.name} (ID: ${product.id}, Active: ${product.isActive}, Category: ${product.category})',
            );
          }
        } else {
          print('⚠️ No products found in database');
        }
      } catch (e) {
        print('❌ Error: $e');
        fail('Database test failed: $e');
      }
    });

    test('Test raw database query for products', () async {
      try {
        final db = await databaseService.database;
        
        // Test raw query without isActive filter
        final allProductsRaw = await db.rawQuery('SELECT * FROM products ORDER BY name ASC');
        print('📦 Raw query found ${allProductsRaw.length} products');
        
        if (allProductsRaw.isNotEmpty) {
          print('📋 Raw products data:');
          for (var product in allProductsRaw.take(3)) {
            print('  - ${product['name']} (ID: ${product['id']}, isActive: ${product['isActive']})');
          }
        }

        // Test query with isActive filter
        final activeProductsRaw = await db.rawQuery('SELECT * FROM products WHERE isActive = 1 ORDER BY name ASC');
        print('📦 Active products query found ${activeProductsRaw.length} products');
        
        // Test query without isActive filter (fallback)
        final fallbackProductsRaw = await db.rawQuery('SELECT * FROM products ORDER BY name ASC');
        print('📦 Fallback query found ${fallbackProductsRaw.length} products');
        
      } catch (e) {
        print('❌ Error in raw query test: $e');
        fail('Raw query test failed: $e');
      }
    });

    test('Test creating a test product', () async {
      try {
        final result = await productService.createTestProduct();
        expect(result, true);
        print('✅ Test product created successfully');

        // Verify the product was added
        final products = await productService.getAllProducts();
        final testProduct = products
            .where((p) => p.name == 'منتج تجريبي')
            .firstOrNull;
        expect(testProduct, isNotNull);
        print('✅ Test product found in database: ${testProduct!.name}');
        print('  - ID: ${testProduct.id}');
        print('  - Active: ${testProduct.isActive}');
        print('  - Category: ${testProduct.category}');
        print('  - Price: ${testProduct.price}');
      } catch (e) {
        print('❌ Error creating test product: $e');
        fail('Test product creation failed: $e');
      }
    });

    test('Test adding demo products', () async {
      try {
        final result = await productService.addDemoProducts();
        expect(result, true);
        print('✅ Demo products added successfully');

        // Verify demo products were added
        final products = await productService.getAllProducts();
        final demoProducts = products
            .where(
              (p) =>
                  p.name.contains('باراسيتامول') ||
                  p.name.contains('قفازات') ||
                  p.name.contains('مقياس حرارة'),
            )
            .toList();

        expect(demoProducts.length, greaterThan(0));
        print('✅ Found ${demoProducts.length} demo products');
        
        for (var product in demoProducts) {
          print('  - ${product.name} (Active: ${product.isActive}, Category: ${product.category})');
        }
      } catch (e) {
        print('❌ Error adding demo products: $e');
        fail('Demo products addition failed: $e');
      }
    });

    test('Test product filtering and search', () async {
      try {
        // Add some test products first
        await productService.createTestProduct();

        // Test search
        final searchResults = await productService.searchProducts('تجريبي');
        expect(searchResults.isNotEmpty, true);
        print(
          '✅ Product search working: found ${searchResults.length} results',
        );

        // Test category filtering
        final categoryProducts = await productService.getProductsByCategory(
          'general_supplies',
        );
        print(
          '✅ Category filtering working: found ${categoryProducts.length} products in general_supplies',
        );
      } catch (e) {
        print('❌ Error in filtering/search test: $e');
        fail('Filtering/search test failed: $e');
      }
    });

    test('Test product model creation and validation', () async {
      try {
        final testProduct = ProductModel(
          id: 'test_${DateTime.now().millisecondsSinceEpoch}',
          name: 'منتج اختبار النموذج',
          description: 'وصف تجريبي',
          category: 'general_supplies',
          code: 'TEST_MODEL_001',
          price: 100.0,
          cost: 80.0,
          quantity: 50,
          minQuantity: 10,
          unit: 'قطعة',
          piecesPerCarton: 1,
          barcode: '123456789',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          distributorPrice: 90.0,
          officePrice: 95.0,
        );

        print('✅ Product model created successfully');
        print('  - Name: ${testProduct.name}');
        print('  - ID: ${testProduct.id}');
        print('  - Active: ${testProduct.isActive}');
        print('  - Category: ${testProduct.category}');
        print('  - Price: ${testProduct.price}');
        print('  - Distributor Price: ${testProduct.distributorPrice}');
        print('  - Office Price: ${testProduct.officePrice}');

        // Test model validation
        expect(testProduct.name.isNotEmpty, true);
        expect(testProduct.price > 0, true);
        expect(testProduct.isActive, true);
        expect(testProduct.category.isNotEmpty, true);

        print('✅ Product model validation passed');
      } catch (e) {
        print('❌ Error in product model test: $e');
        fail('Product model test failed: $e');
      }
    });

    test('Test database service getAllProducts method', () async {
      try {
        final products = await databaseService.getAllProducts();
        print('📦 DatabaseService.getAllProducts() returned ${products.length} products');
        
        if (products.isNotEmpty) {
          print('📋 Sample products from DatabaseService:');
          for (var product in products.take(3)) {
            print('  - ${product.name} (ID: ${product.id}, Active: ${product.isActive})');
          }
        }
        
        expect(products, isA<List<ProductModel>>());
        print('✅ DatabaseService.getAllProducts() working correctly');
      } catch (e) {
        print('❌ Error in DatabaseService.getAllProducts() test: $e');
        fail('DatabaseService test failed: $e');
      }
    });

    test('Test product service getAllProducts method', () async {
      try {
        final products = await productService.getAllProducts();
        print('📦 ProductService.getAllProducts() returned ${products.length} products');
        
        if (products.isNotEmpty) {
          print('📋 Sample products from ProductService:');
          for (var product in products.take(3)) {
            print('  - ${product.name} (ID: ${product.id}, Active: ${product.isActive})');
          }
        }
        
        expect(products, isA<List<ProductModel>>());
        print('✅ ProductService.getAllProducts() working correctly');
      } catch (e) {
        print('❌ Error in ProductService.getAllProducts() test: $e');
        fail('ProductService test failed: $e');
      }
    });
  });
}
