import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_service.dart';
import '../widgets/invoice_card.dart';
import '../screens/invoice_details_screen.dart';
import '../screens/governorate_invoices_screen.dart';

class InvoicesByGovernorateList extends StatefulWidget {
  final String searchQuery;
  final InvoiceStatus? selectedStatus;

  const InvoicesByGovernorateList({
    super.key, 
    required this.searchQuery,
    this.selectedStatus,
  });

  @override
  State<InvoicesByGovernorateList> createState() =>
      _InvoicesByGovernorateListState();
}

class _InvoicesByGovernorateListState
    extends State<InvoicesByGovernorateList> {
  final InvoiceService _invoiceService = InvoiceService();

  // إحصائيات إضافية
  int _totalInvoices = 0;
  int _totalGovernorates = 0;

  /// جلب الفاتورة المحدثة من قاعدة البيانات
  Future<InvoiceModel?> _getUpdatedInvoice(String invoiceId) async {
    try {
      return await _invoiceService.getInvoiceById(invoiceId);
    } catch (e) {
      debugPrint('خطأ في جلب الفاتورة المحدثة: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, List<InvoiceModel>>>(
      stream: _invoiceService.getInvoicesByGovernorate(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
                SizedBox(height: 16.h),
                Text(
                  'حدث خطأ في تحميل البيانات',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.error,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                ElevatedButton(
                  onPressed: () {
                    setState(() {});
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                SizedBox(height: 16.h),
                Text(
                  'جاري تحميل الفواتير...',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          );
        }

        final invoicesByGovernorate = snapshot.data ?? {};

        // تحديث الإحصائيات
        _totalInvoices = invoicesByGovernorate.values.fold(
          0,
          (sum, invoices) => sum + invoices.length,
        );
        _totalGovernorates = invoicesByGovernorate.length;

        if (invoicesByGovernorate.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 24.h),
                Text(
                  'لا توجد فواتير حتى الآن',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'ابدأ بإضافة فاتورة جديدة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // تطبيق البحث والفلترة
        final filteredInvoicesByGovernorate = <String, List<InvoiceModel>>{};

        for (final entry in invoicesByGovernorate.entries) {
          final governorate = entry.key;
          final invoices = entry.value;

          List<InvoiceModel> filteredInvoices = invoices;

          // فلترة حسب البحث
          if (widget.searchQuery.isNotEmpty) {
            filteredInvoices = invoices.where((invoice) {
              return invoice.invoiceNumber.toLowerCase().contains(
                    widget.searchQuery.toLowerCase(),
                  ) ||
                  invoice.customerName.toLowerCase().contains(
                    widget.searchQuery.toLowerCase(),
                  ) ||
                  invoice.customerPhone.contains(widget.searchQuery);
            }).toList();
          }

          // فلترة حسب الحالة
          if (widget.selectedStatus != null) {
            filteredInvoices = filteredInvoices.where((invoice) {
              return invoice.status == widget.selectedStatus;
            }).toList();
          }

          if (filteredInvoices.isNotEmpty) {
            filteredInvoicesByGovernorate[governorate] = filteredInvoices;
          }
        }

        if (filteredInvoicesByGovernorate.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 24.h),
                Text(
                  'لا توجد نتائج للبحث',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'جرب تغيير كلمات البحث',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // قائمة المحافظات
            SizedBox(height: 16.h),
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: filteredInvoicesByGovernorate.length,
                itemBuilder: (context, index) {
                  final governorate = filteredInvoicesByGovernorate.keys
                      .elementAt(index);
                  final invoices =
                      filteredInvoicesByGovernorate[governorate]!;

                  return Card(
                    margin: EdgeInsets.only(bottom: 16.h),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: InkWell(
                      onTap: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => GovernorateInvoicesScreen(
                              governorateName: governorate,
                              invoices: invoices,
                            ),
                          ),
                        );

                        // التعامل مع النتيجة
                        if (result != null && result is Map) {
                          if (result['deleted'] == true) {
                            // إزالة الفاتورة من القائمة فوراً
                            setState(() {
                              invoices.removeWhere(
                                (i) => i.id == result['invoiceId'],
                              );
                            });

                            // عرض رسالة نجاح
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'تم حذف الفاتورة بنجاح',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                  backgroundColor: AppColors.success,
                                ),
                              );
                            }
                          } else if (result['updated'] == true) {
                            // تحديث الفاتورة في القائمة
                            final invoiceId = result['invoiceId'];
                            final updatedInvoice = await _getUpdatedInvoice(invoiceId);
                            
                            if (updatedInvoice != null) {
                              setState(() {
                                final index = invoices.indexWhere((i) => i.id == invoiceId);
                                if (index != -1) {
                                  invoices[index] = updatedInvoice;
                                }
                              });
                            }

                            // عرض رسالة نجاح
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'تم تحديث الفاتورة بنجاح',
                                    style: TextStyle(fontFamily: 'Cairo'),
                                  ),
                                  backgroundColor: AppColors.success,
                                ),
                              );
                            }
                          }
                        }
                      },
                      borderRadius: BorderRadius.circular(12.r),
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25.r),
                          border: Border.all(
                            color: AppColors.primary,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.folder,
                              color: AppColors.primary,
                              size: 24.sp,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    governorate,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  Text(
                                    'اضغط لعرض الفواتير',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.textSecondary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                '${invoices.length}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Cairo',
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: AppColors.primary,
                              size: 16.sp,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
