# إضافة الفواتير إلى الإجراءات السريعة

## ملخص التحديث

تم إضافة الفواتير إلى الإجراءات السريعة في لوحة التحكم بجانب العملاء والمنتجات.

## التغييرات المطبقة

### 1. تحديث ملف `quick_actions_grid.dart`

#### الإضافات:
- استيراد `InvoiceService` لخدمة الفواتير
- إضافة متغير `_invoicesCount` لتخزين عدد الفواتير
- إضافة دالة `_loadInvoicesCount()` لتحميل عدد الفواتير
- تحديث دالة `_loadAllData()` لتشمل تحميل عدد الفواتير

#### إضافة بطاقة الفواتير:
- عنوان: "الفواتير"
- أيقونة: `Icons.receipt_long`
- لون: وردي (`Color(0xFFE91E63)`)
- عرض عدد الفواتير الحالي
- التنقل إلى شاشة إدارة الفواتير عند النقر

### 2. الميزات الجديدة

#### عرض عدد الفواتير:
- يتم عرض العدد الإجمالي للفواتير في قاعدة البيانات
- يتم تحديث العدد عند العودة من شاشة إدارة الفواتير
- عرض "..." أثناء التحميل

#### التنقل السريع:
- النقر على بطاقة الفواتير يؤدي إلى فتح شاشة إدارة الفواتير
- العودة تلقائية إلى لوحة التحكم مع تحديث البيانات

#### التصميم:
- بطاقة الفواتير تتبع نفس تصميم باقي البطاقات
- لون مميز (وردي) لتمييزها عن باقي البطاقات
- أيقونة مناسبة تمثل الفواتير

## موقع البطاقة في الشبكة

تم وضع بطاقة الفواتير في الترتيب التالي:
1. المنتجات (أزرق)
2. العملاء (أخضر)
3. **الفواتير (وردي)** ← الجديد
4. تذكير العملاء (أزرق فاتح)
5. قائمة التحصيل (برتقالي)
6. توريد المصنع (بني)
7. المخزون (رمادي داكن)
8. مشاركة الفواتير (بنفسجي)

## الملفات المعدلة

- `lib/features/dashboard/widgets/quick_actions_grid.dart`

## الخدمات المستخدمة

- `InvoiceService.getAllInvoices()` - للحصول على عدد الفواتير
- `InvoicesManagementScreen` - شاشة إدارة الفواتير

## كيفية الاستخدام

1. افتح التطبيق وانتقل إلى لوحة التحكم
2. ستجد بطاقة "الفواتير" بجانب "العملاء" و "المنتجات"
3. انقر على البطاقة للانتقال إلى إدارة الفواتير
4. عند العودة، سيتم تحديث عدد الفواتير تلقائياً

## ملاحظات تقنية

- يتم تحميل عدد الفواتير بشكل متوازي مع المنتجات والعملاء
- يتم تحديث البيانات عند العودة من شاشة إدارة الفواتير
- التعامل مع الأخطاء في حالة فشل تحميل البيانات
- تصميم متجاوب مع جميع أحجام الشاشات
