# إصلاح مشكلة المبلغ المدفوع في نظام الفواتير - Atlas Medical Supplies

## المشكلة
كان هناك مشكلة في نظام الفواتير حيث أن المبلغ المدفوع عند إنشاء الفاتورة لا يتم إظهاره في الفاتورة ولا يتم خصمه من المبلغ الإجمالي.

## أسباب المشكلة
1. **عند إنشاء الفاتورة**: لا يتم تمرير `paidAmount` في `createInvoice`، وبالتالي يتم تعيينه إلى القيمة الافتراضية `0.0`
2. **عند إنشاء التحصيل**: يتم إنشاء التحصيل ولكن لا يتم تحديث `paidAmount` في الفاتورة
3. **المبلغ المدفوع لا يظهر في الفاتورة**: لأن `paidAmount` يبقى `0.0`

## الحلول المطبقة

### 1. تحديث `InvoiceService`
- **الملف**: `lib/services/invoice_service.dart`
- **التغيير**: إضافة معلمة `paidAmount` إلى دالة `createInvoice`
- **النتيجة**: الآن يمكن تمرير المبلغ المدفوع عند إنشاء الفاتورة

```dart
Future<InvoiceModel> createInvoice({
  required CustomerModel customer,
  required List<InvoiceItem> items,
  required DateTime invoiceDate,
  double discountAmount = 0.0,
  double discountPercentage = 0.0,
  double paidAmount = 0.0, // إضافة معلمة المبلغ المدفوع
  String? notes,
  String? createdBy,
}) async
```

### 2. تحديث `AddInvoiceScreen`
- **الملف**: `lib/features/invoices/screens/add_invoice_screen.dart`
- **التغيير**: تمرير `_paidAmount` إلى `createInvoice`
- **النتيجة**: المبلغ المدفوع يتم تمريره من واجهة المستخدم إلى خدمة الفواتير

```dart
final invoice = await _invoiceService.createInvoice(
  customer: _selectedCustomer!,
  items: _invoiceItems,
  invoiceDate: _invoiceDate,
  discountAmount: _isPercentageDiscount ? 0.0 : _discountValue,
  discountPercentage: _isPercentageDiscount
      ? double.tryParse(_discountPercentageController.text) ?? 0.0
      : 0.0,
  paidAmount: _paidAmount, // إضافة المبلغ المدفوع
  notes: _notesController.text.trim().isEmpty
      ? null
      : _notesController.text.trim(),
);
```

### 3. إنشاء `CollectionModel`
- **الملف**: `lib/models/collection_model.dart`
- **التغيير**: إنشاء نموذج جديد للتحصيلات
- **النتيجة**: نموذج منظم للتحصيلات مع دعم كامل للعمليات CRUD

### 4. إنشاء `CollectionService`
- **الملف**: `lib/services/collection_service.dart`
- **التغيير**: خدمة جديدة لإدارة التحصيلات مع تحديث تلقائي للمبلغ المدفوع
- **النتيجة**: عند إنشاء تحصيل، يتم تحديث `paidAmount` في الفاتورة تلقائياً

```dart
/// تحديث المبلغ المدفوع في الفاتورة
Future<void> _updateInvoicePaidAmount(String invoiceId, double amount) async {
  try {
    // الحصول على الفاتورة الحالية
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice != null) {
      // حساب المبلغ المدفوع الجديد
      final newPaidAmount = invoice.paidAmount + amount;
      
      // تحديث المبلغ المدفوع في الفاتورة
      await _invoiceService.updatePaidAmount(invoiceId, newPaidAmount);
      
      debugPrint('تم تحديث المبلغ المدفوع في الفاتورة: ${invoice.invoiceNumber} - المبلغ الجديد: $newPaidAmount');
    }
  } catch (e) {
    debugPrint('خطأ في تحديث المبلغ المدفوع في الفاتورة: $e');
    // لا نريد أن نوقف عملية إنشاء التحصيل بسبب هذا الخطأ
  }
}
```

### 5. تحديث `DatabaseService`
- **الملف**: `lib/services/database_service.dart`
- **التغيير**: إضافة دوال إدارة التحصيلات
- **النتيجة**: دعم كامل لقاعدة البيانات للتحصيلات

## الميزات الجديدة

### 1. تحديث تلقائي للمبلغ المدفوع
- عند إنشاء تحصيل، يتم تحديث `paidAmount` في الفاتورة تلقائياً
- دعم للدفعات الأولية والدفعات الإضافية

### 2. تتبع كامل للتحصيلات
- تسجيل جميع التحصيلات مع التاريخ والنوع
- إمكانية البحث في التحصيلات
- إحصائيات شاملة للتحصيلات

### 3. تكامل مع نظام الفواتير
- تحديث حالة الفاتورة تلقائياً بناءً على المبلغ المدفوع
- حساب المبلغ المتبقي تلقائياً

## كيفية الاستخدام

### 1. إنشاء فاتورة مع مبلغ مدفوع
```dart
final invoice = await invoiceService.createInvoice(
  customer: customer,
  items: items,
  invoiceDate: DateTime.now(),
  paidAmount: 100.0, // المبلغ المدفوع
  notes: 'فاتورة مع دفعة أولية',
);
```

### 2. إنشاء تحصيل إضافي
```dart
final collection = await collectionService.createAdditionalPayment(
  invoiceId: invoice.id,
  invoiceNumber: invoice.invoiceNumber,
  customerId: customer.id,
  customerName: customer.name,
  amount: 50.0, // المبلغ الإضافي
  notes: 'دفعة إضافية',
);
```

### 3. عرض المبلغ المدفوع
```dart
Text('المبلغ المدفوع: ${invoice.paidAmount.toStringAsFixed(2)} ج.م');
Text('المبلغ المتبقي: ${invoice.calculateRemainingAmount().toStringAsFixed(2)} ج.م');
```

## الاختبار

### 1. إنشاء فاتورة جديدة
- تأكد من أن المبلغ المدفوع يتم حفظه بشكل صحيح
- تأكد من أن `paidAmount` يظهر في تفاصيل الفاتورة

### 2. إنشاء تحصيل
- تأكد من أن المبلغ المدفوع يتم تحديثه في الفاتورة
- تأكد من أن حالة الفاتورة تتحدث تلقائياً

### 3. عرض الفاتورة
- تأكد من أن المبلغ المدفوع والمبلغ المتبقي يظهران بشكل صحيح
- تأكد من أن نسبة الدفع محسوبة بشكل صحيح

## ملاحظات مهمة

1. **النسخ الاحتياطية**: تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التطبيق
2. **الترقية**: النظام يدعم الترقية التلقائية لقاعدة البيانات
3. **التوافق**: جميع التغييرات متوافقة مع الإصدارات السابقة

## الاستنتاج

تم حل مشكلة المبلغ المدفوع بشكل كامل من خلال:
- إضافة دعم للمبلغ المدفوع عند إنشاء الفاتورة
- تحديث تلقائي للمبلغ المدفوع عند إنشاء التحصيلات
- تكامل كامل بين نظام الفواتير ونظام التحصيلات
- واجهة مستخدم محسنة لعرض معلومات الدفع

الآن يمكن للمستخدمين:
- إنشاء فواتير مع مبالغ مدفوعة
- تتبع جميع التحصيلات
- رؤية المبلغ المتبقي بشكل صحيح
- إدارة حالة الفواتير تلقائياً
