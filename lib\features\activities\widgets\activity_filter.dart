import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../models/activity_model.dart';

class ActivityFilter extends StatelessWidget {
  final ActivityType? selectedFilter;
  final Function(ActivityType?) onFilterChanged;

  const ActivityFilter({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _FilterChip(
            label: 'الكل',
            isSelected: selectedFilter == null,
            onTap: () => onFilterChanged(null),
            color: AppColors.primary,
          ),
          SizedBox(width: 8.w),
          ...ActivityType.values.map((type) {
            return Padding(
              padding: EdgeInsets.only(left: 8.w),
              child: _FilterChip(
                label: _getActivityTypeLabel(type),
                isSelected: selectedFilter == type,
                onTap: () => onFilterChanged(type),
                color: Activity.getColorFromType(type),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  String _getActivityTypeLabel(ActivityType type) {
    switch (type) {
      case ActivityType.customer:
        return 'العملاء';
      case ActivityType.product:
        return 'المنتجات';
      case ActivityType.returns:
        return 'المرتجعات';
      case ActivityType.alert:
        return 'التنبيهات';
    }
  }
}

class _FilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final Color color;

  const _FilterChip({
    required this.label,
    required this.isSelected,
    required this.onTap,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected ? color : AppColors.border,
            width: 1.5,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : AppColors.textSecondary,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }
}
