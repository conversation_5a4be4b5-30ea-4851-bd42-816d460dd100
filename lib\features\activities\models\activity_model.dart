import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';

enum ActivityType { customer, product, returns, alert }

class Activity {
  final String id;
  final String title;
  final String subtitle;
  final String amount;
  final DateTime timestamp;
  final IconData icon;
  final Color color;
  final ActivityType type;
  final Map<String, dynamic>? metadata;

  const Activity({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.timestamp,
    required this.icon,
    required this.color,
    required this.type,
    this.metadata,
  });

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }

  factory Activity.fromMap(Map<String, dynamic> map) {
    return Activity(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      subtitle: map['subtitle'] ?? '',
      amount: map['amount'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      icon: Activity.getIconFromType(
        ActivityType.values.firstWhere(
          (e) => e.toString() == 'ActivityType.${map['type']}',
          orElse: () => ActivityType.alert,
        ),
      ),
      color: Activity.getColorFromType(
        ActivityType.values.firstWhere(
          (e) => e.toString() == 'ActivityType.${map['type']}',
          orElse: () => ActivityType.alert,
        ),
      ),
      type: ActivityType.values.firstWhere(
        (e) => e.toString() == 'ActivityType.${map['type']}',
        orElse: () => ActivityType.alert,
      ),
      metadata: map['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'type': type.toString().split('.').last,
      'metadata': metadata,
    };
  }

  static IconData getIconFromType(ActivityType type) {
    switch (type) {

      case ActivityType.customer:
        return Icons.person_add;
      case ActivityType.product:
        return Icons.inventory;
      case ActivityType.returns:
        return Icons.keyboard_return;
      case ActivityType.alert:
        return Icons.warning;
    }
  }

  static Color getColorFromType(ActivityType type) {
    switch (type) {

      case ActivityType.customer:
        return AppColors.customers;
      case ActivityType.product:
        return AppColors.products;
      case ActivityType.returns:
        return AppColors.returns;
      case ActivityType.alert:
        return AppColors.warning;
    }
  }

  Activity copyWith({
    String? id,
    String? title,
    String? subtitle,
    String? amount,
    DateTime? timestamp,
    IconData? icon,
    Color? color,
    ActivityType? type,
    Map<String, dynamic>? metadata,
  }) {
    return Activity(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      amount: amount ?? this.amount,
      timestamp: timestamp ?? this.timestamp,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }
}
