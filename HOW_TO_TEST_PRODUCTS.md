# كيفية اختبار صفحة المنتجات - Atlas Medical Supplies

## 🚀 بدء الاختبار

### 1. تشغيل التطبيق
```bash
# في مجلد المشروع
flutter run
```

### 2. الوصول للصفحة التجريبية
```dart
// في أي شاشة في التطبيق
import 'package:atlas2/features/products/index.dart';

// عرض الشاشة التجريبية
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const DemoProductsScreen(),
  ),
);
```

## 🧪 اختبار الميزات

### ✅ اختبار عرض المنتجات
1. **افتح الشاشة التجريبية**
2. **اضغط على "فتح صفحة المنتجات"**
3. **تحقق من:**
   - ظهور 14 منتج تجريبي
   - عرض البطاقات بشكل صحيح
   - المعلومات كاملة (الاسم، الكود، السعر، الكمية)

### ✅ اختبار البحث
1. **اكتب في حقل البحث:**
   - `مقياس` → يجب أن يظهر مقياس ضغط الدم ومقياس الحرارة
   - `BP001` → يجب أن يظهر مقياس ضغط الدم فقط
   - `1234567890123` → يجب أن يظهر المنتج المقابل

### ✅ اختبار التصفية
1. **اضغط على أيقونة التصفية**
2. **جرب التصفية حسب الفئة:**
   - `أجهزة طبية` → 6 منتجات
   - `مستهلكات` → 2 منتج
   - `معقمات` → 2 منتج
   - `مستلزمات معمل` → 2 منتج
   - `مستلزمات عامة` → 2 منتج

3. **جرب تصفية المخزون المنخفض:**
   - فعّل "مخزون منخفض فقط"
   - يجب أن تظهر 2 منتجات (مقص طبي، ملقط طبي)

### ✅ اختبار الترتيب
1. **اضغط على أيقونة التصفية**
2. **جرب الترتيب حسب:**
   - `الاسم` → ترتيب أبجدي
   - `الكود` → ترتيب حسب الكود
   - `السعر` → ترتيب حسب السعر
   - `الكمية` → ترتيب حسب الكمية

3. **جرب اتجاه الترتيب:**
   - `تصاعدي` → من الأقل إلى الأعلى
   - `تنازلي` → من الأعلى إلى الأقل

### ✅ اختبار الإجراءات السريعة
1. **اضغط على أيقونة الإجراءات في أي بطاقة منتج**
2. **جرب الإجراءات:**
   - `تفاصيل المنتج` → عرض معلومات المنتج
   - `تعديل المنتج` → فتح نافذة التعديل
   - `بيع المنتج` → إنشاء فاتورة بيع
   - `تعديل الكمية` → تعديل كمية المخزون
   - `نسخ الكود` → نسخ كود المنتج

### ✅ اختبار إضافة منتج جديد
1. **اضغط على زر + (Floating Action Button)**
2. **املأ البيانات:**
   - اسم المنتج: `منتج تجريبي جديد`
   - كود المنتج: `TEST001`
   - سعر البيع: `100.00`
   - سعر التكلفة: `80.00`
   - الكمية الأولية: `50`
   - الفئة: `أجهزة طبية`
   - الوحدة: `قطعة`

3. **اضغط "حفظ"**
4. **تحقق من ظهور المنتج الجديد في القائمة**

## 📊 البيانات التجريبية المتوفرة

### أجهزة طبية (6 منتجات)
- مقياس ضغط الدم الرقمي - 299.99 ر.س
- مقياس حرارة طبي - 89.99 ر.س
- مقص طبي - 55.99 ر.س (مخزون منخفض)
- ملقط طبي - 75.99 ر.س (مخزون منخفض)
- سماعة طبية - 199.99 ر.س (نفذ المخزون)
- مصباح طبي LED - 89.99 ر.س (نفذ المخزون)

### مستهلكات (2 منتج)
- قفازات طبية لاتكس - 15.99 ر.س
- كمامات طبية - 25.99 ر.س

### معقمات (2 منتج)
- معقم اليدين - 45.99 ر.س
- محلول تعقيم الأسطح - 35.99 ر.س

### مستلزمات معمل (2 منتج)
- أنابيب اختبار - 12.99 ر.س
- شرائح مجهرية - 8.99 ر.س

### مستلزمات عامة (2 منتج)
- ضمادات طبية - 18.99 ر.س
- شريط طبي - 22.99 ر.س

## 🔍 اختبارات متقدمة

### اختبار الأداء
1. **تحميل سريع**: يجب أن تظهر المنتجات خلال ثانية واحدة
2. **بحث فوري**: النتائج تظهر أثناء الكتابة
3. **حركات سلسة**: الانتقال بين الشاشات سلس

### اختبار التجاوب
1. **أفقياً**: تدوير الجهاز للوضع الأفقي
2. **أحجام مختلفة**: اختبار على أجهزة بأحجام شاشات مختلفة

### اختبار اللغة العربية
1. **اتجاه RTL**: النص من اليمين إلى اليسار
2. **الأرقام**: عرض الأرقام بالشكل الصحيح
3. **التاريخ**: عرض التواريخ بالشكل العربي

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا تظهر المنتجات
**المشكلة**: القائمة فارغة
**الحل**: 
- تأكد من تشغيل `SampleProductsData.getSampleProducts()`
- تحقق من عدم وجود أخطاء في Console

#### 2. خطأ في البحث
**المشكلة**: البحث لا يعمل
**الحل**:
- تأكد من صحة `onSearchChanged` callback
- تحقق من عدم وجود أخطاء في `searchProducts` method

#### 3. بطء في التحميل
**المشكلة**: الصفحة بطيئة
**الحل**:
- تحقق من عدم وجود عمليات ثقيلة في `initState`
- استخدم `ListView.builder` بدلاً من `ListView`

#### 4. مشاكل في التصميم
**المشكلة**: العناصر غير منسقة
**الحل**:
- تأكد من وجود `AppColors` و `AppStrings`
- تحقق من صحة الـ imports

## 📱 اختبار على أجهزة مختلفة

### Android
```bash
flutter run -d android
```

### iOS
```bash
flutter run -d ios
```

### Web
```bash
flutter run -d chrome
```

### Desktop
```bash
flutter run -d windows  # Windows
flutter run -d macos    # macOS
flutter run -d linux    # Linux
```

## 🧪 تشغيل الاختبارات التلقائية

### تشغيل جميع الاختبارات
```bash
flutter test
```

### تشغيل اختبارات المنتجات فقط
```bash
flutter test test/products_list_screen_test.dart
```

### تشغيل اختبارات محددة
```bash
flutter test --name "should display products list screen"
```

## 📋 قائمة التحقق

### ✅ الوظائف الأساسية
- [ ] عرض قائمة المنتجات
- [ ] البحث في المنتجات
- [ ] تصفية حسب الفئة
- [ ] تصفية حسب المخزون
- [ ] ترتيب المنتجات
- [ ] إضافة منتج جديد
- [ ] تعديل المنتج
- [ ] حذف المنتج
- [ ] عرض تفاصيل المنتج

### ✅ التصميم والواجهة
- [ ] دعم اللغة العربية (RTL)
- [ ] ألوان متناسقة
- [ ] تصميم متجاوب
- [ ] حركات سلسة
- [ ] أيقونات واضحة

### ✅ الأداء
- [ ] تحميل سريع
- [ ] بحث فوري
- [ ] استجابة سريعة
- [ ] ذاكرة محسنة

## 🎯 نصائح للاختبار

1. **اختبر جميع السيناريوهات**: عادي، مخزون منخفض، نفذ المخزون
2. **جرب القيم الحدية**: كميات صفرية، أسعار عالية، نصوص طويلة
3. **اختبر الأخطاء**: إدخال بيانات غير صحيحة، انقطاع الاتصال
4. **اختبر الأداء**: مع قوائم كبيرة، عمليات متعددة
5. **اختبر التجاوب**: أحجام شاشات مختلفة، اتجاهات مختلفة

## 🚀 بعد الاختبار

### إذا كان كل شيء يعمل بشكل صحيح:
- ✅ الصفحة جاهزة للإنتاج
- ✅ يمكن دمجها في التطبيق الرئيسي
- ✅ يمكن إضافة ميزات جديدة

### إذا كانت هناك مشاكل:
- 🔧 راجع الأخطاء في Console
- 📝 سجل المشاكل في قائمة المهام
- 🐛 أصلح الأخطاء قبل الإنتاج

---

**تم إنشاء هذا الدليل بواسطة فريق Atlas Medical Supplies**
**آخر تحديث**: ديسمبر 2024

**صفحة المنتجات جاهزة للاختبار والاستخدام! 🎉**
