import '../models/activity_model.dart';

class ActivityService {
  static final ActivityService _instance = ActivityService._internal();
  factory ActivityService() => _instance;
  ActivityService._internal();

  // قائمة الأنشطة المؤقتة (يمكن استبدالها بقاعدة بيانات لاحقاً)
  final List<Activity> _activities = [
    Activity(
      id: '3',
      title: 'عميل جديد',
      subtitle: 'تم إضافة عميل جديد: مكتب طبي الشفاء - نوع العميل: مكتب طبي أ',
      amount: '',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      icon: Activity.getIconFromType(ActivityType.customer),
      color: Activity.getColorFromType(ActivityType.customer),
      type: ActivityType.customer,
      metadata: {
        'customerId': 'C003',
        'customerName': 'مكتب طبي الشفاء',
        'customerType': 'medicalOfficeA',
      },
    ),
    Activity(
      id: '4',
      title: 'منتج جديد',
      subtitle: 'تم إضافة منتج جديد: جهاز قياس ضغط الدم الرقمي',
      amount: '-- ر.س',
      timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
      icon: Activity.getIconFromType(ActivityType.product),
      color: Activity.getColorFromType(ActivityType.product),
      type: ActivityType.product,
      metadata: {
        'productId': 'P003',
        'productName': 'جهاز قياس ضغط الدم الرقمي',
        'price': 1200.0,
      },
    ),
    Activity(
      id: '5',
      title: 'مرتجع منتج',
      subtitle: 'عميل: عيادة الأطفال المتخصصة - مرتجع: جهاز قياس حرارة',
      amount: '-- ر.س',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      icon: Activity.getIconFromType(ActivityType.returns),
      color: Activity.getColorFromType(ActivityType.returns),
      type: ActivityType.returns,
      metadata: {
        'customerId': 'C004',
        'customerName': 'عيادة الأطفال المتخصصة',
        'productId': 'P004',
        'productName': 'جهاز قياس حرارة',
        'reason': 'عيب في التصنيع',
      },
    ),
    Activity(
      id: '6',
      title: 'تنبيه مخزون منخفض',
      subtitle:
          'انخفاض مخزون المنتج: قفازات طبية لاتكس - الكمية المتبقية: -- علب',
      amount: '-- علب',
      timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      icon: Activity.getIconFromType(ActivityType.alert),
      color: Activity.getColorFromType(ActivityType.alert),
      type: ActivityType.alert,
      metadata: {
        'productId': 'P001',
        'productName': 'قفازات طبية لاتكس',
        'currentStock': 5,
        'minStock': 10,
      },
    ),

    Activity(
      id: '8',
      title: 'تحديث منتج',
      subtitle:
          'تم تحديث سعر المنتج: كمامات طبية 3 طبقات - السعر الجديد: -- جنيه',
      amount: '-- ر.س',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      icon: Activity.getIconFromType(ActivityType.product),
      color: Activity.getColorFromType(ActivityType.product),
      type: ActivityType.product,
      metadata: {
        'productId': 'P002',
        'productName': 'كمامات طبية 3 طبقات',
        'oldPrice': 50.0,
        'newPrice': 55.0,
      },
    ),
    Activity(
      id: '9',
      title: 'عميل جديد',
      subtitle: 'تم إضافة عميل جديد: صيدلية الرحمة - نوع العميل: موزع',
      amount: '',
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      icon: Activity.getIconFromType(ActivityType.customer),
      color: Activity.getColorFromType(ActivityType.customer),
      type: ActivityType.customer,
      metadata: {
        'customerId': 'C006',
        'customerName': 'صيدلية الرحمة',
        'customerType': 'distributor',
      },
    ),
    Activity(
      id: '10',
      title: 'تنبيه انتهاء صلاحية',
      subtitle: 'تنبيه: منتج قارب على انتهاء الصلاحية - مطهر كحولي 70%',
      amount: '-- يوم',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      icon: Activity.getIconFromType(ActivityType.alert),
      color: Activity.getColorFromType(ActivityType.alert),
      type: ActivityType.alert,
      metadata: {
        'productId': 'P005',
        'productName': 'مطهر كحولي 70%',
        'expiryDate': '2024-02-15',
        'daysLeft': 30,
      },
    ),
  ];

  Future<List<Activity>> getActivities({
    ActivityType? filter,
    String? searchQuery,
    int limit = 50,
  }) async {
    // محاكاة تأخير الشبكة
    await Future.delayed(const Duration(milliseconds: 500));

    var filteredActivities = List<Activity>.from(_activities);

    // تطبيق فلتر النوع
    if (filter != null) {
      filteredActivities = filteredActivities
          .where((activity) => activity.type == filter)
          .toList();
    }

    // تطبيق فلتر البحث
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredActivities = filteredActivities
          .where(
            (activity) =>
                activity.title.toLowerCase().contains(query) ||
                activity.subtitle.toLowerCase().contains(query),
          )
          .toList();
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filteredActivities.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // تحديد عدد النتائج
    if (filteredActivities.length > limit) {
      filteredActivities = filteredActivities.take(limit).toList();
    }

    return filteredActivities;
  }

  Future<List<Activity>> getRecentActivities({int limit = 5}) async {
    return getActivities(limit: limit);
  }

  Future<Activity?> getActivityById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));

    try {
      return _activities.firstWhere((activity) => activity.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addActivity(Activity activity) async {
    await Future.delayed(const Duration(milliseconds: 300));
    _activities.insert(0, activity);
  }

  Future<Map<String, int>> getActivityStats() async {
    await Future.delayed(const Duration(milliseconds: 200));

    final stats = <String, int>{};
    for (final type in ActivityType.values) {
      stats[type.toString()] = _activities
          .where((activity) => activity.type == type)
          .length;
    }

    return stats;
  }

  Future<void> addCustomerActivity({
    required String customerId,
    required String customerName,
    required String customerType,
  }) async {
    final activity = Activity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'عميل جديد',
      subtitle: 'تم إضافة عميل جديد: $customerName',
      amount: '',
      timestamp: DateTime.now(),
      icon: Activity.getIconFromType(ActivityType.customer),
      color: Activity.getColorFromType(ActivityType.customer),
      type: ActivityType.customer,
      metadata: {
        'customerId': customerId,
        'customerName': customerName,
        'customerType': customerType,
      },
    );

    await addActivity(activity);
  }

  Future<void> addProductActivity({
    required String productId,
    required String productName,
    required double price,
    required String action, // 'added' or 'updated'
  }) async {
    final activity = Activity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: action == 'added' ? 'منتج جديد' : 'تحديث منتج',
      subtitle: action == 'added'
          ? 'تم إضافة منتج جديد: $productName'
          : 'تم تحديث المنتج: $productName',
      amount: '-- ر.س',
      timestamp: DateTime.now(),
      icon: Activity.getIconFromType(ActivityType.product),
      color: Activity.getColorFromType(ActivityType.product),
      type: ActivityType.product,
      metadata: {
        'productId': productId,
        'productName': productName,
        'price': price,
        'action': action,
      },
    );

    await addActivity(activity);
  }
}
