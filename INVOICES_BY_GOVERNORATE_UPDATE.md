# تحديث عرض الفواتير حسب المحافظات

## نظرة عامة
تم إضافة ميزة جديدة لعرض الفواتير حسب المحافظات في قائمة الفواتير، مشابهة لنظام عرض العملاء حسب المحافظات.

## الميزات المضافة

### 1. عرض الفواتير حسب المحافظات
- إضافة زر تبديل في شريط العنوان للتحويل بين العرض العادي والعرض حسب المحافظات
- عرض الفواتير مجمعة حسب محافظة العميل
- عداد لكل محافظة يوضح عدد الفواتير فيها

### 2. شاشة فواتير المحافظة
- شاشة جديدة لعرض فواتير محافظة محددة
- إمكانية حذف وتحديث حالة الفواتير
- مشاركة الفواتير
- عرض تفاصيل الفواتير

### 3. البحث والفلترة
- دعم البحث في الفواتير حسب المحافظات
- فلترة حسب حالة الفاتورة (مدفوع/غير مدفوع)
- تحديث فوري للنتائج

## الملفات المضافة/المعدلة

### ملفات جديدة:
- `lib/features/invoices/widgets/invoices_by_governorate_list.dart`
- `lib/features/invoices/screens/governorate_invoices_screen.dart`

### ملفات معدلة:
- `lib/services/invoice_service.dart` - إضافة دوال جديدة
- `lib/features/invoices/screens/invoices_screen.dart` - تحديث الواجهة

## الدوال الجديدة في InvoiceService

```dart
/// الحصول على الفواتير حسب المحافظة
Stream<Map<String, List<InvoiceModel>>> getInvoicesByGovernorate()

/// الحصول على فاتورة بواسطة المعرف
Future<InvoiceModel?> getInvoiceById(String invoiceId)
```

## كيفية الاستخدام

1. **الوصول للقائمة**: انتقل إلى قائمة الفواتير
2. **تبديل العرض**: اضغط على زر التبديل في شريط العنوان
3. **عرض المحافظات**: ستظهر قائمة بالمحافظات وعدد الفواتير في كل محافظة
4. **عرض فواتير محافظة**: اضغط على أي محافظة لعرض فواتيرها
5. **البحث والفلترة**: استخدم شريط البحث والفلترة كما هو معتاد

## المزايا

- **تنظيم أفضل**: سهولة في العثور على فواتير محافظة محددة
- **إحصائيات واضحة**: عرض عدد الفواتير في كل محافظة
- **تجربة مستخدم محسنة**: واجهة متناسقة مع باقي التطبيق
- **أداء محسن**: تحميل البيانات حسب الحاجة

## التوافق

- يعمل مع جميع أنواع الفواتير
- يدعم البحث والفلترة الحالية
- متوافق مع نظام الحذف والتحديث
- يدعم مشاركة الفواتير

## ملاحظات تقنية

- يستخدم Stream للبيانات المحدثة فورياً
- يدعم التعامل مع الأخطاء
- يحافظ على حالة البحث والفلترة
- يستخدم نفس تصميم واجهة العملاء للمرونة
