import 'package:flutter/material.dart';
import '../constants/app_strings.dart';

class SecurityService {
  static const String _adminPassword = '123456'; // كلمة المرور الافتراضية

  /// التحقق من كلمة المرور
  static bool verifyPassword(String password) {
    return password == _adminPassword;
  }

  /// تغيير كلمة المرور
  static bool changePassword(String oldPassword, String newPassword) {
    if (verifyPassword(oldPassword)) {
      // في التطبيق الحقيقي، سيتم حفظ كلمة المرور الجديدة في قاعدة البيانات
      // أو في التخزين المحلي المشفر
      return true;
    }
    return false;
  }

  /// عرض حوار إدخال كلمة المرور
  static Future<bool> showPasswordDialog(
    BuildContext context,
    String title,
  ) async {
    final TextEditingController passwordController = TextEditingController();
    bool isPasswordVisible = false;

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                  title: Text(title),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(AppStrings.passwordRequired),
                      const SizedBox(height: 16),
                      TextField(
                        controller: passwordController,
                        obscureText: !isPasswordVisible,
                        decoration: InputDecoration(
                          labelText: AppStrings.passwordHint,
                          hintText: AppStrings.enterPassword,
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              isPasswordVisible
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                isPasswordVisible = !isPasswordVisible;
                              });
                            },
                          ),
                        ),
                        onSubmitted: (value) {
                          if (verifyPassword(value)) {
                            Navigator.of(context).pop(true);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(AppStrings.wrongPassword),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text(AppStrings.cancelAction),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (verifyPassword(passwordController.text)) {
                          Navigator.of(context).pop(true);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(AppStrings.wrongPassword),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      child: Text(AppStrings.confirmDeleteProtected),
                    ),
                  ],
                );
              },
            );
          },
        ) ??
        false;
  }

  /// عرض حوار تأكيد الحذف مع كلمة المرور
  static Future<bool> showDeleteConfirmationDialog(
    BuildContext context,
    String itemName,
    String itemType,
  ) async {
    // أولاً، عرض حوار التأكيد
    final bool confirmed =
        await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(AppStrings.deleteConfirmation),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('هل أنت متأكد من حذف $itemType "$itemName"؟'),
                  const SizedBox(height: 8),
                  Text(
                    AppStrings.deleteWarning,
                    style: const TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(AppStrings.cancelAction),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: Text(AppStrings.confirmDeleteProtected),
                ),
              ],
            );
          },
        ) ??
        false;

    if (!confirmed) return false;

    // إذا تم التأكيد، عرض حوار كلمة المرور
    return await showPasswordDialog(context, AppStrings.deleteConfirmation);
  }
}
