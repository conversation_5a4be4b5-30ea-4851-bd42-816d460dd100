import 'package:flutter/material.dart';
import '../../../models/invoice_model.dart';
import '../../../constants/app_colors.dart';

class InvoiceFilterDialog extends StatefulWidget {
  final InvoiceStatus? selectedStatus;

  const InvoiceFilterDialog({
    Key? key,
    this.selectedStatus,
  }) : super(key: key);

  @override
  State<InvoiceFilterDialog> createState() => _InvoiceFilterDialogState();
}

class _InvoiceFilterDialogState extends State<InvoiceFilterDialog> {
  InvoiceStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.selectedStatus;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'فلترة الفواتير',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'حالة الفاتورة:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          _buildStatusOption(
            status: null,
            label: 'جميع الفواتير',
            icon: Icons.list,
          ),
          _buildStatusOption(
            status: InvoiceStatus.pending,
            label: 'معلق',
            icon: Icons.pending,
            color: Colors.orange,
          ),
          _buildStatusOption(
            status: InvoiceStatus.paid,
            label: 'تم الدفع بالكامل',
            icon: Icons.check_circle,
            color: Colors.green,
          ),
          _buildStatusOption(
            status: InvoiceStatus.cancelled,
            label: 'ملغي',
            icon: Icons.cancel,
            color: Colors.red,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'إلغاء',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(_selectedStatus),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  Widget _buildStatusOption({
    required InvoiceStatus? status,
    required String label,
    required IconData icon,
    Color? color,
  }) {
    final isSelected = _selectedStatus == status;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedStatus = status;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primary.withOpacity(0.1)
              : Colors.grey.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected 
                ? AppColors.primary
                : Colors.grey.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : (color ?? Colors.grey[600]),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? AppColors.primary : Colors.black87,
                ),
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
