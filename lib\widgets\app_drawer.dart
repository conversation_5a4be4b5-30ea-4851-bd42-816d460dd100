import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../constants/app_colors.dart';
import '../utils/screen_utils.dart';
import '../features/statistics/screens/statistics_screen.dart';
import '../features/charts/screens/charts_screen.dart';
import '../features/settings/screens/settings_screen.dart';
import '../features/activities/screens/activities_screen.dart';
import '../features/invoices/screens/invoices_screen.dart';
import '../features/invoices/screens/invoices_by_governorate_screen.dart';
import '../features/invoices/screens/invoices_by_date_screen.dart';
import '../features/sales/screens/sales_screen.dart';

import '../services/auth_service.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Drawer(
      backgroundColor:
          Colors.white, // تغيير من AppColors.surface إلى Colors.white
      width: ScreenUtils.getDrawerWidth(context),
      child: Column(
        children: [
          // Header
          Container(
            height: isSmallScreen ? 180.h : 200.h,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.primary, AppColors.primaryDark],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 16.w : 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Logo/Icon
                    Container(
                      height: isSmallScreen ? 50.h : 60.h,
                      width: isSmallScreen ? 50.w : 60.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          isSmallScreen ? 25.r : 30.r,
                        ),
                      ),
                      child: Icon(
                        Icons.medical_services,
                        color: AppColors.primary,
                        size: ScreenUtils.getResponsiveIconSize(
                          context,
                          smallSize: 25.sp,
                          mediumSize: 28.sp,
                          largeSize: 30.sp,
                        ),
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 12.h : 16.h),
                    // App Title
                    Text(
                      'أطلس للمستلزمات الطبية',
                      style: TextStyle(
                        fontSize: ScreenUtils.getResponsiveFontSize(
                          context,
                          smallSize: 16.sp,
                          mediumSize: 17.sp,
                          largeSize: 18.sp,
                        ),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'نظام إدارة المبيعات',
                      style: TextStyle(
                        fontSize: ScreenUtils.getResponsiveFontSize(
                          context,
                          smallSize: 12.sp,
                          mediumSize: 13.sp,
                          largeSize: 14.sp,
                        ),
                        color: Colors.white.withValues(alpha: 0.9),
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                SizedBox(height: isSmallScreen ? 12.h : 16.h),

                // Invoices Section
                _buildSectionHeader('الفواتير', context),

                _buildMenuItem(
                  icon: Icons.receipt_long,
                  title: 'إدارة الفواتير',
                  subtitle: 'عرض وإدارة جميع الفواتير',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const InvoicesScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  icon: Icons.calendar_today,
                  title: 'الفواتير حسب التاريخ',
                  subtitle: 'عرض الفواتير حسب الفترات الزمنية',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const InvoicesByDateScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  icon: Icons.location_on,
                  title: 'الفواتير حسب المحافظات',
                  subtitle: 'عرض الفواتير حسب المحافظة',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) =>
                            const InvoicesByGovernorateScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  icon: Icons.trending_up,
                  title: 'المبيعات',
                  subtitle: 'تقرير مفصل للمبيعات',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SalesScreen(),
                      ),
                    );
                  },
                ),

                Divider(
                  height: isSmallScreen ? 24.h : 32.h,
                  thickness: 1,
                  color: AppColors.border,
                  indent: 16.w,
                  endIndent: 16.w,
                ),

                Divider(
                  height: isSmallScreen ? 24.h : 32.h,
                  thickness: 1,
                  color: AppColors.border,
                  indent: 16.w,
                  endIndent: 16.w,
                ),

                // Activities Section
                _buildSectionHeader('الأنشطة والمتابعة', context),

                _buildMenuItem(
                  icon: Icons.history,
                  title: 'الأنشطة الأخيرة',
                  subtitle: 'متابعة آخر العمليات والأنشطة',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ActivitiesScreen(),
                      ),
                    );
                  },
                ),

                Divider(
                  height: isSmallScreen ? 24.h : 32.h,
                  thickness: 1,
                  color: AppColors.border,
                  indent: 16.w,
                  endIndent: 16.w,
                ),

                // Statistics Section
                _buildSectionHeader('التقارير والإحصائيات', context),

                _buildMenuItem(
                  icon: Icons.analytics,
                  title: 'الإحصائيات العامة',
                  subtitle: 'عرض الأرقام والبيانات العامة',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const StatisticsScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  icon: Icons.bar_chart,
                  title: 'الإحصائيات المرئية',
                  subtitle: 'الرسوم البيانية والمخططات',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ChartsScreen(),
                      ),
                    );
                  },
                ),

                Divider(
                  height: isSmallScreen ? 24.h : 32.h,
                  thickness: 1,
                  color: AppColors.border,
                  indent: 16.w,
                  endIndent: 16.w,
                ),

                // Settings Section
                _buildSectionHeader('الإعدادات', context),

                _buildMenuItem(
                  icon: Icons.settings,
                  title: 'إعدادات التطبيق',
                  subtitle: 'تخصيص إعدادات النظام',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => SettingsScreen()),
                    );
                  },
                ),

                _buildMenuItem(
                  icon: Icons.backup,
                  title: 'النسخ الاحتياطي',
                  subtitle: 'حفظ واستعادة البيانات',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    _showBackupDialog(context);
                  },
                ),

                _buildMenuItem(
                  icon: Icons.info_outline,
                  title: 'حول التطبيق',
                  subtitle: 'معلومات الإصدار والمطور',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog(context);
                  },
                ),

                Divider(
                  height: isSmallScreen ? 24.h : 32.h,
                  thickness: 1,
                  color: AppColors.border,
                  indent: 16.w,
                  endIndent: 16.w,
                ),

                _buildMenuItem(
                  icon: Icons.logout,
                  title: 'تسجيل الخروج',
                  subtitle: 'إنهاء الجلسة والخروج من التطبيق',
                  context: context,
                  onTap: () {
                    Navigator.pop(context);
                    _showLogoutDialog(context);
                  },
                ),

                SizedBox(height: isSmallScreen ? 16.h : 20.h),
              ],
            ),
          ),

          // Footer
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12.w : 16.w),
            child: Column(
              children: [
                Divider(color: AppColors.border),
                SizedBox(height: 8.h),
                Text(
                  'الإصدار 1.0.0',
                  style: TextStyle(
                    fontSize: ScreenUtils.getResponsiveFontSize(
                      context,
                      smallSize: 11.sp,
                      mediumSize: 11.5.sp,
                      largeSize: 12.sp,
                    ),
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  '© 2024 أطلس للمستلزمات الطبية',
                  style: TextStyle(
                    fontSize: ScreenUtils.getResponsiveFontSize(
                      context,
                      smallSize: 9.sp,
                      mediumSize: 9.5.sp,
                      largeSize: 10.sp,
                    ),
                    color: AppColors.textSecondary,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: isSmallScreen ? 6.h : 8.h,
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: ScreenUtils.getResponsiveFontSize(
            context,
            smallSize: 13.sp,
            mediumSize: 13.5.sp,
            largeSize: 14.sp,
          ),
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required BuildContext context,
    required VoidCallback onTap,
  }) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(isSmallScreen ? 6.w : 8.w),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(isSmallScreen ? 6.r : 8.r),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: ScreenUtils.getResponsiveIconSize(
            context,
            smallSize: 18.sp,
            mediumSize: 19.sp,
            largeSize: 20.sp,
          ),
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: ScreenUtils.getResponsiveFontSize(
            context,
            smallSize: 13.sp,
            mediumSize: 13.5.sp,
            largeSize: 14.sp,
          ),
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          fontFamily: 'Cairo',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: ScreenUtils.getResponsiveFontSize(
            context,
            smallSize: 11.sp,
            mediumSize: 11.5.sp,
            largeSize: 12.sp,
          ),
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      onTap: onTap,
      dense: true,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: isSmallScreen ? 2.h : 4.h,
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('النسخ الاحتياطي', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'ستتوفر ميزة النسخ الاحتياطي في الإصدارات القادمة.',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'أطلس للمستلزمات الطبية',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.medical_services,
        color: AppColors.primary,
        size: 32.sp,
      ),
      children: [
        Text(
          'نظام إدارة المبيعات والمخزون للمستلزمات الطبية',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        SizedBox(height: 16.h),
        Text(
          'تم تطوير هذا التطبيق لإدارة عمليات البيع والعملاء والمخزون بطريقة سهلة وفعالة.',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
      ],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تسجيل الخروج',
          style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟ سيتم حذف بيانات تسجيل الدخول المحفوظة.',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontFamily: 'Cairo',
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authService = Provider.of<AuthService>(
                  context,
                  listen: false,
                );
                await authService.signOut();
                Navigator.of(
                  context,
                ).pushNamedAndRemoveUntil('/login', (route) => false);
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'حدث خطأ في تسجيل الخروج: $e',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text('تسجيل الخروج', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }
}
