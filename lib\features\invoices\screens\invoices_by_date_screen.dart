import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_service.dart';
import '../../../constants/app_colors.dart';
import '../../../widgets/back_button.dart';
import '../widgets/invoice_card.dart';
import 'invoice_details_screen.dart';
import 'add_invoice_screen.dart';

class InvoicesByDateScreen extends StatefulWidget {
  const InvoicesByDateScreen({super.key});

  @override
  State<InvoicesByDateScreen> createState() => _InvoicesByDateScreenState();
}

class _InvoicesByDateScreenState extends State<InvoicesByDateScreen> {
  final InvoiceService _invoiceService = InvoiceService();

  List<InvoiceModel> _invoices = [];
  List<InvoiceModel> _filteredInvoices = [];
  bool _isLoading = true;
  String _errorMessage = '';
  String _selectedPeriod = 'all'; // القيمة الافتراضية: الكل

  // خيارات التاريخ المحدثة
  final List<Map<String, dynamic>> _dateOptions = [
    {
      'key': 'all',
      'label': 'الكل',
      'icon': Icons.all_inclusive,
      'color': Colors.indigo,
    },
    {
      'key': 'today',
      'label': 'اليوم',
      'icon': Icons.today,
      'color': Colors.blue,
    },
    {
      'key': 'week',
      'label': 'الأسبوع',
      'icon': Icons.view_week,
      'color': Colors.green,
    },
    {
      'key': 'month',
      'label': 'الشهر',
      'icon': Icons.calendar_month,
      'color': Colors.orange,
    },
    {
      'key': 'year',
      'label': 'السنة',
      'icon': Icons.calendar_today,
      'color': Colors.purple,
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final invoices = await _invoiceService.getAllInvoices();

      setState(() {
        _invoices = invoices;
        _filterInvoicesByDate(_selectedPeriod);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الفواتير: $e';
        _isLoading = false;
      });
    }
  }

  void _filterInvoicesByDate(String period) {
    setState(() {
      _selectedPeriod = period;
    });

    // إذا كان الخيار "الكل"، نعرض جميع الفواتير
    if (period == 'all') {
      setState(() {
        _filteredInvoices = _invoices;
      });
      return;
    }

    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate = now;

    switch (period) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'year':
        startDate = DateTime(now.year, 1, 1);
        break;
      default:
        startDate = DateTime(now.year, now.month, now.day);
    }

    setState(() {
      _filteredInvoices = _invoices.where((invoice) {
        final invoiceDate = invoice.invoiceDate;
        return invoiceDate.isAfter(
              startDate.subtract(const Duration(days: 1)),
            ) &&
            invoiceDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    });
  }

  String _getPeriodText(String period) {
    switch (period) {
      case 'all':
        return 'الكل';
      case 'today':
        return 'اليوم';
      case 'week':
        return 'الأسبوع';
      case 'month':
        return 'الشهر';
      case 'year':
        return 'السنة';
      default:
        return 'اليوم';
    }
  }

  double _calculateTotalAmount() {
    return _filteredInvoices.fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.calendar_today,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'الفواتير حسب التاريخ',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 20,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
      ),
      body: Column(
        children: [
          // شريط الفلترة العلوي المصمم
          _buildFilterBar(),
          // قائمة الفواتير
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                ? _buildErrorWidget()
                : _buildInvoicesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.grey[50]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان الشريط
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.filter_list,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'اختر الفترة الزمنية',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          // أزرار الفلترة
          Row(
            children: _dateOptions.map((option) {
              final isSelected = _selectedPeriod == option['key'];
              final color = option['color'] as Color;
              return Expanded(
                child: GestureDetector(
                  onTap: () => _filterInvoicesByDate(option['key']),
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 6.w),
                    padding: EdgeInsets.symmetric(
                      vertical: 16.h,
                      horizontal: 8.w,
                    ),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? LinearGradient(
                              colors: [color, color.withValues(alpha: 0.8)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                      color: isSelected ? null : Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                      border: isSelected
                          ? null
                          : Border.all(color: Colors.grey[300]!, width: 1.5),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: color.withValues(alpha: 0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ]
                          : [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          option['icon'],
                          color: isSelected ? Colors.white : color,
                          size: 24.sp,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          option['label'],
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                            color: isSelected ? Colors.white : color,
                            fontFamily: 'Cairo',
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          // إحصائيات سريعة
          if (_filteredInvoices.isNotEmpty) ...[
            SizedBox(height: 20.h),
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withValues(alpha: 0.1),
                    AppColors.secondary.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 1.5,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    icon: Icons.receipt_long,
                    label: 'عدد الفواتير',
                    value: '${_filteredInvoices.length}',
                    color: AppColors.primary,
                  ),
                  Container(
                    width: 1,
                    height: 40.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          AppColors.primary.withValues(alpha: 0.3),
                          Colors.transparent,
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                  _buildStatItem(
                    icon: Icons.attach_money,
                    label: 'إجمالي المبلغ',
                    value: '${_calculateTotalAmount().toStringAsFixed(2)} ر.س',
                    color: AppColors.secondary,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 24.sp),
        ),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: color,
            fontFamily: 'Cairo',
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
            fontFamily: 'Cairo',
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل الفواتير',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _errorMessage,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _loadInvoices,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesList() {
    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64.sp, color: Colors.grey[400]),
            SizedBox(height: 16.h),
            Text(
              'لا توجد فواتير في هذه الفترة',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'جرب اختيار فترة زمنية مختلفة',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: InvoiceCard(
            invoice: invoice,
            onTap: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => InvoiceDetailsScreen(invoice: invoice),
                ),
              );
              if (result == true) {
                _loadInvoices();
              }
            },
          ),
        );
      },
    );
  }
}
