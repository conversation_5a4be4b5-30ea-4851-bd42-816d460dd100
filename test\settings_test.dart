import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:atlas_medical_supplies/core/settings/app_settings.dart';
import 'package:atlas_medical_supplies/core/settings/settings_manager.dart';

void main() {
  group('AppSettings Tests', () {
    late AppSettings settings;

    setUp(() {
      settings = AppSettings();
    });

    test('should initialize with default values', () {
      expect(settings.splashOptimization, true);
      expect(settings.enableRTL, true);
      expect(settings.selectedLanguage, 'ar');
      expect(settings.enableDatabaseEncryption, true);
    });

    test('should convert to and from JSON', () {
      // تغيير بعض القيم
      settings.splashOptimization = false;
      settings.enableRTL = false;
      settings.selectedLanguage = 'en';

      // تحويل إلى JSON
      final json = settings.toJson();
      expect(json['splashOptimization'], false);
      expect(json['enableRTL'], false);
      expect(json['selectedLanguage'], 'en');

      // إنشاء إعدادات جديدة من JSON
      final newSettings = AppSettings();
      newSettings.fromJson(json);
      expect(newSettings.splashOptimization, false);
      expect(newSettings.enableRTL, false);
      expect(newSettings.selectedLanguage, 'en');
    });

    test('should reset to defaults', () {
      // تغيير القيم
      settings.splashOptimization = false;
      settings.enableRTL = false;
      settings.selectedLanguage = 'en';

      // إعادة تعيين
      settings.resetToDefaults();
      expect(settings.splashOptimization, true);
      expect(settings.enableRTL, true);
      expect(settings.selectedLanguage, 'ar');
    });
  });

  group('SettingsManager Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with default settings', () {
      final settings = container.read(settingsProvider);
      expect(settings.splashOptimization, true);
      expect(settings.enableRTL, true);
    });

    test('should toggle settings', () {
      final manager = container.read(settingsProvider.notifier);
      final initialValue = container.read(settingsProvider).splashOptimization;

      manager.toggleSplashOptimization();
      expect(container.read(settingsProvider).splashOptimization, !initialValue);
    });

    test('should change language', () {
      final manager = container.read(settingsProvider.notifier);
      manager.setLanguage('en');
      expect(container.read(settingsProvider).selectedLanguage, 'en');
    });

    test('should change theme', () {
      final manager = container.read(settingsProvider.notifier);
      manager.setTheme('dark');
      expect(container.read(settingsProvider).selectedTheme, 'dark');
    });

    test('should reset settings', () {
      final manager = container.read(settingsProvider.notifier);
      
      // تغيير بعض القيم
      manager.setLanguage('en');
      manager.setTheme('dark');
      
      // إعادة تعيين
      manager.resetSettings();
      expect(container.read(settingsProvider).selectedLanguage, 'ar');
      expect(container.read(settingsProvider).selectedTheme, 'auto');
    });

    test('should get settings summary', () {
      final manager = container.read(settingsProvider.notifier);
      final summary = manager.getSettingsSummary();
      
      expect(summary['performance'], isA<Map>());
      expect(summary['ui'], isA<Map>());
      expect(summary['security'], isA<Map>());
      expect(summary['data'], isA<Map>());
      expect(summary['connectivity'], isA<Map>());
      expect(summary['ux'], isA<Map>());
      expect(summary['build'], isA<Map>());
    });
  });

  group('Settings Integration Tests', () {
    test('should apply performance settings', () {
      final settings = AppSettings();
      settings.removeDebugLogs = true;
      settings.enableImageCompression = true;
      
      // اختبار تطبيق الإعدادات
      expect(settings.removeDebugLogs, true);
      expect(settings.enableImageCompression, true);
    });

    test('should apply security settings', () {
      final settings = AppSettings();
      settings.disableScreenCapture = true;
      settings.enableRootDetection = true;
      settings.enableDatabaseEncryption = true;
      
      expect(settings.disableScreenCapture, true);
      expect(settings.enableRootDetection, true);
      expect(settings.enableDatabaseEncryption, true);
    });

    test('should apply UI settings', () {
      final settings = AppSettings();
      settings.enableRTL = true;
      settings.enableAdaptiveTheme = true;
      settings.selectedLanguage = 'ar';
      
      expect(settings.enableRTL, true);
      expect(settings.enableAdaptiveTheme, true);
      expect(settings.selectedLanguage, 'ar');
    });
  });

  group('Settings Validation Tests', () {
    test('should validate cache expiration days', () {
      final settings = AppSettings();
      
      // القيم الصحيحة
      settings.cacheExpirationDays = 1;
      expect(settings.cacheExpirationDays, 1);
      
      settings.cacheExpirationDays = 30;
      expect(settings.cacheExpirationDays, 30);
      
      // القيم الافتراضية
      settings.resetToDefaults();
      expect(settings.cacheExpirationDays, 7);
    });

    test('should validate sync interval minutes', () {
      final settings = AppSettings();
      
      // القيم الصحيحة
      settings.syncIntervalMinutes = 5;
      expect(settings.syncIntervalMinutes, 5);
      
      settings.syncIntervalMinutes = 120;
      expect(settings.syncIntervalMinutes, 120);
      
      // القيم الافتراضية
      settings.resetToDefaults();
      expect(settings.syncIntervalMinutes, 30);
    });

    test('should validate language codes', () {
      final settings = AppSettings();
      
      settings.selectedLanguage = 'ar';
      expect(settings.selectedLanguage, 'ar');
      
      settings.selectedLanguage = 'en';
      expect(settings.selectedLanguage, 'en');
      
      // إعادة تعيين للقيمة الافتراضية
      settings.resetToDefaults();
      expect(settings.selectedLanguage, 'ar');
    });

    test('should validate theme modes', () {
      final settings = AppSettings();
      
      settings.selectedTheme = 'auto';
      expect(settings.selectedTheme, 'auto');
      
      settings.selectedTheme = 'light';
      expect(settings.selectedTheme, 'light');
      
      settings.selectedTheme = 'dark';
      expect(settings.selectedTheme, 'dark');
      
      // إعادة تعيين للقيمة الافتراضية
      settings.resetToDefaults();
      expect(settings.selectedTheme, 'auto');
    });
  });

  group('Settings Persistence Tests', () {
    test('should save and load settings', () async {
      final settings = AppSettings();
      
      // تغيير بعض القيم
      settings.splashOptimization = false;
      settings.enableRTL = false;
      settings.selectedLanguage = 'en';
      
      // حفظ الإعدادات
      await settings.save();
      
      // إنشاء إعدادات جديدة وتحميلها
      final newSettings = AppSettings();
      await newSettings.load();
      
      // التحقق من القيم المحفوظة
      expect(newSettings.splashOptimization, false);
      expect(newSettings.enableRTL, false);
      expect(newSettings.selectedLanguage, 'en');
    });
  });

  group('Settings Performance Tests', () {
    test('should handle large settings efficiently', () {
      final settings = AppSettings();
      
      // اختبار الأداء مع تغييرات متعددة
      for (int i = 0; i < 100; i++) {
        settings.splashOptimization = i % 2 == 0;
        settings.enableRTL = i % 3 == 0;
        settings.enableAdaptiveTheme = i % 4 == 0;
      }
      
      // التحقق من القيمة النهائية
      expect(settings.splashOptimization, false);
      expect(settings.enableRTL, false);
      expect(settings.enableAdaptiveTheme, false);
    });
  });
}
