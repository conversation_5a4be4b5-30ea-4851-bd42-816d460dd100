import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// فئة مساعدة للتعامل مع أحجام الشاشات المختلفة
class ScreenUtils {
  /// الحصول على أبعاد الشاشة
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// التحقق من كون الشاشة صغيرة (أقل من 600 بكسل)
  static bool isSmallScreen(BuildContext context) {
    return getScreenSize(context).width < 600;
  }

  /// التحقق من كون الشاشة متوسطة (600-900 بكسل)
  static bool isMediumScreen(BuildContext context) {
    final width = getScreenSize(context).width;
    return width >= 600 && width < 900;
  }

  /// التحقق من كون الشاشة كبيرة (أكبر من 900 بكسل)
  static bool isLargeScreen(BuildContext context) {
    return getScreenSize(context).width >= 900;
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة بناءً على حجم الشاشة
  static int getGridCrossAxisCount(BuildContext context) {
    if (isSmallScreen(context)) return 2;
    if (isMediumScreen(context)) return 3;
    return 4;
  }

  /// الحصول على عرض القائمة الجانبية بناءً على حجم الشاشة
  static double getDrawerWidth(BuildContext context) {
    final screenWidth = getScreenSize(context).width;
    if (isSmallScreen(context)) {
      return screenWidth * 0.85;
    }
    return 320.w;
  }

  /// الحصول على حجم الخط المناسب بناءً على حجم الشاشة
  static double getResponsiveFontSize(
    BuildContext context, {
    required double smallSize,
    required double mediumSize,
    required double largeSize,
  }) {
    if (isSmallScreen(context)) return smallSize;
    if (isMediumScreen(context)) return mediumSize;
    return largeSize;
  }

  /// الحصول على المسافات المناسبة بناءً على حجم الشاشة
  static EdgeInsets getResponsivePadding(
    BuildContext context, {
    required EdgeInsets smallPadding,
    required EdgeInsets mediumPadding,
    required EdgeInsets largePadding,
  }) {
    if (isSmallScreen(context)) return smallPadding;
    if (isMediumScreen(context)) return mediumPadding;
    return largePadding;
  }

  /// الحصول على المسافات الأفقية المناسبة
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    if (isSmallScreen(context)) {
      return EdgeInsets.symmetric(horizontal: 12.w);
    }
    return EdgeInsets.symmetric(horizontal: 16.w);
  }

  /// الحصول على المسافات الرأسية المناسبة
  static EdgeInsets getResponsiveVerticalPadding(BuildContext context) {
    if (isSmallScreen(context)) {
      return EdgeInsets.symmetric(vertical: 8.h);
    }
    return EdgeInsets.symmetric(vertical: 12.h);
  }

  /// الحصول على حجم الأيقونة المناسب
  static double getResponsiveIconSize(
    BuildContext context, {
    required double smallSize,
    required double mediumSize,
    required double largeSize,
  }) {
    if (isSmallScreen(context)) return smallSize;
    if (isMediumScreen(context)) return mediumSize;
    return largeSize;
  }

  /// الحصول على نسبة العرض إلى الارتفاع المناسبة للبطاقات
  static double getResponsiveAspectRatio(BuildContext context) {
    if (isSmallScreen(context)) return 0.85;
    if (isMediumScreen(context)) return 0.9;
    return 1.0;
  }

  /// الحصول على المسافات بين العناصر في الشبكة
  static double getResponsiveSpacing(BuildContext context) {
    if (isSmallScreen(context)) return 8.w;
    if (isMediumScreen(context)) return 12.w;
    return 16.w;
  }

  /// التحقق من كون الجهاز في الوضع الأفقي
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// الحصول على ارتفاع آمن للشاشة (بدون شريط الحالة)
  static double getSafeHeight(BuildContext context) {
    return MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.top;
  }

  /// الحصول على عرض آمن للشاشة (بدون شريط التنقل)
  static double getSafeWidth(BuildContext context) {
    return MediaQuery.of(context).size.width -
        MediaQuery.of(context).padding.left -
        MediaQuery.of(context).padding.right;
  }
}
