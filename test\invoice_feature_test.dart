import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('اختبارات أساسية للتطبيق', () {
    testWidgets('اختبار بسيط للتطبيق', (WidgetTester tester) async {
      // اختبار بسيط للتأكد من أن التطبيق يعمل
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Center(child: Text('اختبار التطبيق'))),
        ),
      );

      expect(find.text('اختبار التطبيق'), findsOneWidget);
    });

    testWidgets('اختبار التخطيط المتجاوب', (WidgetTester tester) async {
      // اختبار التخطيط المتجاوب
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LayoutBuilder(
              builder: (context, constraints) {
                if (constraints.maxWidth > 600) {
                  return Row(
                    children: [
                      Expanded(child: Text('شاشة عريضة')),
                      Expanded(child: Text('عمود ثاني')),
                    ],
                  );
                } else {
                  return Column(children: [Text('شاشة ضيقة'), Text('صف ثاني')]);
                }
              },
            ),
          ),
        ),
      );

      expect(find.text('شاشة عريضة'), findsOneWidget);
      expect(find.text('عمود ثاني'), findsOneWidget);
    });

    testWidgets('اختبار دعم RTL', (WidgetTester tester) async {
      // اختبار دعم RTL
      await tester.pumpWidget(
        Directionality(
          textDirection: TextDirection.rtl,
          child: MaterialApp(
            home: Scaffold(body: Center(child: Text('نص عربي'))),
          ),
        ),
      );

      expect(find.text('نص عربي'), findsOneWidget);
    });

    testWidgets('اختبار الألوان والتنسيق', (WidgetTester tester) async {
      // اختبار الألوان والتنسيق
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              color: Colors.blue,
              child: Center(
                child: Text(
                  'نص ملون',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      expect(find.text('نص ملون'), findsOneWidget);
    });

    testWidgets('اختبار الأزرار والتفاعل', (WidgetTester tester) async {
      // اختبار الأزرار والتفاعل
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: ElevatedButton(
                onPressed: () {
                  buttonPressed = true;
                },
                child: Text('زر تجريبي'),
              ),
            ),
          ),
        ),
      );

      expect(find.text('زر تجريبي'), findsOneWidget);

      // النقر على الزر
      await tester.tap(find.text('زر تجريبي'));
      await tester.pumpAndSettle();

      expect(buttonPressed, true);
    });
  });
}
