import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// إصلاح مشكلة الخلفية السوداء في التطبيق
class ThemeFix {
  /// إنشاء مظهر فاتح مخصص مع خلفية بيضاء
  static ThemeData createLightTheme() {
    return ThemeData(
      brightness: Brightness.light,
      scaffoldBackgroundColor: Colors.white,
      cardColor: Colors.white,
      canvasColor: Colors.white,
      dialogBackgroundColor: Colors.white,
      dividerColor: Colors.grey[300],
      colorScheme: const ColorScheme.light(
        background: Colors.white,
        surface: Colors.white,
        onBackground: Colors.black87,
        onSurface: Colors.black87,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
      ),
      drawerTheme: const DrawerThemeData(
        backgroundColor: Colors.white,
      ),
      popupMenuTheme: PopupMenuThemeData(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      snackBarTheme: const SnackBarThemeData(
        backgroundColor: Colors.white,
        contentTextStyle: TextStyle(color: Colors.black87),
      ),
    );
  }

  /// إنشاء مظهر داكن مخصص مع خلفية رمادية فاتحة بدلاً من السوداء
  static ThemeData createDarkTheme() {
    return ThemeData(
      brightness: Brightness.dark,
      scaffoldBackgroundColor: const Color(0xFFF5F5F5), // رمادي فاتح جداً
      cardColor: Colors.white,
      canvasColor: const Color(0xFFF5F5F5),
      dialogBackgroundColor: Colors.white,
      dividerColor: Colors.grey[400],
      colorScheme: const ColorScheme.dark(
        background: Color(0xFFF5F5F5),
        surface: Colors.white,
        onBackground: Colors.black87,
        onSurface: Colors.black87,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFF5F5F5),
        foregroundColor: Colors.black87,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFFF5F5F5),
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
      ),
      drawerTheme: const DrawerThemeData(
        backgroundColor: Color(0xFFF5F5F5),
      ),
      popupMenuTheme: PopupMenuThemeData(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      snackBarTheme: const SnackBarThemeData(
        backgroundColor: Colors.white,
        contentTextStyle: TextStyle(color: Colors.black87),
      ),
    );
  }

  /// تطبيق إصلاح الخلفية على أي widget
  static Widget applyBackgroundFix(Widget child) {
    return Container(
      color: Colors.white,
      child: child,
    );
  }

  /// إنشاء Scaffold مع خلفية بيضاء مضمونة
  static Scaffold createWhiteScaffold({
    required Widget body,
    PreferredSizeWidget? appBar,
    Widget? drawer,
    Widget? bottomNavigationBar,
    Widget? floatingActionButton,
  }) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: appBar,
      body: Container(
        color: Colors.white,
        child: body,
      ),
      drawer: drawer,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
    );
  }
}
