# إصلاح مشكلة عدم ظهور المنتجات في قائمة المنتجات

## المشكلة
المنتجات لا تظهر في قائمة المنتجات كما هو موضح في الصورة، حيث تظهر رسالة "لا توجد منتجات حتى الآن" مع أيقونة مخزون فارغة.

## الأسباب المحتملة
1. **قاعدة البيانات فارغة**: لا توجد منتجات في قاعدة البيانات
2. **مشكلة في الفلاتر**: الفلاتر المطبقة تستبعد جميع المنتجات
3. **مشكلة في تحميل البيانات**: خطأ في جلب المنتجات من قاعدة البيانات
4. **مشكلة في عرض البيانات**: خطأ في عرض المنتجات في الواجهة

## الحلول المطبقة

### 1. تحسين دالة تحميل المنتجات (`_loadProducts`)
- إضافة منطق للتحقق من وجود منتجات
- إضافة منتجات تجريبية تلقائياً إذا لم توجد منتجات
- تحسين رسائل التصحيح للمساعدة في تشخيص المشاكل

```dart
Future<void> _loadProducts() async {
  try {
    setState(() {
      _isLoading = true;
    });

    debugPrint('بدء تحميل المنتجات في شاشة المنتجات...');
    
    // محاولة جلب المنتجات من قاعدة البيانات
    final products = await _productService.getAllProducts();
    debugPrint('تم جلب ${products.length} منتج في شاشة المنتجات');
    
    // إذا لم توجد منتجات، إضافة منتجات تجريبية تلقائياً
    if (products.isEmpty) {
      debugPrint('لا توجد منتجات، إضافة منتجات تجريبية...');
      _addDemoProducts();
      
      // إعادة جلب المنتجات بعد الإضافة
      final updatedProducts = await _productService.getAllProducts();
      debugPrint('تم جلب ${updatedProducts.length} منتج بعد إضافة المنتجات التجريبية');
      
      if (mounted) {
        setState(() {
          _products = updatedProducts;
          _applyFilters();
          _isLoading = false;
        });
      }
    } else {
      if (mounted) {
        setState(() {
          _products = products;
          _applyFilters();
          _isLoading = false;
        });
      }
    }
    
    debugPrint('تم تحديث قائمة المنتجات في شاشة المنتجات');
    debugPrint('عدد المنتجات المفلترة: ${_filteredProducts.length}');
    
  } catch (e) {
    debugPrint('خطأ في تحميل المنتجات: $e');
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل المنتجات: $e');
    }
  }
}
```

### 2. تحسين دالة إضافة المنتجات التجريبية (`_addDemoProducts`)
- إضافة منطق أفضل لإضافة المنتجات التجريبية
- إضافة رسائل نجاح وفشل واضحة
- إعادة تحميل القائمة بعد الإضافة

```dart
void _addDemoProducts() async {
  try {
    debugPrint('بدء إضافة المنتجات التجريبية...');
    
    // إضافة المنتجات التجريبية مباشرة
    final result = await _productService.addDemoProducts();
    
    if (result) {
      debugPrint('تم إضافة المنتجات التجريبية بنجاح');
      
      // تحديث القائمة فوراً
      await _loadProducts();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة المنتجات التجريبية بنجاح',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      debugPrint('فشل في إضافة المنتجات التجريبية');
      if (mounted) {
        _showErrorSnackBar('فشل في إضافة المنتجات التجريبية');
      }
    }
  } catch (e) {
    debugPrint('خطأ في إضافة المنتجات التجريبية: $e');
    if (mounted) {
      _showErrorSnackBar('خطأ في إضافة المنتجات التجريبية: $e');
    }
  }
}
```

### 3. تحسين دالة تطبيق الفلاتر (`_applyFilters`)
- إضافة رسائل تصحيح مفصلة
- تحسين منطق الفلترة
- إضافة معلومات تشخيصية للمنتجات المستبعدة

```dart
void _applyFilters() {
  debugPrint('تطبيق الفلاتر على ${_products.length} منتج');
  
  _filteredProducts = _products.where((product) {
    // فلتر الفئة
    bool matchesCategory =
        _selectedCategory == 'الكل' || product.category == _selectedCategory;
    
    // فلتر الحالة
    bool matchesStatus =
        _selectedStatus == 'الكل' ||
        product.isActive == (_selectedStatus == 'نشط');
    
    // فلتر السعر الأدنى
    bool matchesPrice =
        _minPrice == null ||
        product.getPriceForCustomerType('distributor') >= _minPrice!;
    
    // فلتر السعر الأقصى
    bool matchesMaxPrice =
        _maxPrice == null ||
        product.getPriceForCustomerType('distributor') <= _maxPrice!;
    
    // فلتر المخزون المنخفض
    bool matchesLowStock =
        _showLowStockOnly == false || product.quantity <= product.minQuantity;
    
    // فلتر النشاط - إظهار جميع المنتجات النشطة افتراضياً
    bool matchesActive = !_showActiveOnly || product.isActive;
    
    // فلتر التاريخ
    bool matchesDate =
        _startDate == null || product.createdAt.isAfter(_startDate!);
    bool matchesEndDate =
        _endDate == null || product.createdAt.isBefore(_endDate!);
    
    final matches = matchesCategory &&
        matchesStatus &&
        matchesPrice &&
        matchesMaxPrice &&
        matchesLowStock &&
        matchesActive &&
        matchesDate &&
        matchesEndDate;
    
    if (!matches) {
      debugPrint('تم استبعاد المنتج: ${product.name} - الفئة: $matchesCategory, الحالة: $matchesStatus, السعر: $matchesPrice, النشاط: $matchesActive');
    }
    
    return matches;
  }).toList();
  
  debugPrint('عدد المنتجات بعد التصفية: ${_filteredProducts.length}');
}
```

### 4. تحسين `ProductList` Widget
- إضافة منطق لإضافة منتجات تجريبية تلقائياً
- تحسين عرض الحالة الفارغة
- إضافة زر لإضافة منتجات تجريبية يدوياً

```dart
// في حالة عدم وجود منتجات
if (products.isEmpty) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.inventory_2_outlined,
          size: 64.sp,
          color: AppColors.textSecondary,
        ),
        SizedBox(height: 24.h),
        Text(
          widget.searchQuery.isNotEmpty
              ? 'لا توجد نتائج للبحث'
              : 'لا توجد منتجات حتى الآن',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          widget.searchQuery.isNotEmpty
              ? 'جرب تغيير كلمات البحث'
              : 'ابدأ بإضافة منتج جديد',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(height: 16.h),
        // إضافة زر لإضافة منتجات تجريبية
        if (!widget.searchQuery.isNotEmpty)
          ElevatedButton.icon(
            onPressed: () async {
              try {
                final productService = ProductService();
                await productService.addDemoProducts();
                _loadProducts(); // إعادة تحميل المنتجات
              } catch (e) {
                debugPrint('خطأ في إضافة المنتجات التجريبية: $e');
              }
            },
            icon: Icon(Icons.add_box),
            label: Text(
              'إضافة منتجات تجريبية',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
      ],
    ),
  );
}
```

### 5. تحسين `ProductListRefresher`
- تحسين دالة `getProductsWithRefresh`
- إضافة منطق أفضل لجلب المنتجات
- تحسين رسائل التصحيح

```dart
Future<List<ProductModel>> getProductsWithRefresh() async {
  try {
    debugPrint('بدء جلب المنتجات مع التحديث...');
    
    // محاولة جلب المنتجات مباشرة أولاً
    final products = await _productService.getAllProducts();
    debugPrint('تم جلب ${products.length} منتج مباشرة');
    
    // إذا لم توجد منتجات، محاولة تحديث قاعدة البيانات
    if (products.isEmpty) {
      debugPrint('لا توجد منتجات، محاولة تحديث قاعدة البيانات...');
      await refreshProducts();
      
      // إعادة جلب المنتجات بعد التحديث
      final updatedProducts = await _productService.getAllProducts();
      debugPrint('تم جلب ${updatedProducts.length} منتج بعد التحديث');
      return updatedProducts;
    }
    
    return products;
  } catch (e) {
    debugPrint('خطأ في جلب المنتجات مع التحديث: $e');
    debugPrint('تفاصيل الخطأ: ${e.toString()}');
    return [];
  }
}
```

### 6. تحسين خدمة قاعدة البيانات
- إضافة رسائل تصحيح مفصلة في `getAllProducts`
- طباعة تفاصيل المنتجات للمساعدة في التصحيح
- تحسين معالجة الأخطاء

```dart
Future<List<ProductModel>> getAllProducts() async {
  final db = await database;

  try {
    debugPrint('بدء جلب جميع المنتجات...');

    // فحص وجود العمود isActive
    final columns = await db.rawQuery("PRAGMA table_info(products)");
    final hasIsActiveColumn = columns.any((col) => col['name'] == 'isActive');

    debugPrint('عمود isActive موجود: $hasIsActiveColumn');

    String query;
    List<dynamic> whereArgs = [];

    if (hasIsActiveColumn) {
      query = 'SELECT * FROM products WHERE isActive = 1 ORDER BY name ASC';
      debugPrint('استخدام استعلام مع فلتر isActive');
    } else {
      query = 'SELECT * FROM products ORDER BY name ASC';
      debugPrint('استخدام استعلام بدون فلتر isActive');
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      query,
      whereArgs,
    );
    debugPrint('تم جلب ${maps.length} منتج من قاعدة البيانات');

    // طباعة تفاصيل المنتجات للمساعدة في التصحيح
    for (int i = 0; i < maps.length; i++) {
      final product = maps[i];
      debugPrint('المنتج ${i + 1}: ${product['name']} - isActive: ${product['isActive']}');
    }

    final products = List.generate(maps.length, (i) {
      try {
        return ProductModel.fromMap(maps[i]);
      } catch (e) {
        debugPrint('خطأ في تحويل المنتج ${i}: $e');
        debugPrint('بيانات المنتج: ${maps[i]}');
        rethrow;
      }
    });

    debugPrint('تم تحويل جميع المنتجات بنجاح');
    return products;
  } catch (e) {
    debugPrint('خطأ في جلب المنتجات: $e');
    debugPrint('تفاصيل الخطأ: ${e.toString()}');

    // في حالة الخطأ، حاول جلب جميع المنتجات بدون فلترة
    try {
      debugPrint('محاولة جلب المنتجات بدون فلترة...');
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        orderBy: 'name ASC',
      );
      debugPrint('تم جلب ${maps.length} منتج بدون فلترة');

      final products = List.generate(
        maps.length,
        (i) => ProductModel.fromMap(maps[i]),
      );
      return products;
    } catch (fallbackError) {
      debugPrint('فشل في جلب المنتجات بدون فلترة: $fallbackError');
      rethrow;
    }
  }
}
```

## ملفات الاختبار
تم إنشاء ملف اختبار شامل `test/products_fix_test.dart` لفحص:
- اتصال قاعدة البيانات
- إضافة المنتجات التجريبية
- إنشاء منتج تجريبي واحد
- تصفية المنتجات
- هيكل قاعدة البيانات

## كيفية التشخيص
1. **فحص سجلات التصحيح**: ابحث عن رسائل `debugPrint` في وحدة التحكم
2. **تشغيل الاختبارات**: شغل ملف `test/products_fix_test.dart`
3. **فحص قاعدة البيانات**: استخدم أزرار الفحص في شاشة المنتجات
4. **إضافة منتجات تجريبية**: استخدم الزر المخصص لإضافة منتجات تجريبية

## النتائج المتوقعة
بعد تطبيق هذه الحلول:
- ستظهر المنتجات في قائمة المنتجات
- سيتم إضافة منتجات تجريبية تلقائياً إذا لم توجد منتجات
- ستتحسن رسائل التصحيح للمساعدة في تشخيص المشاكل
- سيكون هناك زر لإضافة منتجات تجريبية يدوياً

## ملاحظات إضافية
- تأكد من أن قاعدة البيانات تعمل بشكل صحيح
- تحقق من أن جميع الأعمدة المطلوبة موجودة في جدول المنتجات
- تأكد من أن المنتجات لديها `isActive = 1` لعرضها
- تحقق من أن الفلاتر لا تستبعد جميع المنتجات
