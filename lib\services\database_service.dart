import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';

import '../models/customer_model.dart';
import '../models/invoice_model.dart';
import '../models/invoice_action_model.dart';
import 'package:flutter/foundation.dart'; // Added for debugPrint

class SavedCredentials {
  final String phone;
  final String password;
  SavedCredentials({required this.phone, required this.password});
}

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'atlas_medical.db');
    return await openDatabase(
      path,
      version: 17, // إصدار قاعدة البيانات - تم إضافة جدول التحصيلات
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// فحص وإصلاح قاعدة البيانات عند بدء التطبيق
  Future<void> checkAndFixDatabase() async {
    try {
      final db = await database;
      print('فحص قاعدة البيانات...');

      // التحقق من وجود جدول الفواتير
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='invoices'",
      );

      if (tables.isEmpty) {
        print('جدول الفواتير غير موجود، إنشاؤه...');
        await _onCreate(db, 14);
      } else {
        print('جدول الفواتير موجود، فحص الأعمدة...');
        // التأكد من وجود جميع الأعمدة المطلوبة
        await _ensureInvoiceColumnsExist(db);
      }

      print('تم فحص وإصلاح قاعدة البيانات بنجاح');
    } catch (e) {
      print('خطأ في فحص وإصلاح قاعدة البيانات: $e');
      // إذا فشل الفحص، حاول إجبار الترقية
      try {
        await forceDatabaseUpgrade();
      } catch (upgradeError) {
        print('فشل في إجبار الترقية: $upgradeError');
      }
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول المستخدمين
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        phone TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        email TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        lastLogin TEXT,
        permissions TEXT
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        code TEXT,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        minQuantity INTEGER NOT NULL DEFAULT 0,
        unit TEXT NOT NULL,
        piecesPerCarton INTEGER,
        barcode TEXT,
        image TEXT,
        imageUrl TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        createdBy TEXT,
        distributorPrice REAL DEFAULT 0,
        officePrice REAL DEFAULT 0
      )
    ''');

    // جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone1 TEXT,
        phone2 TEXT,
        email TEXT,
        address TEXT,
        street TEXT,
        building TEXT,
        floor TEXT,
        apartment TEXT,
        landmark TEXT,
        village TEXT,
        city TEXT,
        governorate TEXT,
        postalCode TEXT,
        latitude REAL,
        longitude REAL,
        taxNumber TEXT,
        type TEXT,
        activity TEXT,
        balance REAL DEFAULT 0,
        lastOrderDate TEXT,
        notes TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        createdBy TEXT
      )
    ''');

    // جدول الفواتير
    await db.execute('''
      CREATE TABLE invoices (
        id TEXT PRIMARY KEY,
        invoiceNumber TEXT UNIQUE NOT NULL,
        customerId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        invoiceDate TEXT NOT NULL,
        items TEXT,
        subtotal REAL NOT NULL DEFAULT 0,
        discountAmount REAL NOT NULL DEFAULT 0,
        discountPercentage REAL NOT NULL DEFAULT 0,
        total REAL NOT NULL DEFAULT 0,
        paidAmount REAL NOT NULL DEFAULT 0,
        status INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        createdBy TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // جدول إجراءات الفواتير
    await db.execute('''
      CREATE TABLE invoice_actions (
        id TEXT PRIMARY KEY,
        invoiceId TEXT NOT NULL,
        invoiceNumber TEXT NOT NULL,
        customerId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        governorate TEXT NOT NULL,
        actionType INTEGER NOT NULL,
        amount REAL NOT NULL,
        actionDate TEXT NOT NULL,
        notes TEXT,
        createdBy TEXT,
        FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // جدول المبيعات
    await db.execute('''
      CREATE TABLE sales (
        id TEXT PRIMARY KEY,
        invoiceId TEXT NOT NULL,
        invoiceNumber TEXT NOT NULL,
        customerId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        invoiceDate TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        paidAmount REAL NOT NULL,
        remainingAmount REAL NOT NULL,
        status INTEGER NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // جدول التحصيلات
    await db.execute('''
      CREATE TABLE collections (
        id TEXT PRIMARY KEY,
        invoiceId TEXT NOT NULL,
        invoiceNumber TEXT NOT NULL,
        customerId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        invoiceDate TEXT NOT NULL,
        invoiceTotal REAL NOT NULL,
        previousPaidAmount REAL NOT NULL,
        collectionAmount REAL NOT NULL,
        remainingAmount REAL NOT NULL,
        collectionDate TEXT NOT NULL,
        notes TEXT,
        createdBy TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // إدخال بيانات تجريبية (مستخدمين + منتجات فقط)
    await _insertDemoData(db);

    // جدول بيانات الاعتماد المحفوظة (يُنشأ دائمًا مع قاعدة جديدة)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS saved_credentials (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        phone TEXT NOT NULL,
        password TEXT NOT NULL
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('بدء ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion');

    if (oldVersion < 2) {
      print('ترقية إلى الإصدار 2...');
      // إضافة أعمدة الإصدار 2
      await db.execute('ALTER TABLE products ADD COLUMN code TEXT');
      await db.execute(
        'ALTER TABLE products ADD COLUMN piecesPerCarton INTEGER',
      );
      await db.execute('ALTER TABLE products ADD COLUMN image TEXT');
      await db.execute('ALTER TABLE products ADD COLUMN createdBy TEXT');

      await db.execute('ALTER TABLE customers ADD COLUMN phone1 TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN phone2 TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN street TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN building TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN floor TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN apartment TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN landmark TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN village TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN latitude REAL');
      await db.execute('ALTER TABLE customers ADD COLUMN longitude REAL');
      await db.execute('ALTER TABLE customers ADD COLUMN type TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN activity TEXT');
      await db.execute(
        'ALTER TABLE customers ADD COLUMN balance REAL DEFAULT 0',
      );
      await db.execute('ALTER TABLE customers ADD COLUMN lastOrderDate TEXT');
      await db.execute('ALTER TABLE customers ADD COLUMN createdBy TEXT');
      print('تم الانتهاء من الترقية إلى الإصدار 2');
    }

    if (oldVersion < 3) {
      // تفريغ جدول العملاء
      await db.delete('customers');
    }

    if (oldVersion < 4) {
      // إنشاء جدول بيانات الاعتماد المحفوظة
      await db.execute('''
        CREATE TABLE IF NOT EXISTS saved_credentials (
          id INTEGER PRIMARY KEY CHECK (id = 1),
          phone TEXT NOT NULL,
          password TEXT NOT NULL
        )
      ''');
      // إصلاح سجلات العملاء ذات المعرّف الفارغ/الـ NULL لمنع تعارضات المفتاح الأساسي
      await db.execute('''
        UPDATE customers
        SET id = 'cust_' || CAST(strftime('%s','now') AS TEXT) || '_' || rowid
        WHERE (id IS NULL OR id = '')
      ''');
      // إصلاح سجلات المنتجات ذات المعرّف الفارغ/الـ NULL لمنع تعارضات المفتاح الأساسي
      await db.execute('''
        UPDATE products
        SET id = 'prod_' || CAST(strftime('%s','now') AS TEXT) || '_' || rowid
        WHERE (id IS NULL OR id = '')
      ''');
    }

    if (oldVersion < 5) {
      // إصلاح مشاكل الإصدار 5
      // إزالة عمود phone القديم من جدول العملاء إذا كان موجوداً
      try {
        await db.execute('ALTER TABLE customers DROP COLUMN phone');
      } catch (e) {
        // العمود غير موجود، تجاهل الخطأ
      }

      // إصلاح البيانات الموجودة
      await db.execute('''
        UPDATE customers
        SET type = 'medicalOfficeA'
        WHERE type IS NULL OR type = ''
      ''');

      await db.execute('''
        UPDATE customers
        SET activity = ''
        WHERE activity IS NULL
      ''');

      await db.execute('''
        UPDATE customers
        SET village = ''
        WHERE village IS NULL
      ''');
    }

    if (oldVersion < 6) {
      // إضافة أعمدة الأسعار المختلفة للمنتجات
      try {
        await db.execute(
          'ALTER TABLE products ADD COLUMN distributorPrice REAL DEFAULT 0',
        );
        await db.execute(
          'ALTER TABLE products ADD COLUMN medicalOfficeAPrice REAL DEFAULT 0',
        );
        await db.execute(
          'ALTER TABLE products ADD COLUMN medicalOfficeBPrice REAL DEFAULT 0',
        );
        await db.execute(
          'ALTER TABLE products ADD COLUMN majorClientPrice REAL DEFAULT 0',
        );

        // تحديث الأسعار الموجودة لتكون مساوية للسعر الأساسي
        await db.execute('''
          UPDATE products 
          SET distributorPrice = price * 0.8,
              medicalOfficeAPrice = price * 0.9,
              medicalOfficeBPrice = price * 0.95,
              majorClientPrice = price * 0.85
          WHERE distributorPrice = 0 OR distributorPrice IS NULL
        ''');
      } catch (e) {
        print('خطأ في إضافة أعمدة الأسعار: $e');
      }
    }

    if (oldVersion < 9) {
      // تحديث نظام الأسعار ليكون موزع ومكتب فقط
      try {
        // إضافة عمود officePrice الجديد
        await db.execute(
          'ALTER TABLE products ADD COLUMN officePrice REAL DEFAULT 0',
        );

        // تحديث البيانات الموجودة - نقل سعر المكتب الطبي أ إلى officePrice
        await db.execute('''
          UPDATE products 
          SET officePrice = medicalOfficeAPrice
          WHERE medicalOfficeAPrice > 0
        ''');

        // إذا لم يكن هناك سعر مكتب طبي أ، استخدم السعر الأساسي
        await db.execute('''
          UPDATE products 
          SET officePrice = price * 0.9
          WHERE officePrice = 0 OR officePrice IS NULL
        ''');

        // حذف الأعمدة القديمة (اختياري - يمكن الاحتفاظ بها للتوافق مع الإصدارات القديمة)
        // await db.execute('ALTER TABLE products DROP COLUMN medicalOfficeAPrice');
        // await db.execute('ALTER TABLE products DROP COLUMN medicalOfficeBPrice');
        // await db.execute('ALTER TABLE products DROP COLUMN majorClientPrice');

        print('تم تحديث نظام الأسعار بنجاح');
      } catch (e) {
        print('خطأ في تحديث نظام الأسعار: $e');
      }
    }

    if (oldVersion < 10) {
      print('ترقية إلى الإصدار 10...');
      // إضافة جداول الفواتير
      print('تم الانتهاء من الترقية إلى الإصدار 10');
    }

    if (oldVersion < 11) {
      print('ترقية إلى الإصدار 11...');
      // إنشاء جدول الفواتير
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS invoices (
            id TEXT PRIMARY KEY,
            invoiceNumber TEXT UNIQUE NOT NULL,
            customerId TEXT NOT NULL,
            customerName TEXT NOT NULL,
            customerPhone TEXT DEFAULT "",
            invoiceDate TEXT NOT NULL,
            items TEXT,
            subtotal REAL NOT NULL DEFAULT 0,
            discountAmount REAL NOT NULL DEFAULT 0,
            discountPercentage REAL NOT NULL DEFAULT 0,
            total REAL NOT NULL DEFAULT 0,
            paidAmount REAL NOT NULL DEFAULT 0,
            status INTEGER NOT NULL DEFAULT 0,
            notes TEXT,
            createdBy TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
          )
        ''');
        print('تم إنشاء جدول الفواتير بنجاح');
      } catch (e) {
        print('خطأ في إنشاء جدول الفواتير: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 11');
    }

    if (oldVersion < 12) {
      print('ترقية إلى الإصدار 12...');
      // إضافة أعمدة الخصم للفواتير
      try {
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN discountAmount REAL DEFAULT 0',
        );
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN discountPercentage REAL DEFAULT 0',
        );
        print('تم إضافة أعمدة الخصم بنجاح');
      } catch (e) {
        print('خطأ في إضافة أعمدة الخصم: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 12');
    }

    if (oldVersion < 13) {
      print('ترقية إلى الإصدار 13...');
      // إضافة عمود المبلغ المدفوع للفواتير
      try {
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN paidAmount REAL DEFAULT 0',
        );
        print('تم إضافة عمود المبلغ المدفوع بنجاح');
      } catch (e) {
        print('خطأ في إضافة عمود المبلغ المدفوع: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 13');
    }

    if (oldVersion < 14) {
      print('ترقية إلى الإصدار 14...');
      // التأكد من وجود عمود رقم هاتف العميل
      try {
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN customerPhone TEXT DEFAULT ""',
        );
        print('تم إضافة عمود رقم هاتف العميل بنجاح');
      } catch (e) {
        print('خطأ في إضافة عمود رقم هاتف العميل: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 14');
    }

    if (oldVersion < 15) {
      print('ترقية إلى الإصدار 15...');
      // إنشاء جدول إجراءات الفواتير
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS invoice_actions (
            id TEXT PRIMARY KEY,
            invoiceId TEXT NOT NULL,
            invoiceNumber TEXT NOT NULL,
            customerId TEXT NOT NULL,
            customerName TEXT NOT NULL,
            governorate TEXT NOT NULL,
            actionType INTEGER NOT NULL,
            amount REAL NOT NULL,
            actionDate TEXT NOT NULL,
            notes TEXT,
            createdBy TEXT,
            FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
          )
        ''');
        print('تم إنشاء جدول إجراءات الفواتير بنجاح');
      } catch (e) {
        print('خطأ في إنشاء جدول إجراءات الفواتير: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 15');
    }

    if (oldVersion < 16) {
      print('ترقية إلى الإصدار 16...');
      // إنشاء جدول المبيعات
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS sales (
            id TEXT PRIMARY KEY,
            invoiceId TEXT NOT NULL,
            invoiceNumber TEXT NOT NULL,
            customerId TEXT NOT NULL,
            customerName TEXT NOT NULL,
            customerPhone TEXT,
            invoiceDate TEXT NOT NULL,
            totalAmount REAL NOT NULL,
            paidAmount REAL NOT NULL,
            remainingAmount REAL NOT NULL,
            status INTEGER NOT NULL,
            notes TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
          )
        ''');
        print('تم إنشاء جدول المبيعات بنجاح');
      } catch (e) {
        print('خطأ في إنشاء جدول المبيعات: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 16');
    }

    if (oldVersion < 17) {
      print('ترقية إلى الإصدار 17...');
      // إنشاء جدول التحصيلات
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS collections (
            id TEXT PRIMARY KEY,
            invoiceId TEXT NOT NULL,
            invoiceNumber TEXT NOT NULL,
            customerId TEXT NOT NULL,
            customerName TEXT NOT NULL,
            customerPhone TEXT,
            invoiceDate TEXT NOT NULL,
            invoiceTotal REAL NOT NULL,
            previousPaidAmount REAL NOT NULL,
            collectionAmount REAL NOT NULL,
            remainingAmount REAL NOT NULL,
            collectionDate TEXT NOT NULL,
            notes TEXT,
            createdBy TEXT,
            createdAt TEXT NOT NULL,
            FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
          )
        ''');
        print('تم إنشاء جدول التحصيلات بنجاح');
      } catch (e) {
        print('خطأ في إنشاء جدول التحصيلات: $e');
      }
      print('تم الانتهاء من الترقية إلى الإصدار 17');
    }

    print('تم الانتهاء من ترقية قاعدة البيانات بنجاح');
  }

  Future<void> _insertDemoData(Database db) async {
    // مستخدم تجريبي
    await db.insert('users', {
      'id': '1',
      'phone': '01125312343',
      'password': '123456',
      'name': 'MOHAMED FAYED',
      'role': 'admin',
      'email': '<EMAIL>',
      'isActive': 1,
      'createdAt': DateTime.now().toIso8601String(),
      'lastLogin': DateTime.now().toIso8601String(),
      'permissions': '{}',
    });

    // منتجات تجريبية
    await db.insert('products', {
      'id': '1',
      'name': 'قفازات طبية',
      'description': 'قفازات طبية لاتكس',
      'category': 'معدات حماية',
      'code': 'GLOVE001',
      'price': 25.0,
      'cost': 15.0,
      'quantity': 100,
      'minQuantity': 10,
      'unit': 'علبة',
      'piecesPerCarton': 50,
      'barcode': '123456789',
      'isActive': 1,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'createdBy': '1',
      'distributorPrice': 20.0,
      'officePrice': 22.0,
    });

    await db.insert('products', {
      'id': '2',
      'name': 'كمامات طبية',
      'description': 'كمامات طبية 3 طبقات',
      'category': 'معدات حماية',
      'code': 'MASK001',
      'price': 50.0,
      'cost': 30.0,
      'quantity': 200,
      'minQuantity': 20,
      'unit': 'علبة',
      'piecesPerCarton': 100,
      'barcode': '987654321',
      'isActive': 1,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'createdBy': '1',
      'distributorPrice': 40.0,
      'officePrice': 45.0,
    });

    // لا نقوم بإدخال عملاء أو فواتير تجريبية لضمان أن قاعدة العملاء فارغة
  }

  // عمليات المستخدمين
  Future<UserModel?> getUserByPhone(String phone) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'phone = ? AND isActive = 1',
      whereArgs: [phone],
    );

    if (maps.isEmpty) return null;

    return UserModel.fromMap(maps.first);
  }

  /// تحقق من كلمة المرور للمستخدم بالهاتف
  Future<bool> verifyUserPassword(String phone, String password) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'phone = ? AND password = ? AND isActive = 1',
      whereArgs: [phone, password],
      columns: ['id'],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  /// تحديث كلمة المرور للمستخدم عن طريق رقم الهاتف
  Future<int> updateUserPasswordByPhone(
    String phone,
    String newPassword,
  ) async {
    final db = await database;
    return db.update(
      'users',
      {'password': newPassword, 'lastLogin': DateTime.now().toIso8601String()},
      where: 'phone = ? AND isActive = 1',
      whereArgs: [phone],
    );
  }

  Future<void> updateUserLastLogin(String userId) async {
    final db = await database;
    await db.update(
      'users',
      {'lastLogin': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  // عمليات المنتجات

  /// فحص وإصلاح قاعدة البيانات
  Future<void> checkDatabase() async {
    try {
      debugPrint('🔧 بدء فحص قاعدة البيانات...');
      final db = await database;

      // فحص وجود الجداول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );
      debugPrint(
        '📋 الجداول الموجودة: ${tables.map((t) => t['name']).toList()}',
      );

      // فحص وجود جدول المنتجات
      final hasProductsTable = tables.any((t) => t['name'] == 'products');
      if (!hasProductsTable) {
        debugPrint('❌ جدول المنتجات غير موجود، إنشاؤه...');
        await _onCreate(db, 17);
      }

      // فحص وجود الأعمدة المطلوبة
      if (hasProductsTable) {
        final columns = await db.rawQuery("PRAGMA table_info(products)");
        final columnNames = columns.map((c) => c['name'] as String).toList();
        debugPrint('📊 أعمدة جدول المنتجات: $columnNames');

        // فحص وجود العمود isActive
        if (!columnNames.contains('isActive')) {
          debugPrint('⚠️ عمود isActive غير موجود، إضافته...');
          await db.execute(
            'ALTER TABLE products ADD COLUMN isActive INTEGER DEFAULT 1',
          );
        }
      }

      debugPrint('✅ تم فحص قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في فحص قاعدة البيانات: $e');
      rethrow;
    }
  }

  // عمليات العملاء
  Future<List<CustomerModel>> getAllCustomers() async {
    final db = await database;

    try {
      // فحص وجود العمود isActive
      final columns = await db.rawQuery("PRAGMA table_info(customers)");
      final hasIsActiveColumn = columns.any((col) => col['name'] == 'isActive');

      String query;
      List<dynamic> whereArgs = [];

      if (hasIsActiveColumn) {
        query = 'SELECT * FROM customers WHERE isActive = 1 ORDER BY name ASC';
      } else {
        query = 'SELECT * FROM customers ORDER BY name ASC';
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        query,
        whereArgs,
      );
      return List.generate(maps.length, (i) => CustomerModel.fromMap(maps[i]));
    } catch (e) {
      print('خطأ في جلب العملاء: $e');
      // في حالة الخطأ، حاول جلب جميع العملاء بدون فلترة
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'name ASC',
      );
      return List.generate(maps.length, (i) => CustomerModel.fromMap(maps[i]));
    }
  }

  Future<CustomerModel?> getCustomerById(String id) async {
    final db = await database;

    try {
      // فحص وجود العمود isActive
      final columns = await db.rawQuery("PRAGMA table_info(customers)");
      final hasIsActiveColumn = columns.any((col) => col['name'] == 'isActive');

      String query;
      List<dynamic> whereArgs = [id];

      if (hasIsActiveColumn) {
        query = 'SELECT * FROM customers WHERE id = ? AND isActive = 1';
      } else {
        query = 'SELECT * FROM customers WHERE id = ?';
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        query,
        whereArgs,
      );
      if (maps.isEmpty) return null;
      return CustomerModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في جلب العميل: $e');
      // في حالة الخطأ، حاول جلب العميل بدون فلترة
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );
      if (maps.isEmpty) return null;
      return CustomerModel.fromMap(maps.first);
    }
  }

  // فحص وإصلاح قاعدة البيانات
  Future<void> checkAndRepairDatabase() async {
    final db = await database;
    try {
      print('فحص قاعدة البيانات...');

      // فحص وجود الجداول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );
      print('الجداول الموجودة: ${tables.map((e) => e['name']).toList()}');

      // فحص جدول العملاء
      if (tables.any((table) => table['name'] == 'customers')) {
        final customerColumns = await db.rawQuery(
          "PRAGMA table_info(customers)",
        );
        print(
          'أعمدة جدول العملاء: ${customerColumns.map((e) => e['name']).toList()}',
        );

        // فحص وإضافة العمود isActive إذا كان مفقوداً
        final hasIsActiveColumn = customerColumns.any(
          (col) => col['name'] == 'isActive',
        );
        if (!hasIsActiveColumn) {
          print('إضافة العمود isActive إلى جدول العملاء...');
          await db.execute(
            'ALTER TABLE customers ADD COLUMN isActive INTEGER NOT NULL DEFAULT 1',
          );
          print('تم إضافة العمود isActive بنجاح');
        }

        // فحص عدد العملاء
        final customerCount = await db.rawQuery(
          "SELECT COUNT(*) as count FROM customers",
        );
        print('عدد العملاء: ${customerCount.first['count']}');

        // فحص آخر 5 عملاء
        final recentCustomers = await db.rawQuery(
          "SELECT id, name, phone1 FROM customers ORDER BY createdAt DESC LIMIT 5",
        );
        print(
          'آخر 5 عملاء: ${recentCustomers.map((e) => '${e['name']} (${e['phone1']})').toList()}',
        );
      } else {
        print('تحذير: جدول العملاء غير موجود!');
      }

      // فحص جدول المنتجات
      if (tables.any((table) => table['name'] == 'products')) {
        final productColumns = await db.rawQuery("PRAGMA table_info(products)");
        print(
          'أعمدة جدول المنتجات: ${productColumns.map((e) => e['name']).toList()}',
        );

        // فحص وإضافة العمود isActive إذا كان مفقوداً
        final hasIsActiveColumn = productColumns.any(
          (col) => col['name'] == 'isActive',
        );
        if (!hasIsActiveColumn) {
          print('إضافة العمود isActive إلى جدول المنتجات...');
          await db.execute(
            'ALTER TABLE products ADD COLUMN isActive INTEGER NOT NULL DEFAULT 1',
          );
          print('تم إضافة العمود isActive بنجاح');
        }

        // فحص عدد المنتجات
        final productCount = await db.rawQuery(
          "SELECT COUNT(*) as count FROM products",
        );
        print('عدد المنتجات: ${productCount.first['count']}');

        // فحص آخر 5 منتجات
        final recentProducts = await db.rawQuery(
          "SELECT id, name, code FROM products ORDER BY createdAt DESC LIMIT 5",
        );
        print(
          'آخر 5 منتجات: ${recentProducts.map((e) => '${e['name']} (${e['code']})').toList()}',
        );
      } else {
        print('تحذير: جدول المنتجات غير موجود!');
      }

      // فحص إعدادات قاعدة البيانات
      final pragmaUserVersion = await db.rawQuery("PRAGMA user_version");
      print('إصدار قاعدة البيانات: ${pragmaUserVersion.first['user_version']}');
    } catch (e) {
      print('خطأ في فحص قاعدة البيانات: $e');
      print('تفاصيل الخطأ: ${e.toString()}');
    }
  }

  // إنشاء عميل تجريبي لاختبار قاعدة البيانات
  Future<bool> createTestCustomer() async {
    try {
      print('إنشاء عميل تجريبي لاختبار قاعدة البيانات...');

      final testCustomer = CustomerModel(
        id: '',
        name: 'عميل تجريبي',
        type: CustomerType.medicalOfficeA,
        activity: 'اختبار',
        address: 'عنوان تجريبي',
        governorate: 'القاهرة',
        city: 'القاهرة',
        street: 'شارع تجريبي',
        phone1: '01000000000',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'test',
      );

      await insertCustomer(testCustomer);
      print('تم إنشاء العميل التجريبي بنجاح');
      return true;
    } catch (e) {
      print('فشل في إنشاء العميل التجريبي: $e');
      return false;
    }
  }

  // إنشاء منتج تجريبي لاختبار قاعدة البيانات

  Future<void> insertCustomer(CustomerModel customer) async {
    final db = await database;

    try {
      // توليد معرف فريد إذا كان فارغاً
      final String ensuredId = customer.id.isEmpty
          ? _generateId(prefix: 'cust')
          : customer.id;

      final toInsert = customer.id.isEmpty
          ? customer.copyWith(id: ensuredId)
          : customer;

      // التحقق السريع من البيانات الأساسية
      if (toInsert.name.trim().isEmpty ||
          toInsert.phone1?.trim().isEmpty == true) {
        throw Exception('الاسم ورقم الهاتف مطلوبان');
      }

      // تنظيف البيانات الأساسية فقط
      final cleanedCustomer = toInsert.copyWith(
        name: toInsert.name.trim(),
        phone1: toInsert.phone1?.trim(),
        governorate: toInsert.governorate?.trim(),
        city: toInsert.city?.trim(),
        activity: toInsert.activity.trim().isEmpty
            ? ''
            : toInsert.activity.trim(),
      );

      // إدخال العميل مباشرة
      await db.insert('customers', cleanedCustomer.toMap());
    } catch (e) {
      if (e.toString().contains('UNIQUE constraint failed')) {
        throw Exception('رقم الهاتف مستخدم بالفعل');
      }
      throw Exception('فشل في حفظ العميل: $e');
    }
  }

  Future<void> updateCustomer(CustomerModel customer) async {
    final db = await database;
    await db.update(
      'customers',
      customer.toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  Future<void> deleteCustomer(String id) async {
    final db = await database;
    await db.delete('customers', where: 'id = ?', whereArgs: [id]);
  }

  // تفريغ جميع العملاء
  Future<void> clearCustomers() async {
    final db = await database;
    await db.delete('customers');
  }

  // إحصائيات لوحة التحكم
  Future<Map<String, dynamic>> getDashboardStats() async {
    final db = await database;

    final customerCountResult = await db.rawQuery(
      'SELECT COUNT(*) FROM customers WHERE isActive = 1',
    );
    final customerCount = (customerCountResult.first['COUNT(*)'] as int?) ?? 0;

    return {'customerCount': customerCount};
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  // =====================
  // Saved credentials API
  // =====================
  Future<void> saveCredentials({
    required String phone,
    required String password,
  }) async {
    final db = await database;
    await db.transaction((txn) async {
      await txn.delete('saved_credentials');
      await txn.insert('saved_credentials', {
        'id': 1,
        'phone': phone,
        'password': password,
      });
    });
  }

  Future<SavedCredentials?> getSavedCredentials() async {
    final db = await database;
    final rows = await db.query('saved_credentials', where: 'id = 1', limit: 1);
    if (rows.isEmpty) return null;
    final row = rows.first;
    final phone = row['phone'] as String?;
    final password = row['password'] as String?;
    if (phone == null || password == null) return null;
    return SavedCredentials(phone: phone, password: password);
  }

  Future<void> clearSavedCredentials() async {
    final db = await database;
    await db.delete('saved_credentials');
  }

  // =====================
  // Helpers
  // =====================
  String _generateId({String prefix = 'id'}) {
    final int ts = DateTime.now().microsecondsSinceEpoch;
    final String uuid = const Uuid().v4().substring(0, 8);
    return '${prefix}_${ts}_$uuid';
  }

  // =====================
  // Database Management
  // =====================

  /// حذف جميع البيانات من قاعدة البيانات (إعادة تعيين كاملة)
  Future<void> clearAllData() async {
    final db = await database;

    try {
      await db.transaction((txn) async {
        // حذف جميع البيانات من جميع الجداول
        await txn.delete('customers');

        // لا نحذف المستخدمين أو بيانات الاعتماد المحفوظة
        // لحماية بيانات النظام الأساسية

        print('تم حذف جميع البيانات بنجاح');
      });
    } catch (e) {
      print('خطأ في حذف البيانات: $e');
      rethrow;
    }
  }

  /// حذف جميع العملاء فقط
  Future<void> clearAllCustomers() async {
    final db = await database;

    try {
      await db.delete('customers');
      print('تم حذف جميع العملاء بنجاح');
    } catch (e) {
      print('خطأ في حذف العملاء: $e');
      rethrow;
    }
  }

  // =====================
  // Invoice Management
  // =====================

  /// إنشاء فاتورة جديدة
  Future<InvoiceModel> createInvoice(InvoiceModel invoice) async {
    final db = await database;
    final id = _generateId(prefix: 'inv');

    final newInvoice = invoice.copyWith(
      id: id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // التأكد من وجود جميع الأعمدة المطلوبة
    await _ensureInvoiceColumnsExist(db);

    // طباعة البيانات التي سيتم إدخالها للمساعدة في التصحيح
    final invoiceMap = newInvoice.toMap();
    print('بيانات الفاتورة للإدخال:');
    invoiceMap.forEach((key, value) {
      print('  $key: $value');
    });

    try {
      await db.insert('invoices', invoiceMap);
      print('تم إدخال الفاتورة بنجاح');
    } catch (e) {
      print('خطأ في إدخال الفاتورة: $e');

      // إذا كان الخطأ يتعلق بعمود customerPhone، حاول إصلاح المشكلة
      if (e.toString().contains('customerPhone') ||
          e.toString().contains('no column named')) {
        print('مشكلة في عمود customerPhone، محاولة إصلاح...');
        try {
          // إجبار ترقية قاعدة البيانات
          await forceDatabaseUpgrade();
          // إعادة المحاولة بعد الترقية
          await db.insert('invoices', invoiceMap);
          print('تم إدخال الفاتورة بنجاح بعد الإصلاح');
        } catch (upgradeError) {
          print('فشل في إصلاح قاعدة البيانات: $upgradeError');
          // إذا فشل الإصلاح، حاول إعادة إنشاء الجدول
          try {
            await _recreateInvoicesTable(db);
            await db.insert('invoices', invoiceMap);
            print('تم إدخال الفاتورة بنجاح بعد إعادة إنشاء الجدول');
          } catch (recreateError) {
            print('فشل في إعادة إنشاء الجدول: $recreateError');
            rethrow;
          }
        }
      } else {
        // طباعة مخطط الجدول للمساعدة في التصحيح
        await _debugInvoiceTableSchema();
        rethrow;
      }
    }

    return newInvoice;
  }

  /// تحديث فاتورة موجودة
  Future<InvoiceModel> updateInvoice(InvoiceModel invoice) async {
    final db = await database;

    final updatedInvoice = invoice.copyWith(updatedAt: DateTime.now());

    // التأكد من وجود جميع الأعمدة المطلوبة
    await _ensureInvoiceColumnsExist(db);

    await db.update(
      'invoices',
      updatedInvoice.toMap(),
      where: 'id = ?',
      whereArgs: [invoice.id],
    );
    return updatedInvoice;
  }

  /// حذف فاتورة
  Future<void> deleteInvoice(String invoiceId) async {
    final db = await database;
    await db.delete('invoices', where: 'id = ?', whereArgs: [invoiceId]);
  }

  /// الحصول على فاتورة واحدة
  Future<InvoiceModel?> getInvoice(String invoiceId) async {
    final db = await database;
    final rows = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [invoiceId],
      limit: 1,
    );

    if (rows.isEmpty) return null;
    return InvoiceModel.fromMap(rows.first);
  }

  /// الحصول على فاتورة واحدة بالمعرف
  Future<InvoiceModel?> getInvoiceById(String invoiceId) async {
    final db = await database;
    final rows = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [invoiceId],
      limit: 1,
    );

    if (rows.isEmpty) return null;
    return InvoiceModel.fromMap(rows.first);
  }

  /// الحصول على جميع الفواتير
  Future<List<InvoiceModel>> getAllInvoices() async {
    final db = await database;
    final rows = await db.query('invoices', orderBy: 'createdAt DESC');

    return rows.map((row) => InvoiceModel.fromMap(row)).toList();
  }

  /// البحث في الفواتير
  Future<List<InvoiceModel>> searchInvoices(String query) async {
    final db = await database;
    final rows = await db.query(
      'invoices',
      where:
          'invoiceNumber LIKE ? OR customerName LIKE ? OR customerPhone LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'createdAt DESC',
    );

    return rows.map((row) => InvoiceModel.fromMap(row)).toList();
  }

  /// الحصول على فواتير عميل معين
  Future<List<InvoiceModel>> getCustomerInvoices(String customerId) async {
    final db = await database;
    final rows = await db.query(
      'invoices',
      where: 'customerId = ?',
      whereArgs: [customerId],
      orderBy: 'createdAt DESC',
    );

    return rows.map((row) => InvoiceModel.fromMap(row)).toList();
  }

  /// الحصول على فواتير بحالة معينة
  Future<List<InvoiceModel>> getInvoicesByStatus(InvoiceStatus status) async {
    final db = await database;
    final rows = await db.query(
      'invoices',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'createdAt DESC',
    );

    return rows.map((row) => InvoiceModel.fromMap(row)).toList();
  }

  /// تحديث حالة الفاتورة
  Future<void> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  ) async {
    final db = await database;
    await db.update(
      'invoices',
      {'status': status.index, 'updatedAt': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// تحديث المبلغ المدفوع للفاتورة
  Future<void> updateInvoicePaidAmount(
    String invoiceId,
    double paidAmount,
  ) async {
    final db = await database;
    await db.update(
      'invoices',
      {'paidAmount': paidAmount, 'updatedAt': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// الحصول على إحصائيات الفواتير
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    final db = await database;

    // إجمالي الفواتير
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices',
    );
    final total = totalResult.first['count'] as int;

    // الفواتير المدفوعة
    final paidResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices WHERE status = 1',
    );
    final paid = paidResult.first['count'] as int;

    // الفواتير غير المدفوعة
    final pendingResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices WHERE status = 0',
    );
    final pending = pendingResult.first['count'] as int;

    // الفواتير الملغية
    final cancelledResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices WHERE status = 2',
    );
    final cancelled = cancelledResult.first['count'] as int;

    // إجمالي المبالغ
    final totalAmountResult = await db.rawQuery(
      'SELECT SUM(total) as total FROM invoices WHERE status != 2',
    );
    final totalAmount =
        (totalAmountResult.first['total'] as num?)?.toDouble() ?? 0.0;

    // إجمالي المبالغ المدفوعة
    final paidAmountResult = await db.rawQuery(
      'SELECT SUM(total) as total FROM invoices WHERE status = 1',
    );
    final paidAmount =
        (paidAmountResult.first['total'] as num?)?.toDouble() ?? 0.0;

    return {
      'total': total,
      'paid': paid,
      'pending': pending,
      'cancelled': cancelled,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'pendingAmount': totalAmount - paidAmount,
    };
  }

  /// الحصول على رقم الفاتورة التالي
  Future<String> getNextInvoiceNumber() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT MAX(CAST(SUBSTR(invoiceNumber, 5) AS INTEGER)) as maxNum FROM invoices WHERE invoiceNumber LIKE "INV-%"',
    );

    final maxNum = result.first['maxNum'] as int? ?? 0;
    final nextNum = maxNum + 1;

    return 'INV-${nextNum.toString().padLeft(6, '0')}';
  }

  /// حذف جميع الفواتير
  Future<void> clearAllInvoices() async {
    final db = await database;
    try {
      await db.delete('invoices');
      print('تم حذف جميع الفواتير بنجاح');
    } catch (e) {
      print('خطأ في حذف الفواتير: $e');
      rethrow;
    }
  }

  /// التأكد من وجود جميع أعمدة جدول الفواتير
  Future<void> _ensureInvoiceColumnsExist(Database db) async {
    try {
      // التحقق من وجود عمود customerPhone
      await db.execute(
        'ALTER TABLE invoices ADD COLUMN customerPhone TEXT DEFAULT ""',
      );
      print('تم التأكد من وجود عمود رقم هاتف العميل');
    } catch (e) {
      // العمود موجود بالفعل، لا حاجة للفعل
      print('عمود رقم هاتف العميل موجود بالفعل');
    }

    try {
      // التحقق من وجود عمود discountAmount
      await db.execute(
        'ALTER TABLE invoices ADD COLUMN discountAmount REAL DEFAULT 0',
      );
      print('تم التأكد من وجود عمود قيمة الخصم');
    } catch (e) {
      // العمود موجود بالفعل، لا حاجة للفعل
      print('عمود قيمة الخصم موجود بالفعل');
    }

    try {
      // التحقق من وجود عمود discountPercentage
      await db.execute(
        'ALTER TABLE invoices ADD COLUMN discountPercentage REAL DEFAULT 0',
      );
      print('تم التأكد من وجود عمود نسبة الخصم');
    } catch (e) {
      // العمود موجود بالفعل، لا حاجة للفعل
      print('عمود نسبة الخصم موجود بالفعل');
    }

    try {
      // التحقق من وجود عمود paidAmount
      await db.execute(
        'ALTER TABLE invoices ADD COLUMN paidAmount REAL DEFAULT 0',
      );
      print('تم التأكد من وجود عمود المبلغ المدفوع');
    } catch (e) {
      // العمود موجود بالفعل، لا حاجة للفعل
      print('عمود المبلغ المدفوع موجود بالفعل');
    }

    // طباعة مخطط الجدول للمساعدة في التصحيح
    await _debugInvoiceTableSchema();
  }

  /// طباعة مخطط جدول الفواتير للمساعدة في التصحيح
  Future<void> _debugInvoiceTableSchema() async {
    final db = await database;
    try {
      final result = await db.rawQuery('PRAGMA table_info(invoices)');
      print('مخطط جدول الفواتير:');
      for (final row in result) {
        print(
          '  ${row['name']}: ${row['type']} ${row['notnull'] == 1 ? 'NOT NULL' : 'NULL'} ${row['dflt_value'] != null ? 'DEFAULT ${row['dflt_value']}' : ''}',
        );
      }
    } catch (e) {
      print('خطأ في الحصول على مخطط الجدول: $e');
    }
  }

  /// إعادة إنشاء جدول الفواتير في حالة وجود مشاكل في المخطط
  Future<void> _recreateInvoicesTable(Database db) async {
    try {
      print('إعادة إنشاء جدول الفواتير...');

      // حذف الجدول القديم
      await db.execute('DROP TABLE IF EXISTS invoices');

      // إنشاء الجدول الجديد مع المخطط الصحيح
      await db.execute('''
        CREATE TABLE invoices (
          id TEXT PRIMARY KEY,
          invoiceNumber TEXT UNIQUE NOT NULL,
          customerId TEXT NOT NULL,
          customerName TEXT NOT NULL,
          customerPhone TEXT DEFAULT "",
          invoiceDate TEXT NOT NULL,
          items TEXT,
          subtotal REAL NOT NULL DEFAULT 0,
          discountAmount REAL NOT NULL DEFAULT 0,
          discountPercentage REAL NOT NULL DEFAULT 0,
          total REAL NOT NULL DEFAULT 0,
          paidAmount REAL NOT NULL DEFAULT 0,
          status INTEGER NOT NULL DEFAULT 0,
          notes TEXT,
          createdBy TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      print('تم إعادة إنشاء جدول الفواتير بنجاح');

      // التأكد من وجود جميع الأعمدة المطلوبة
      await _ensureInvoiceColumnsExist(db);
    } catch (e) {
      print('خطأ في إعادة إنشاء جدول الفواتير: $e');
      rethrow;
    }
  }

  /// إجبار ترقية قاعدة البيانات إلى أحدث إصدار
  Future<void> forceDatabaseUpgrade() async {
    final db = await database;
    final currentVersion = await db.getVersion();
    print('الإصدار الحالي لقاعدة البيانات: $currentVersion');

    if (currentVersion < 15) {
      print('إجبار الترقية إلى الإصدار 15...');
      await _onUpgrade(db, currentVersion, 15);
      await db.setVersion(15);
      print('تم الترقية بنجاح إلى الإصدار 15');
    } else {
      print('قاعدة البيانات محدثة بالفعل');
    }

    // التأكد من وجود جميع الأعمدة المطلوبة بعد الترقية
    await _ensureInvoiceColumnsExist(db);
  }

  // ==================== دوال إجراءات الفواتير ====================

  /// إنشاء إجراء فاتورة جديد
  Future<void> createInvoiceAction(InvoiceActionModel action) async {
    final db = await database;
    await db.insert('invoice_actions', action.toMap());
  }

  /// الحصول على جميع إجراءات الفواتير
  Future<List<InvoiceActionModel>> getAllInvoiceActions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoice_actions',
      orderBy: 'actionDate DESC',
    );
    return List.generate(
      maps.length,
      (i) => InvoiceActionModel.fromMap(maps[i]),
    );
  }

  /// الحصول على إجراءات فاتورة معينة
  Future<List<InvoiceActionModel>> getInvoiceActions(String invoiceId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoice_actions',
      where: 'invoiceId = ?',
      whereArgs: [invoiceId],
      orderBy: 'actionDate DESC',
    );
    return List.generate(
      maps.length,
      (i) => InvoiceActionModel.fromMap(maps[i]),
    );
  }

  /// حذف إجراء فاتورة
  Future<void> deleteInvoiceAction(String actionId) async {
    final db = await database;
    await db.delete('invoice_actions', where: 'id = ?', whereArgs: [actionId]);
  }

  // ==================== دوال المنتجات ====================

  /// الحصول على منتج بواسطة المعرف
  Future<Map<String, dynamic>?> getProductById(String productId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'id = ?',
        whereArgs: [productId],
      );
      return maps.isNotEmpty ? maps.first : null;
    } catch (e) {
      print('Error getting product by ID: $e');
      return null;
    }
  }

  /// الحصول على جميع المنتجات
  Future<List<Map<String, dynamic>>> getAllProducts() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'isActive = ?',
        whereArgs: [1],
        orderBy: 'name ASC',
      );
      return maps;
    } catch (e) {
      print('Error getting all products: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'isActive = ? AND (name LIKE ? OR code LIKE ?)',
        whereArgs: [1, '%$query%', '%$query%'],
        orderBy: 'name ASC',
      );
      return maps;
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }
}
