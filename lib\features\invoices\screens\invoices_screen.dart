import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_service.dart';
import '../../../constants/app_colors.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../../../core/settings/app_settings.dart';
import '../../../utils/screen_utils.dart';
import '../widgets/invoice_card.dart';
import '../widgets/invoices_by_governorate_list.dart';
import 'add_invoice_screen.dart';
import 'invoice_details_screen.dart';
import 'invoices_by_date_screen.dart';
import 'collect_invoice_screen.dart';
import '../../../widgets/app_drawer.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({Key? key}) : super(key: key);

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen> {
  final InvoiceService _invoiceService = InvoiceService();

  List<InvoiceModel> _invoices = [];
  List<InvoiceModel> _filteredInvoices = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _errorMessage = '';
  int _viewMode = 0; // 0: عادي، 1: حسب المحافظات، 2: حسب التاريخ

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final invoices = await _invoiceService.getAllInvoices();

      setState(() {
        _invoices = invoices;
        _filteredInvoices = invoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الفواتير: $e';
        _isLoading = false;
      });
    }
  }

  void _filterInvoices() {
    setState(() {
      _filteredInvoices = _invoices.where((invoice) {
        // فلترة حسب البحث فقط
        return _searchQuery.isEmpty ||
            invoice.invoiceNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            invoice.customerName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            invoice.customerPhone.contains(_searchQuery);
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _filterInvoices();
  }

  Future<void> _deleteInvoice(InvoiceModel invoice) async {
    // التحقق من حماية الحذف
    final appSettings = AppSettings();
    final isDeleteProtected = await appSettings
        .isPasswordRequiredForInvoiceDelete();

    if (isDeleteProtected) {
      // استخدام حوار الحماية مع كلمة المرور
      showDeleteProtectionDialog(
        context: context,
        title: 'حماية حذف الفاتورة',
        message:
            'حذف الفاتورة محمي بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة.',
        itemName: 'الفاتورة رقم ${invoice.invoiceNumber}',
        onConfirm: () async {
          try {
            await _invoiceService.deleteInvoice(invoice.id);
            _loadInvoices();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم حذف الفاتورة رقم ${invoice.invoiceNumber} بنجاح',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('خطأ في حذف الفاتورة: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      );
    } else {
      // استخدام حوار التأكيد العادي
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              'تأكيد الحذف',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
                fontSize: ScreenUtils.getResponsiveFontSize(
                  context,
                  smallSize: 16.sp,
                  mediumSize: 18.sp,
                  largeSize: 20.sp,
                ),
              ),
            ),
            content: Text(
              'هل أنت متأكد من حذف الفاتورة رقم ${invoice.invoiceNumber}؟\nلا يمكن التراجع عن هذا الإجراء.',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: ScreenUtils.getResponsiveFontSize(
                  context,
                  smallSize: 14.sp,
                  mediumSize: 16.sp,
                  largeSize: 18.sp,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'إلغاء',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: ScreenUtils.getResponsiveFontSize(
                      context,
                      smallSize: 14.sp,
                      mediumSize: 16.sp,
                      largeSize: 18.sp,
                    ),
                  ),
                ),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await _invoiceService.deleteInvoice(invoice.id);
                    _loadInvoices();
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'تم حذف الفاتورة رقم ${invoice.invoiceNumber} بنجاح',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في حذف الفاتورة: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: Text(
                  'حذف',
                  style: TextStyle(
                    color: Colors.red,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    fontSize: ScreenUtils.getResponsiveFontSize(
                      context,
                      smallSize: 14.sp,
                      mediumSize: 16.sp,
                      largeSize: 18.sp,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _updateInvoiceStatus(
    InvoiceModel invoice,
    InvoiceStatus newStatus,
  ) async {
    try {
      await _invoiceService.updateInvoiceStatus(invoice.id, newStatus);
      _loadInvoices();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث حالة الفاتورة إلى ${newStatus == InvoiceStatus.paid ? 'تم الدفع بالكامل' : 'معلق'}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _collectInvoice(InvoiceModel invoice) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CollectInvoiceScreen(invoice: invoice),
      ),
    );

    // إذا تم التحصيل بنجاح، قم بتحديث قائمة الفواتير
    if (result != null && result['success'] == true) {
      _loadInvoices();
    }
  }

  // بناء شريط البحث المتجاوب
  Widget _buildResponsiveSearchBar() {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Container(
      padding: ScreenUtils.getResponsivePadding(
        context,
        smallPadding: EdgeInsets.all(12.w),
        mediumPadding: EdgeInsets.all(16.w),
        largePadding: EdgeInsets.all(20.w),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isSmallScreen
          ? Column(
              children: [
                CustomTextField(
                  controller: TextEditingController(),
                  labelText: 'البحث في الفواتير',
                  hintText: 'البحث في الفواتير...',
                  prefixIcon: Icons.search,
                  onChanged: _onSearchChanged,
                ),
                SizedBox(height: 12.h),
                // عنوان نوع العرض الحالي
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Text(
                    _getViewModeTitle(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
                SizedBox(height: 12.h),
                Row(
                  children: [
                    // أزرار التبديل بين أنماط العرض للشاشات الصغيرة
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20.r),
                          border: Border.all(
                            color: AppColors.primary.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // زر العرض العادي
                            IconButton(
                              icon: Icon(
                                Icons.list,
                                color: _viewMode == 0
                                    ? AppColors.primary
                                    : Colors.grey[600],
                                size: 20.sp,
                              ),
                              onPressed: () {
                                setState(() {
                                  _viewMode = 0;
                                });
                              },
                              tooltip: 'عرض عادي',
                            ),
                            // زر العرض حسب المحافظات
                            IconButton(
                              icon: Icon(
                                Icons.location_on,
                                color: _viewMode == 1
                                    ? AppColors.primary
                                    : Colors.grey[600],
                                size: 20.sp,
                              ),
                              onPressed: () {
                                setState(() {
                                  _viewMode = 1;
                                });
                              },
                              tooltip: 'عرض حسب المحافظات',
                            ),
                            // زر العرض حسب التاريخ
                            IconButton(
                              icon: Icon(
                                Icons.calendar_today,
                                color: _viewMode == 2
                                    ? AppColors.primary
                                    : Colors.grey[600],
                                size: 20.sp,
                              ),
                              onPressed: () {
                                setState(() {
                                  _viewMode = 2;
                                });
                              },
                              tooltip: 'عرض حسب التاريخ',
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 8.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: AppColors.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.receipt_long,
                            color: AppColors.primary,
                            size: ScreenUtils.getResponsiveIconSize(
                              context,
                              smallSize: 16.sp,
                              mediumSize: 18.sp,
                              largeSize: 20.sp,
                            ),
                          ),
                          SizedBox(width: 6.w),
                          Text(
                            '${_filteredInvoices.length} فاتورة',
                            style: TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenUtils.getResponsiveFontSize(
                                context,
                                smallSize: 12.sp,
                                mediumSize: 14.sp,
                                largeSize: 16.sp,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: TextEditingController(),
                    labelText: 'البحث في الفواتير',
                    hintText: 'البحث في الفواتير...',
                    prefixIcon: Icons.search,
                    onChanged: _onSearchChanged,
                  ),
                ),
                SizedBox(width: 16.w),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.receipt_long,
                        color: AppColors.primary,
                        size: ScreenUtils.getResponsiveIconSize(
                          context,
                          smallSize: 16.sp,
                          mediumSize: 18.sp,
                          largeSize: 20.sp,
                        ),
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        '${_filteredInvoices.length} فاتورة',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: ScreenUtils.getResponsiveFontSize(
                            context,
                            smallSize: 12.sp,
                            mediumSize: 14.sp,
                            largeSize: 16.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  // بناء قائمة الفواتير المتجاوبة
  Widget _buildResponsiveInvoiceList() {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);
    final isLargeScreen = ScreenUtils.isLargeScreen(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.primary),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: ScreenUtils.getResponsiveIconSize(
                context,
                smallSize: 48.sp,
                mediumSize: 56.sp,
                largeSize: 64.sp,
              ),
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              _errorMessage,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: ScreenUtils.getResponsiveFontSize(
                  context,
                  smallSize: 14.sp,
                  mediumSize: 16.sp,
                  largeSize: 18.sp,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            CustomButton(
              text: 'إعادة المحاولة',
              onPressed: _loadInvoices,
              backgroundColor: AppColors.primary,
            ),
          ],
        ),
      );
    }

    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: ScreenUtils.getResponsiveIconSize(
                context,
                smallSize: 48.sp,
                mediumSize: 56.sp,
                largeSize: 64.sp,
              ),
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد فواتير تطابق البحث'
                  : 'لا توجد فواتير',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: ScreenUtils.getResponsiveFontSize(
                  context,
                  smallSize: 14.sp,
                  mediumSize: 16.sp,
                  largeSize: 18.sp,
                ),
              ),
            ),
            if (_searchQuery.isEmpty) ...[
              SizedBox(height: 16.h),
              CustomButton(
                text: 'إضافة فاتورة جديدة',
                onPressed: () => _navigateToAddInvoice(),
                backgroundColor: AppColors.primary,
              ),
            ],
          ],
        ),
      );
    }

    // استخدام GridView للشاشات المتوسطة والكبيرة
    if (isMediumScreen || isLargeScreen) {
      final crossAxisCount = ScreenUtils.getGridCrossAxisCount(context);
      final childAspectRatio = ScreenUtils.getResponsiveAspectRatio(context);
      final spacing = ScreenUtils.getResponsiveSpacing(context);

      return RefreshIndicator(
        onRefresh: _loadInvoices,
        color: AppColors.primary,
        child: GridView.builder(
          padding: EdgeInsets.all(spacing),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
          ),
          itemCount: _filteredInvoices.length,
          itemBuilder: (context, index) {
            final invoice = _filteredInvoices[index];
            return InvoiceCard(
              invoice: invoice,
              onTap: () => _navigateToInvoiceDetails(invoice),
              onDelete: () => _deleteInvoice(invoice),
              onStatusUpdate: (status) => _updateInvoiceStatus(invoice, status),
              onCollect: () => _collectInvoice(invoice),
            );
          },
        ),
      );
    }

    // استخدام ListView للشاشات الصغيرة
    return RefreshIndicator(
      onRefresh: _loadInvoices,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _filteredInvoices.length,
        itemBuilder: (context, index) {
          final invoice = _filteredInvoices[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: InvoiceCard(
              invoice: invoice,
              onTap: () => _navigateToInvoiceDetails(invoice),
              onDelete: () => _deleteInvoice(invoice),
              onStatusUpdate: (status) => _updateInvoiceStatus(invoice, status),
              onCollect: () => _collectInvoice(invoice),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: isSmallScreen
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'إدارة الفواتير',
                    style: TextStyle(
                      fontSize: ScreenUtils.getResponsiveFontSize(
                        context,
                        smallSize: 16.sp,
                        mediumSize: 18.sp,
                        largeSize: 20.sp,
                      ),
                    ),
                  ),
                  Text(
                    '${_filteredInvoices.length} فاتورة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'إدارة الفواتير',
                    style: TextStyle(
                      fontSize: ScreenUtils.getResponsiveFontSize(
                        context,
                        smallSize: 16.sp,
                        mediumSize: 18.sp,
                        largeSize: 20.sp,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.receipt_long,
                          color: Colors.white,
                          size: ScreenUtils.getResponsiveIconSize(
                            context,
                            smallSize: 14.sp,
                            mediumSize: 16.sp,
                            largeSize: 18.sp,
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '${_filteredInvoices.length}',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: ScreenUtils.getResponsiveFontSize(
                              context,
                              smallSize: 10.sp,
                              mediumSize: 12.sp,
                              largeSize: 14.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
        actions: [
          // أزرار التبديل بين أنماط العرض - متجاوبة
          if (!isSmallScreen) ...[
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // زر العرض العادي
                  IconButton(
                    icon: Icon(
                      Icons.list,
                      color: _viewMode == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.7),
                      size: ScreenUtils.getResponsiveIconSize(
                        context,
                        smallSize: 18.sp,
                        mediumSize: 20.sp,
                        largeSize: 22.sp,
                      ),
                    ),
                    onPressed: () {
                      setState(() {
                        _viewMode = 0;
                      });
                    },
                    tooltip: 'عرض عادي',
                  ),
                  // زر العرض حسب المحافظات
                  IconButton(
                    icon: Icon(
                      Icons.location_on,
                      color: _viewMode == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.7),
                      size: ScreenUtils.getResponsiveIconSize(
                        context,
                        smallSize: 18.sp,
                        mediumSize: 20.sp,
                        largeSize: 22.sp,
                      ),
                    ),
                    onPressed: () {
                      setState(() {
                        _viewMode = 1;
                      });
                    },
                    tooltip: 'عرض حسب المحافظات',
                  ),
                  // زر العرض حسب التاريخ
                  IconButton(
                    icon: Icon(
                      Icons.calendar_today,
                      color: _viewMode == 2
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.7),
                      size: ScreenUtils.getResponsiveIconSize(
                        context,
                        smallSize: 18.sp,
                        mediumSize: 20.sp,
                        largeSize: 22.sp,
                      ),
                    ),
                    onPressed: () {
                      setState(() {
                        _viewMode = 2;
                      });
                    },
                    tooltip: 'عرض حسب التاريخ',
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
          ],
          IconButton(
            onPressed: _loadInvoices,
            icon: Icon(
              Icons.refresh,
              color: Colors.white,
              size: ScreenUtils.getResponsiveIconSize(
                context,
                smallSize: 20.sp,
                mediumSize: 22.sp,
                largeSize: 24.sp,
              ),
            ),
            tooltip: 'تحديث',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // شريط البحث المتجاوب
          _buildResponsiveSearchBar(),

          // عداد الفواتير - متجاوب
          if (!_isLoading && _filteredInvoices.isNotEmpty && _viewMode == 0)
            Container(
              padding: ScreenUtils.getResponsivePadding(
                context,
                smallPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 6.h,
                ),
                mediumPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
                largePadding: EdgeInsets.symmetric(
                  horizontal: 20.w,
                  vertical: 10.h,
                ),
              ),
              color: Colors.grey[50],
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.grey[600],
                    size: ScreenUtils.getResponsiveIconSize(
                      context,
                      smallSize: 16.sp,
                      mediumSize: 18.sp,
                      largeSize: 20.sp,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'تم العثور على ${_filteredInvoices.length} فاتورة',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: ScreenUtils.getResponsiveFontSize(
                        context,
                        smallSize: 12.sp,
                        mediumSize: 14.sp,
                        largeSize: 16.sp,
                      ),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_searchQuery.isNotEmpty) ...[
                    SizedBox(width: 16.w),
                    Text(
                      'من أصل ${_invoices.length} فاتورة',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: ScreenUtils.getResponsiveFontSize(
                          context,
                          smallSize: 10.sp,
                          mediumSize: 12.sp,
                          largeSize: 14.sp,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

          // قائمة الفواتير المتجاوبة
          Expanded(
            child: _viewMode == 1
                ? InvoicesByGovernorateList(searchQuery: _searchQuery)
                : _viewMode == 2
                ? const InvoicesByDateScreen()
                : _buildResponsiveInvoiceList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddInvoice,
        backgroundColor: AppColors.primary,
        child: Icon(
          Icons.add,
          color: Colors.white,
          size: ScreenUtils.getResponsiveIconSize(
            context,
            smallSize: 20.sp,
            mediumSize: 22.sp,
            largeSize: 24.sp,
          ),
        ),
        tooltip: 'إضافة فاتورة جديدة',
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  void _navigateToAddInvoice() async {
    final result = await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddInvoiceScreen()));

    if (result == true) {
      _loadInvoices();
    }
  }

  void _navigateToInvoiceDetails(InvoiceModel invoice) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InvoiceDetailsScreen(invoice: invoice),
      ),
    );

    if (result == true) {
      _loadInvoices();
    }
  }

  // الحصول على عنوان نوع العرض الحالي
  String _getViewModeTitle() {
    switch (_viewMode) {
      case 0:
        return 'عرض عادي';
      case 1:
        return 'عرض حسب المحافظات';
      case 2:
        return 'عرض حسب التاريخ';
      default:
        return 'عرض عادي';
    }
  }
}
