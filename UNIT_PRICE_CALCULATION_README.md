# نظام حساب الأسعار حسب الوحدات في الفواتير

## ملخص التحديث

تم تطوير نظام ذكي لحساب الأسعار تلقائياً في الفواتير بناءً على الوحدة المختارة (قطعة أو كرتونة). النظام يحسب السعر تلقائياً ويحدث الإجمالي بناءً على الوحدة المختارة.

## الميزات الجديدة

### 1. حساب السعر التلقائي حسب الوحدة
- **قطعة**: يعرض السعر الأساسي للمنتج
- **كرتونة**: يحسب السعر تلقائياً = سعر القطعة × عدد القطع في الكرتونة

### 2. تحديث تلقائي للإجمالي
- عند تغيير الوحدة، يتم تحديث السعر والإجمالي تلقائياً
- عند تغيير الكمية، يتم حساب الإجمالي بناءً على الوحدة المختارة

### 3. عرض معلومات الوحدة
- عرض السعر الحالي حسب الوحدة المختارة
- معلومات إضافية للكرتونة (عدد القطع والسعر الإجمالي)

## التحديثات المطبقة

### 1. ProductModel
تم إضافة دوال جديدة:
```dart
// الحصول على السعر حسب الوحدة المختارة
double getPriceForUnit(String unit, {String? customerType})

// الحصول على السعر حسب الوحدة للفاتورة
double getPriceForUnitInInvoice(String unit)

// الحصول على الوحدات المتاحة للمنتج
List<String> getAvailableUnits()
```

### 2. InvoiceService
تم تحديث `createInvoiceItem`:
```dart
InvoiceItem createInvoiceItem({
  required ProductModel product,
  required int quantity,
  String? unit, // إضافة معلمة الوحدة
  double? customPrice,
})
```

### 3. InvoiceItemForm
تم تحديث الواجهة لتدعم:
- اختيار الوحدات المتاحة فقط للمنتج
- تحديث السعر والإجمالي تلقائياً عند تغيير الوحدة
- عرض معلومات مفصلة عن السعر والوحدة

## كيفية العمل

### عند إضافة منتج للفاتورة:
1. يتم إنشاء العنصر بالوحدة الافتراضية (قطعة)
2. يمكن تغيير الوحدة من القائمة المنسدلة
3. يتم تحديث السعر والإجمالي تلقائياً

### عند تغيير الوحدة:
1. **قطعة**: السعر = السعر الأساسي للمنتج
2. **كرتونة**: السعر = السعر الأساسي × عدد القطع في الكرتونة
3. يتم تحديث الإجمالي تلقائياً

### عند تغيير الكمية:
1. يتم الحفاظ على الوحدة المختارة
2. يتم حساب الإجمالي بناءً على السعر الحالي للوحدة
3. يتم عرض رسالة نجاح مع تفاصيل التحديث

## مثال عملي

**منتج: حقن طبية**
- السعر الأساسي: 5 ج.م للقطعة
- عدد القطع في الكرتونة: 10

**في الفاتورة:**
- **قطعة**: السعر = 5 ج.م، الإجمالي = 5 × الكمية
- **كرتونة**: السعر = 50 ج.م (5 × 10)، الإجمالي = 50 × الكمية

## الوحدات المدعومة

- **قطعة**: السعر الأساسي
- **كرتونة**: السعر × عدد القطع في الكرتونة
- **وحدات أخرى**: يتم استخدام السعر الأساسي

## الفوائد

1. **دقة في الحسابات**: منع الأخطاء في حساب الأسعار
2. **سهولة الاستخدام**: تحديث تلقائي للأسعار والإجماليات
3. **مرونة في الوحدات**: دعم الوحدات المختلفة حسب المنتج
4. **شفافية**: عرض واضح للأسعار والوحدات
5. **اتساق البيانات**: ضمان تطابق الأسعار مع المنتجات

## ملاحظات تقنية

- النظام يحافظ على السعر الأساسي للمنتج
- يتم حساب سعر الكرتونة تلقائياً بناءً على `piecesPerCarton`
- الوحدات المتاحة تعتمد على إعدادات المنتج
- يتم تحديث الواجهة تلقائياً عند تغيير الوحدة

## الاختبار

لاختبار النظام:
1. أضف منتج للفاتورة
2. غيّر الوحدة من قطعة إلى كرتونة
3. تأكد من تحديث السعر والإجمالي تلقائياً
4. غيّر الكمية وتأكد من الحساب الصحيح
