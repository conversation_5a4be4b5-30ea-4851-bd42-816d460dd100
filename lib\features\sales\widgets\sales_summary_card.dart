import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../utils/screen_utils.dart';

class SalesSummaryCard extends StatelessWidget {
  final Map<String, dynamic> statistics;
  final int filteredCount;
  final int totalCount;

  const SalesSummaryCard({
    super.key,
    required this.statistics,
    required this.filteredCount,
    required this.totalCount,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);

    final totalSales = statistics['totalSales'] ?? 0.0;
    final totalPaid = statistics['totalPaid'] ?? 0.0;
    final totalRemaining = statistics['totalRemaining'] ?? 0.0;
    final paymentRate = statistics['paymentRate'] ?? 0.0;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.primary.withOpacity(0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16.w : 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                  size: isSmallScreen ? 20.sp : 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'ملخص المبيعات',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16.sp : 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                    fontFamily: 'Cairo',
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '$filteredCount من $totalCount',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _StatisticItem(
                    title: 'إجمالي المبيعات',
                    value: totalSales,
                    icon: Icons.attach_money,
                    color: AppColors.primary,
                    isSmallScreen: isSmallScreen,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _StatisticItem(
                    title: 'المدفوع',
                    value: totalPaid,
                    icon: Icons.check_circle,
                    color: Colors.green,
                    isSmallScreen: isSmallScreen,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _StatisticItem(
                    title: 'المتبقي',
                    value: totalRemaining,
                    icon: Icons.pending,
                    color: Colors.orange,
                    isSmallScreen: isSmallScreen,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _StatisticItem(
                    title: 'نسبة الدفع',
                    value: paymentRate,
                    icon: Icons.percent,
                    color: Colors.blue,
                    isSmallScreen: isSmallScreen,
                    isPercentage: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _StatisticItem extends StatelessWidget {
  final String title;
  final double value;
  final IconData icon;
  final Color color;
  final bool isSmallScreen;
  final bool isPercentage;

  const _StatisticItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.isSmallScreen,
    this.isPercentage = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12.w : 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: isSmallScreen ? 16.sp : 18.sp),
              SizedBox(width: 6.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 11.sp : 12.sp,
                    color: Colors.grey.shade700,
                    fontFamily: 'Cairo',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            isPercentage
                ? '${value.toStringAsFixed(1)}%'
                : NumberFormat.currency(
                    locale: 'ar_SA',
                    symbol: 'ر.س ',
                    decimalDigits: 2,
                  ).format(value),
            style: TextStyle(
              fontSize: isSmallScreen ? 14.sp : 16.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }
}
