#!/bin/bash

# سكريبت بناء التطبيق المحسن
# Atlas Medical Supplies - Optimized Build Script

echo "🚀 بدء بناء التطبيق المحسن..."

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# التحقق من وجود Flutter
if ! command -v flutter &> /dev/null; then
    print_error "Flutter غير مثبت أو غير متاح في PATH"
    exit 1
fi

# التحقق من إصدار Flutter
FLUTTER_VERSION=$(flutter --version | grep -o "Flutter [0-9]*\.[0-9]*\.[0-9]*" | head -1)
print_message "إصدار Flutter: $FLUTTER_VERSION"

# تنظيف المشروع
print_header "تنظيف المشروع"
flutter clean
print_message "تم تنظيف المشروع"

# تحديث التبعيات
print_header "تحديث التبعيات"
flutter pub get
print_message "تم تحديث التبعيات"

# تحليل الكود
print_header "تحليل الكود"
flutter analyze
if [ $? -eq 0 ]; then
    print_message "تحليل الكود ناجح"
else
    print_warning "تحليل الكود وجد بعض المشاكل"
fi

# تشغيل الاختبارات
print_header "تشغيل الاختبارات"
flutter test
if [ $? -eq 0 ]; then
    print_message "جميع الاختبارات نجحت"
else
    print_warning "بعض الاختبارات فشلت"
fi

# بناء نسخة Android محسنة
print_header "بناء نسخة Android محسنة"

# التحقق من وجود Android SDK
if [ -z "$ANDROID_HOME" ]; then
    print_warning "ANDROID_HOME غير محدد"
fi

# بناء APK محسن
print_message "بناء APK محسن..."
flutter build apk \
    --release \
    --tree-shake-icons \
    --obfuscate \
    --split-debug-info=build/debug-info \
    --target-platform android-arm64

if [ $? -eq 0 ]; then
    print_message "تم بناء APK بنجاح"
    print_message "موقع الملف: build/app/outputs/flutter-apk/app-arm64-release.apk"
else
    print_error "فشل في بناء APK"
    exit 1
fi

# بناء App Bundle (للنشر على Google Play)
print_header "بناء App Bundle"
flutter build appbundle \
    --release \
    --tree-shake-icons \
    --obfuscate \
    --split-debug-info=build/debug-info

if [ $? -eq 0 ]; then
    print_message "تم بناء App Bundle بنجاح"
    print_message "موقع الملف: build/app/outputs/bundle/release/app-release.aab"
else
    print_warning "فشل في بناء App Bundle"
fi

# بناء نسخة Web محسنة
print_header "بناء نسخة Web محسنة"
flutter build web \
    --release \
    --tree-shake-icons \
    --web-renderer canvaskit \
    --dart-define=FLUTTER_WEB_USE_SKIA=true

if [ $? -eq 0 ]; then
    print_message "تم بناء نسخة Web بنجاح"
    print_message "موقع الملفات: build/web/"
else
    print_warning "فشل في بناء نسخة Web"
fi

# بناء نسخة Windows محسنة
print_header "بناء نسخة Windows محسنة"
flutter build windows \
    --release \
    --tree-shake-icons

if [ $? -eq 0 ]; then
    print_message "تم بناء نسخة Windows بنجاح"
    print_message "موقع الملفات: build/windows/runner/Release/"
else
    print_warning "فشل في بناء نسخة Windows"
fi

# بناء نسخة macOS محسنة
print_header "بناء نسخة macOS محسنة"
flutter build macos \
    --release \
    --tree-shake-icons

if [ $? -eq 0 ]; then
    print_message "تم بناء نسخة macOS بنجاح"
    print_message "موقع الملفات: build/macos/Build/Products/Release/"
else
    print_warning "فشل في بناء نسخة macOS"
fi

# بناء نسخة Linux محسنة
print_header "بناء نسخة Linux محسنة"
flutter build linux \
    --release \
    --tree-shake-icons

if [ $? -eq 0 ]; then
    print_message "تم بناء نسخة Linux بنجاح"
    print_message "موقع الملفات: build/linux/x64/release/bundle/"
else
    print_warning "فشل في بناء نسخة Linux"
fi

# إنشاء تقرير البناء
print_header "إنشاء تقرير البناء"
BUILD_REPORT="build/build_report_$(date +%Y%m%d_%H%M%S).txt"

echo "تقرير بناء التطبيق" > "$BUILD_REPORT"
echo "التاريخ: $(date)" >> "$BUILD_REPORT"
echo "إصدار Flutter: $FLUTTER_VERSION" >> "$BUILD_REPORT"
echo "" >> "$BUILD_REPORT"

# معلومات عن الملفات المبنية
echo "الملفات المبنية:" >> "$BUILD_REPORT"
if [ -f "build/app/outputs/flutter-apk/app-arm64-release.apk" ]; then
    APK_SIZE=$(du -h "build/app/outputs/flutter-apk/app-arm64-release.apk" | cut -f1)
    echo "- Android APK: $APK_SIZE" >> "$BUILD_REPORT"
fi

if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    AAB_SIZE=$(du -h "build/app/outputs/bundle/release/app-release.aab" | cut -f1)
    echo "- Android App Bundle: $AAB_SIZE" >> "$BUILD_REPORT"
fi

if [ -d "build/web" ]; then
    WEB_SIZE=$(du -sh "build/web" | cut -f1)
    echo "- Web Bundle: $WEB_SIZE" >> "$BUILD_REPORT"
fi

if [ -d "build/windows/runner/Release" ]; then
    WIN_SIZE=$(du -sh "build/windows/runner/Release" | cut -f1)
    echo "- Windows Bundle: $WIN_SIZE" >> "$BUILD_REPORT"
fi

if [ -d "build/macos/Build/Products/Release" ]; then
    MAC_SIZE=$(du -sh "build/macos/Build/Products/Release" | cut -f1)
    echo "- macOS Bundle: $MAC_SIZE" >> "$BUILD_REPORT"
fi

if [ -d "build/linux/x64/release/bundle" ]; then
    LINUX_SIZE=$(du -sh "build/linux/x64/release/bundle" | cut -f1)
    echo "- Linux Bundle: $LINUX_SIZE" >> "$BUILD_REPORT"
fi

print_message "تم إنشاء تقرير البناء: $BUILD_REPORT"

# تنظيف الملفات المؤقتة
print_header "تنظيف الملفات المؤقتة"
find . -name "*.log" -type f -delete
find . -name "*.tmp" -type f -delete
print_message "تم تنظيف الملفات المؤقتة"

print_header "اكتمل البناء بنجاح! 🎉"
print_message "جميع النسخ المحسنة جاهزة للنشر"
print_message "راجع تقرير البناء للحصول على التفاصيل: $BUILD_REPORT"
