import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../lib/services/database_service.dart';
import '../lib/services/product_service.dart';
import '../lib/models/product_model.dart';

void main() {
  group('Products Fix Test', () {
    late DatabaseService databaseService;
    late ProductService productService;

    setUpAll(() {
      // Initialize database factory for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() {
      databaseService = DatabaseService();
      productService = ProductService();
    });

    test('Test database connection and products table', () async {
      print('🔍 بدء اختبار قاعدة البيانات...');
      
      try {
        // فحص قاعدة البيانات
        await databaseService.checkAndRepairDatabase();
        print('✅ تم فحص قاعدة البيانات بنجاح');
        
        // محاولة جلب المنتجات
        final products = await productService.getAllProducts();
        print('📦 تم جلب ${products.length} منتج من قاعدة البيانات');
        
        // طباعة تفاصيل المنتجات
        for (int i = 0; i < products.length; i++) {
          final product = products[i];
          print('  ${i + 1}. ${product.name} - isActive: ${product.isActive} - الكمية: ${product.quantity}');
        }
        
        expect(true, true); // اختبار ناجح
      } catch (e) {
        print('❌ خطأ في اختبار قاعدة البيانات: $e');
        fail('فشل في اختبار قاعدة البيانات: $e');
      }
    });

    test('Test adding demo products', () async {
      print('🔍 بدء اختبار إضافة المنتجات التجريبية...');
      
      try {
        // إضافة منتجات تجريبية
        final result = await productService.addDemoProducts();
        print('📦 نتيجة إضافة المنتجات التجريبية: $result');
        
        if (result) {
          // جلب المنتجات بعد الإضافة
          final products = await productService.getAllProducts();
          print('📦 تم جلب ${products.length} منتج بعد إضافة المنتجات التجريبية');
          
          // طباعة تفاصيل المنتجات
          for (int i = 0; i < products.length; i++) {
            final product = products[i];
            print('  ${i + 1}. ${product.name} - isActive: ${product.isActive} - الكمية: ${product.quantity}');
          }
          
          expect(products.length, greaterThan(0));
        } else {
          print('❌ فشل في إضافة المنتجات التجريبية');
          expect(result, true);
        }
      } catch (e) {
        print('❌ خطأ في اختبار إضافة المنتجات التجريبية: $e');
        fail('فشل في اختبار إضافة المنتجات التجريبية: $e');
      }
    });

    test('Test creating single test product', () async {
      print('🔍 بدء اختبار إنشاء منتج تجريبي واحد...');
      
      try {
        // إنشاء منتج تجريبي واحد
        final result = await productService.createTestProduct();
        print('📦 نتيجة إنشاء المنتج التجريبي: $result');
        
        if (result) {
          // جلب المنتجات بعد الإنشاء
          final products = await productService.getAllProducts();
          print('📦 تم جلب ${products.length} منتج بعد إنشاء المنتج التجريبي');
          
          // البحث عن المنتج التجريبي
          final testProduct = products.where((p) => p.name == 'منتج تجريبي').firstOrNull;
          if (testProduct != null) {
            print('✅ تم العثور على المنتج التجريبي: ${testProduct.name} - isActive: ${testProduct.isActive}');
            expect(testProduct.isActive, true);
          } else {
            print('❌ لم يتم العثور على المنتج التجريبي');
            expect(testProduct, isNotNull);
          }
        } else {
          print('❌ فشل في إنشاء المنتج التجريبي');
          expect(result, true);
        }
      } catch (e) {
        print('❌ خطأ في اختبار إنشاء المنتج التجريبي: $e');
        fail('فشل في اختبار إنشاء المنتج التجريبي: $e');
      }
    });

    test('Test products filtering', () async {
      print('🔍 بدء اختبار تصفية المنتجات...');
      
      try {
        // جلب جميع المنتجات
        final allProducts = await productService.getAllProducts();
        print('📦 إجمالي المنتجات: ${allProducts.length}');
        
        // تصفية المنتجات النشطة فقط
        final activeProducts = allProducts.where((p) => p.isActive).toList();
        print('📦 المنتجات النشطة: ${activeProducts.length}');
        
        // تصفية المنتجات المتوفرة
        final availableProducts = allProducts.where((p) => p.quantity > 0).toList();
        print('📦 المنتجات المتوفرة: ${availableProducts.length}');
        
        // طباعة تفاصيل المنتجات النشطة
        for (int i = 0; i < activeProducts.length; i++) {
          final product = activeProducts[i];
          print('  ${i + 1}. ${product.name} - الكمية: ${product.quantity} - السعر: ${product.price}');
        }
        
        expect(activeProducts.length, greaterThanOrEqualTo(0));
      } catch (e) {
        print('❌ خطأ في اختبار تصفية المنتجات: $e');
        fail('فشل في اختبار تصفية المنتجات: $e');
      }
    });

    test('Test database structure', () async {
      print('🔍 بدء اختبار هيكل قاعدة البيانات...');
      
      try {
        final db = await databaseService.database;
        
        // فحص وجود جدول المنتجات
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'",
        );
        print('📦 جدول المنتجات موجود: ${tables.isNotEmpty}');
        
        if (tables.isNotEmpty) {
          // فحص أعمدة جدول المنتجات
          final columns = await db.rawQuery("PRAGMA table_info(products)");
          print('📦 أعمدة جدول المنتجات:');
          for (final column in columns) {
            print('  - ${column['name']}: ${column['type']}');
          }
          
          // فحص عدد المنتجات
          final countResult = await db.rawQuery("SELECT COUNT(*) as count FROM products");
          final count = countResult.first['count'] as int;
          print('📦 عدد المنتجات في قاعدة البيانات: $count');
          
          // فحص المنتجات النشطة
          final activeCountResult = await db.rawQuery("SELECT COUNT(*) as count FROM products WHERE isActive = 1");
          final activeCount = activeCountResult.first['count'] as int;
          print('📦 عدد المنتجات النشطة: $activeCount');
        }
        
        expect(tables.isNotEmpty, true);
      } catch (e) {
        print('❌ خطأ في اختبار هيكل قاعدة البيانات: $e');
        fail('فشل في اختبار هيكل قاعدة البيانات: $e');
      }
    });
  });
}
