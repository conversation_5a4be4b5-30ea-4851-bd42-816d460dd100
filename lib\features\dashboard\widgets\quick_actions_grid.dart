import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../utils/screen_utils.dart';

import '../../../services/customer_service.dart';
import '../../../services/invoice_service.dart';
import '../../../services/sales_service.dart';
import '../../../services/collection_service.dart';

import '../../customers/screens/customers_screen.dart';

import '../../invoices/screens/invoices_screen.dart';
import '../../sales/screens/sales_screen.dart';

import '../../inventory/screens/inventory_screen.dart';
import '../../collections/screens/collections_screen.dart';

class QuickActionsGrid extends StatefulWidget {
  const QuickActionsGrid({super.key});

  @override
  State<QuickActionsGrid> createState() => _QuickActionsGridState();
}

class _QuickActionsGridState extends State<QuickActionsGrid> {

  final CustomerService _customerService = CustomerService();
  final InvoiceService _invoiceService = InvoiceService();
  final CollectionService _collectionService = CollectionService();

  int _productsCount = 0;
  int _customersCount = 0;
  int _invoicesCount = 0;
  double _totalSales = 0.0;
  int _pendingInvoicesCount = 0;
  double _pendingCollectionsAmount =
      0.0; // إضافة متغير جديد لمبالغ التحصيل المستحقة

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  Future<void> _loadAllData() async {
    try {
      await Future.wait([
        _loadCustomersCount(),
        _loadInvoicesCount(),
        _loadSalesData(),
        _loadPendingInvoicesCount(),
        _loadPendingCollectionsAmount(), // إضافة تحميل مبالغ التحصيل
      ]);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  Future<void> _loadCustomersCount() async {
    try {
      final customers = await _customerService.getAllCustomers();
      if (mounted) {
        setState(() {
          _customersCount = customers.length;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _customersCount = 0;
        });
      }
    }
  }

  Future<void> _loadInvoicesCount() async {
    try {
      final invoices = await _invoiceService.getAllInvoices();
      if (mounted) {
        setState(() {
          _invoicesCount = invoices.length;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _invoicesCount = 0;
        });
      }
    }
  }

  Future<void> _loadSalesData() async {
    try {
      final salesService = SalesService();
      final totalSales = await salesService.getTotalSales();
      if (mounted) {
        setState(() {
          _totalSales = totalSales;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _totalSales = 0.0;
        });
      }
    }
  }

  Future<void> _loadPendingInvoicesCount() async {
    try {
      final pendingInvoices = await _collectionService.getPendingInvoices();
      if (mounted) {
        setState(() {
          _pendingInvoicesCount = pendingInvoices.length;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _pendingInvoicesCount = 0;
        });
      }
    }
  }

  // إضافة دالة جديدة لحساب مبالغ التحصيل المستحقة
  Future<void> _loadPendingCollectionsAmount() async {
    try {
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalAmount = 0.0;

      for (final invoice in pendingInvoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _pendingCollectionsAmount = totalAmount;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _pendingCollectionsAmount = 0.0;
        });
      }
    }
  }

  int _getPendingInvoicesCount() {
    return _pendingInvoicesCount;
  }

  // إضافة دالة جديدة للحصول على مبالغ التحصيل
  double _getPendingCollectionsAmount() {
    return _pendingCollectionsAmount;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = 3; // عدد الأعمدة ثابت = 3
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);

    final statistics = [

      _StatisticCard(
        title: 'العملاء',
        subtitle: 'إدارة',
        value: _isLoading ? '...' : _customersCount.toString(),
        icon: Icons.people,
        color: const Color(0xFF4CAF50), // Green
        onTap: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CustomersScreen()),
          );
          // تحديث جميع البيانات عند العودة
          if (mounted) {
            _loadAllData();
          }
        },
      ),
      _StatisticCard(
        title: 'الفواتير',
        subtitle: 'عرض فقط',
        value: _isLoading ? '...' : _invoicesCount.toString(),
        icon: Icons.receipt_long,
        color: const Color(0xFFE91E63), // Pink
        onTap: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InvoicesScreen()),
          );
          // تحديث جميع البيانات عند العودة
          if (mounted) {
            _loadAllData();
          }
        },
      ),
      _StatisticCard(
        title: 'المبيعات',
        subtitle: 'تقرير مفصل',
        value: _isLoading
            ? '...'
            : NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س ',
                decimalDigits: 0,
              ).format(_totalSales),
        icon: Icons.trending_up,
        color: const Color(0xFF4CAF50), // Green
        onTap: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SalesScreen()),
          );
          // تحديث جميع البيانات عند العودة
          if (mounted) {
            _loadAllData();
          }
        },
      ),

      _StatisticCard(
        title: 'توريد المصنع',
        subtitle: 'إدارة',
        value: '',
        icon: Icons.factory,
        color: const Color(0xFF795548), // Brown
        onTap: () {
          // Navigate to factory supply
        },
      ),
      _StatisticCard(
        title: 'التحصيل',
        subtitle: 'إدارة',
        value: _isLoading
            ? '...'
            : NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س ',
                decimalDigits: 0,
              ).format(_getPendingCollectionsAmount()),
        icon: Icons.payment,
        color: const Color(0xFF9C27B0), // Purple
        onTap: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CollectionsScreen()),
          );
          // تحديث جميع البيانات عند العودة
          if (mounted) {
            _loadAllData();
          }
        },
      ),

      _StatisticCard(
        title: 'المخزون',
        value: '0',
        icon: Icons.warehouse,
        color: const Color(0xFF607D8B), // Dark Gray
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InventoryScreen()),
          );
        },
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // شبكة الإجراءات السريعة
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: ScreenUtils.getResponsiveHorizontalPadding(context),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: ScreenUtils.getResponsiveSpacing(context),
            mainAxisSpacing: ScreenUtils.getResponsiveSpacing(context),
            childAspectRatio: isSmallScreen
                ? 0.75
                : isMediumScreen
                ? 0.85
                : 0.95,
          ),
          itemCount: statistics.length,
          itemBuilder: (context, index) {
            final statistic = statistics[index];
            return _StatisticCardWidget(statistic: statistic, context: context);
          },
        ),
      ],
    );
  }
}

class _StatisticCard {
  final String title;
  final String? subtitle;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _StatisticCard({
    required this.title,
    this.subtitle,
    required this.value,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

class _StatisticCardWidget extends StatelessWidget {
  final _StatisticCard statistic;
  final BuildContext context;

  const _StatisticCardWidget({required this.statistic, required this.context});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);
    final isMediumScreen = ScreenUtils.isMediumScreen(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: statistic.onTap,
        borderRadius: BorderRadius.circular(
          isSmallScreen
              ? 12.r
              : isMediumScreen
              ? 16.r
              : 20.r,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey.shade50, Colors.grey.shade100],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(
              isSmallScreen
                  ? 12.r
                  : isMediumScreen
                  ? 16.r
                  : 20.r,
            ),
            border: Border.all(
              color: Colors.grey.shade200.withValues(alpha: 0.5),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: isSmallScreen
                    ? 8
                    : isMediumScreen
                    ? 12
                    : 16,
                offset: const Offset(0, 2),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.9),
                blurRadius: 1,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة مع خلفية بيضاء
              Container(
                width: isSmallScreen
                    ? 36.w
                    : isMediumScreen
                    ? 40.w
                    : 44.w,
                height: isSmallScreen
                    ? 36.h
                    : isMediumScreen
                    ? 40.h
                    : 44.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                    isSmallScreen
                        ? 10.r
                        : isMediumScreen
                        ? 12.r
                        : 14.r,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: statistic.color.withValues(alpha: 0.2),
                      blurRadius: isSmallScreen
                          ? 8
                          : isMediumScreen
                          ? 10
                          : 12,
                      offset: const Offset(0, 3),
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    statistic.icon,
                    size: isSmallScreen
                        ? 18.sp
                        : isMediumScreen
                        ? 20.sp
                        : 22.sp,
                    color: statistic.color,
                  ),
                ),
              ),

              SizedBox(
                height: isSmallScreen
                    ? 8.h
                    : isMediumScreen
                    ? 12.h
                    : 16.h,
              ),

              // القيمة
              if (statistic.value.isNotEmpty)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen
                        ? 6.w
                        : isMediumScreen
                        ? 8.w
                        : 10.w,
                    vertical: isSmallScreen
                        ? 3.h
                        : isMediumScreen
                        ? 4.h
                        : 5.h,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.grey.shade100, Colors.grey.shade50],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(
                      isSmallScreen
                          ? 10.r
                          : isMediumScreen
                          ? 12.r
                          : 14.r,
                    ),
                    border: Border.all(color: Colors.grey.shade300, width: 0.5),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    statistic.value,
                    style: TextStyle(
                      fontSize: isSmallScreen
                          ? 10.sp
                          : isMediumScreen
                          ? 11.sp
                          : 12.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                      fontFamily: 'Cairo',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

              SizedBox(
                height: isSmallScreen
                    ? 6.h
                    : isMediumScreen
                    ? 8.h
                    : 12.h,
              ),

              // العنوان
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen
                      ? 8.w
                      : isMediumScreen
                      ? 10.w
                      : 12.w,
                ),
                child: Text(
                  statistic.title,
                  style: TextStyle(
                    fontSize: isSmallScreen
                        ? 11.sp
                        : isMediumScreen
                        ? 12.sp
                        : 13.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // العنوان الفرعي
              if (statistic.subtitle != null) ...[
                SizedBox(
                  height: isSmallScreen
                      ? 4.h
                      : isMediumScreen
                      ? 6.h
                      : 8.h,
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen
                        ? 6.w
                        : isMediumScreen
                        ? 7.w
                        : 8.w,
                    vertical: isSmallScreen
                        ? 3.h
                        : isMediumScreen
                        ? 3.5.h
                        : 4.h,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        statistic.color.withValues(alpha: 0.15),
                        statistic.color.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(
                      isSmallScreen
                          ? 8.r
                          : isMediumScreen
                          ? 10.r
                          : 12.r,
                    ),
                    border: Border.all(
                      color: statistic.color.withValues(alpha: 0.3),
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    statistic.subtitle!,
                    style: TextStyle(
                      fontSize: isSmallScreen
                          ? 9.sp
                          : isMediumScreen
                          ? 10.sp
                          : 11.sp,
                      color: statistic.color,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Cairo',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
