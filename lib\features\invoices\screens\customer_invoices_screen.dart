import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../widgets/back_button.dart';
import '../widgets/invoice_card.dart';
import '../../../models/invoice_model.dart';
import '../../../models/customer_model.dart';
import '../../../services/invoice_service.dart';
import 'invoice_details_screen.dart';
import 'collect_invoice_screen.dart';

class CustomerInvoicesScreen extends ConsumerStatefulWidget {
  final CustomerModel customer;

  const CustomerInvoicesScreen({super.key, required this.customer});

  @override
  ConsumerState<CustomerInvoicesScreen> createState() =>
      _CustomerInvoicesScreenState();
}

class _CustomerInvoicesScreenState
    extends ConsumerState<CustomerInvoicesScreen> {
  String _selectedPeriod = 'all';
  List<InvoiceModel> _invoices = [];
  bool _isLoading = false;
  String _errorMessage = '';

  final Map<String, String> _periodLabels = {
    'all': 'الكل',
    'today': 'اليوم',
    'week': 'الأسبوع',
    'month': 'الشهر',
    'year': 'السنة',
  };

  @override
  void initState() {
    super.initState();
    _loadCustomerInvoices();
  }

  Future<void> _loadCustomerInvoices() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final invoiceService = InvoiceService();
      final invoices = await invoiceService.getCustomerInvoices(
        customerId: widget.customer.id,
        period: _selectedPeriod,
      );

      setState(() {
        _invoices = invoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل فواتير العميل: $e';
        _isLoading = false;
      });
    }
  }

  void _onPeriodChanged(String period) {
    setState(() {
      _selectedPeriod = period;
    });
    _loadCustomerInvoices();
  }

  void _openInvoiceDetails(InvoiceModel invoice) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            InvoiceDetailsScreen(invoice: invoice, customer: widget.customer),
      ),
    );
  }

  void _collectInvoice(InvoiceModel invoice) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CollectInvoiceScreen(invoice: invoice),
      ),
    );

    // إذا تم التحصيل بنجاح، قم بتحديث قائمة الفواتير
    if (result != null && result['success'] == true) {
      _loadCustomerInvoices();
    }
  }

  String _getStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return 'معلق';
      case InvoiceStatus.paid:
        return 'تم الدفع بالكامل';
      case InvoiceStatus.partial:
        return 'دفع جزئي';
      case InvoiceStatus.cancelled:
        return 'ملغي';
    }
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partial:
        return Colors.blue;
      case InvoiceStatus.cancelled:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // AppBar مخصص
          Container(
            color: AppColors.primary,
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              bottom: 16.h,
              left: 16.w,
              right: 16.w,
            ),
            child: Row(
              children: [
                CustomBackButton(color: Colors.white, size: 20.sp),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فواتير العميل',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18.sp,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        widget.customer.name ?? 'غير محدد',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // أزرار اختيار الفترة
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: _periodLabels.entries.map((entry) {
                final isSelected = _selectedPeriod == entry.key;
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: 8.w),
                    child: ElevatedButton(
                      onPressed: () => _onPeriodChanged(entry.key),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isSelected
                            ? AppColors.primary
                            : Colors.white,
                        foregroundColor: isSelected
                            ? Colors.white
                            : AppColors.primary,
                        elevation: isSelected ? 0 : 1,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          side: BorderSide(
                            color: isSelected
                                ? Colors.transparent
                                : AppColors.primary,
                          ),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                      ),
                      child: Text(
                        entry.value,
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

          SizedBox(height: 16.h),

          // إحصائيات سريعة
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColors.border),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'عدد الفواتير',
                    '${_invoices.length}',
                    Icons.receipt,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المبالغ',
                    '${_calculateTotalAmount().toStringAsFixed(2)} ر.س',
                    Icons.attach_money,
                    AppColors.success,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'متوسط الفاتورة',
                    _invoices.isNotEmpty
                        ? '${(_calculateTotalAmount() / _invoices.length).toStringAsFixed(2)} ر.س'
                        : '0.00 ر.س',
                    Icons.analytics,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // بطاقة تفاصيل الفاتورة الأولى (إذا كانت موجودة)
          if (_invoices.isNotEmpty) ...[
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: AppColors.border),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildInvoiceDetailsCard(_invoices.first),
            ),
            SizedBox(height: 16.h),
          ],

          // قائمة الفواتير
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: AppColors.border),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildInvoicesList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceDetailsCard(InvoiceModel invoice) {
    final dateFormat = DateFormat('dd/M/yyyy');
    final formattedDate = dateFormat.format(invoice.invoiceDate);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header مع الحالة ورقم الفاتورة
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // حالة الفاتورة
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: _getStatusColor(invoice.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(color: _getStatusColor(invoice.status)),
              ),
              child: Text(
                _getStatusText(invoice.status),
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: _getStatusColor(invoice.status),
                ),
              ),
            ),
            // رقم الفاتورة
            Text(
              invoice.invoiceNumber,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // اسم العميل
        Text(
          widget.customer.name ?? 'غير محدد',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),

        SizedBox(height: 16.h),

        // تفاصيل الفاتورة
        Row(
          children: [
            // التاريخ
            Expanded(
              child: _buildDetailItem(
                'التاريخ',
                Icons.calendar_today,
                formattedDate,
              ),
            ),
            SizedBox(width: 16.w),
            // الخصم
            Expanded(
              child: _buildDetailItem(
                'الخصم',
                Icons.local_offer,
                '${invoice.discountPercentage.toStringAsFixed(1)}%',
              ),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        Row(
          children: [
            // المنتجات
            Expanded(
              child: _buildDetailItem(
                'المنتجات',
                Icons.shopping_cart,
                '${invoice.items.length} منتج',
              ),
            ),
            SizedBox(width: 16.w),
            // قبل الخصم
            Expanded(
              child: _buildDetailItem(
                'قبل الخصم',
                Icons.calculate,
                '${invoice.subtotal.toStringAsFixed(2)} ر.س',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 16.sp, color: AppColors.textSecondary),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48.sp, color: AppColors.error),
            SizedBox(height: 16.h),
            Text(
              _errorMessage,
              style: TextStyle(
                fontFamily: 'Cairo',
                color: AppColors.error,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _loadCustomerInvoices,
              child: Text(
                'إعادة المحاولة',
                style: TextStyle(fontFamily: 'Cairo'),
              ),
            ),
          ],
        ),
      );
    }

    if (_invoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 48.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد فواتير لهذا العميل في هذه الفترة',
              style: TextStyle(
                fontFamily: 'Cairo',
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'يمكنك إنشاء فاتورة جديدة من صفحة تفاصيل العميل',
              style: TextStyle(
                fontFamily: 'Cairo',
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _invoices.length,
      itemBuilder: (context, index) {
        final invoice = _invoices[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: InvoiceCard(
            invoice: invoice,
            onTap: () => _openInvoiceDetails(invoice),
            onCollect: () => _collectInvoice(invoice),
          ),
        );
      },
    );
  }

  double _calculateTotalAmount() {
    return _invoices.fold(0.0, (sum, invoice) => sum + invoice.total);
  }
}
