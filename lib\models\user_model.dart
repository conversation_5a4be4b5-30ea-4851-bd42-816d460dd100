class UserModel {
  final String id;
  final String phone; // الهاتف كمعرف رئيسي
  final String name;
  final String role; // admin, accountant, sales_rep, supervisor
  final String? email; // اختياري
  final String? profileImage;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final Map<String, bool> permissions;

  const UserModel({
    required this.id,
    required this.phone,
    required this.name,
    required this.role,
    this.email,
    this.profileImage,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
    this.permissions = const {},
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      phone: json['phone'] ?? '',
      name: json['name'] ?? '',
      role: json['role'] ?? 'sales_rep',
      email: json['email'],
      profileImage: json['profileImage'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      lastLogin: json['lastLogin'] != null
          ? DateTime.parse(json['lastLogin'])
          : null,
      permissions: Map<String, bool>.from(json['permissions'] ?? {}),
    );
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      phone: map['phone'] ?? '',
      name: map['name'] ?? '',
      role: map['role'] ?? 'sales_rep',
      email: map['email'],
      profileImage: map['profileImage'],
      isActive: map['isActive'] == 1,
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      lastLogin: map['lastLogin'] != null
          ? DateTime.parse(map['lastLogin'])
          : null,
      permissions: Map<String, bool>.from(
        map['permissions'] != null
            ? Map<String, dynamic>.from(
                map['permissions'] is String
                    ? {} // يمكن إضافة parsing للـ JSON string هنا
                    : map['permissions'],
              )
            : {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone': phone,
      'name': name,
      'role': role,
      'email': email,
      'profileImage': profileImage,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
      'permissions': permissions,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'phone': phone,
      'name': name,
      'role': role,
      'email': email,
      'profileImage': profileImage,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
      'permissions': permissions.toString(),
    };
  }

  UserModel copyWith({
    String? id,
    String? phone,
    String? name,
    String? role,
    String? email,
    String? profileImage,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
    Map<String, bool>? permissions,
  }) {
    return UserModel(
      id: id ?? this.id,
      phone: phone ?? this.phone,
      name: name ?? this.name,
      role: role ?? this.role,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      permissions: permissions ?? this.permissions,
    );
  }

  bool hasPermission(String permission) {
    return permissions[permission] ?? false;
  }

  bool get isAdmin => role == 'admin';
  bool get isAccountant => role == 'accountant';
  bool get isSalesRep => role == 'sales_rep';
  bool get isSupervisor => role == 'supervisor';

  String get roleDisplayName {
    switch (role) {
      case 'admin':
        return 'MOHAMED FAYED';
      case 'accountant':
        return 'محاسب';
      case 'sales_rep':
        return 'مندوب مبيعات';
      case 'supervisor':
        return 'مشرف';
      default:
        return 'غير محدد';
    }
  }
}
