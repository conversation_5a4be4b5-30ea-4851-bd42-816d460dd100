import '../constants/app_strings.dart';
import 'customer_model.dart';

class ProductModel {
  final String id;
  final String name;
  final String description;
  final String category;
  final String code;
  final double price;
  final double cost;
  final int quantity;
  final int minQuantity;
  final int piecesPerCarton;
  final String unit;
  final String? barcode;
  final String? imageUrl;
  final String? image;
  final String? createdBy;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // أسعار مختلفة لأنواع العملاء - موزع ومكتب فقط
  final double distributorPrice;
  final double officePrice;

  const ProductModel({
    required this.id,
    required this.name,
    this.description = '',
    required this.category,
    this.code = '',
    required this.price,
    required this.cost,
    required this.quantity,
    required this.minQuantity,
    this.piecesPerCarton = 1,
    required this.unit,
    this.barcode,
    this.imageUrl,
    this.image,
    this.createdBy,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.distributorPrice = 0.0,
    this.officePrice = 0.0,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    // التأكد من أن الوحدة صحيحة
    String unit = json['unit'] ?? 'قطعة';
    if (unit.isEmpty || !AppStrings.allUnits.contains(unit)) {
      unit = AppStrings.piece;
    }

    return ProductModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? 'general_supplies',
      code: json['code'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      cost: (json['cost'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 0,
      minQuantity: json['minQuantity'] ?? 0,
      piecesPerCarton: json['piecesPerCarton'] ?? 1,
      unit: unit,
      barcode: json['barcode'],
      image: json['image'],
      imageUrl: json['imageUrl'],
      createdBy: json['createdBy'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      distributorPrice: (json['distributorPrice'] ?? 0.0).toDouble(),
      officePrice: (json['officePrice'] ?? 0.0).toDouble(),
    );
  }

  factory ProductModel.fromMap(Map<String, dynamic> map) {
    // التأكد من أن الوحدة صحيحة
    String unit = map['unit'] ?? 'قطعة';
    if (unit.isEmpty || !AppStrings.allUnits.contains(unit)) {
      unit = AppStrings.piece;
    }

    return ProductModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? 'general_supplies',
      code: map['code'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      cost: (map['cost'] ?? 0.0).toDouble(),
      quantity: map['quantity'] ?? 0,
      minQuantity: map['minQuantity'] ?? 0,
      piecesPerCarton: map['piecesPerCarton'] ?? 1,
      unit: unit,
      barcode: map['barcode'],
      image: map['image'],
      imageUrl: map['imageUrl'],
      createdBy: map['createdBy'],
      isActive: map['isActive'] == 1,
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      distributorPrice: (map['distributorPrice'] ?? 0.0).toDouble(),
      officePrice: (map['officePrice'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'code': code,
      'price': price,
      'cost': cost,
      'quantity': quantity,
      'minQuantity': minQuantity,
      'piecesPerCarton': piecesPerCarton,
      'unit': unit,
      'barcode': barcode,
      'image': image,
      'imageUrl': imageUrl,
      'createdBy': createdBy,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'distributorPrice': distributorPrice,
      'officePrice': officePrice,
    };
  }

  Map<String, dynamic> toJson() {
    return toMap();
  }

  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? code,
    double? price,
    double? cost,
    int? quantity,
    int? minQuantity,
    int? piecesPerCarton,
    String? unit,
    String? barcode,
    String? imageUrl,
    String? image,
    String? createdBy,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? distributorPrice,
    double? medicalOfficeAPrice,
    double? medicalOfficeBPrice,
    double? majorClientPrice,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      code: code ?? this.code,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      piecesPerCarton: piecesPerCarton ?? this.piecesPerCarton,
      unit: unit ?? this.unit,
      barcode: barcode ?? this.barcode,
      imageUrl: imageUrl ?? this.imageUrl,
      image: image ?? this.image,
      createdBy: createdBy ?? this.createdBy,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      distributorPrice: distributorPrice ?? this.distributorPrice,
      officePrice: officePrice ?? this.officePrice,
    );
  }

  // الحصول على السعر حسب نوع العميل - موزع ومكتب فقط
  double getPriceForCustomerType(String customerType) {
    switch (customerType.toLowerCase()) {
      case 'distributor':
        return distributorPrice > 0 ? distributorPrice : price;
      case 'office':
      case 'medical_office':
        return officePrice > 0 ? officePrice : price;
      default:
        return price;
    }
  }

  // الحصول على السعر حسب الوحدة المختارة
  double getPriceForUnit(String unit, {String? customerType}) {
    // الحصول على السعر الأساسي حسب نوع العميل
    final basePrice = getPriceForCustomerType(customerType ?? 'general');

    switch (unit) {
      case AppStrings.carton:
        // سعر الكرتونة = سعر القطعة × عدد القطع في الكرتونة
        return basePrice * piecesPerCarton;
      case AppStrings.piece:
      default:
        // سعر القطعة = السعر الأساسي
        return basePrice;
    }
  }

  // الحصول على السعر حسب الوحدة للفاتورة (بدون تحديد نوع العميل)
  double getPriceForUnitInInvoice(String unit) {
    switch (unit) {
      case AppStrings.carton:
        // سعر الكرتونة = سعر القطعة × عدد القطع في الكرتونة
        return price * piecesPerCarton;
      case AppStrings.piece:
      default:
        // سعر القطعة = السعر الأساسي
        return price;
    }
  }

  // الحصول على السعر حسب الوحدة ونوع العميل للفاتورة
  double getPriceForUnitAndCustomerType(
    String unit,
    CustomerType customerType,
  ) {
    try {
      double basePrice;

      // تحديد السعر الأساسي حسب نوع العميل
      switch (customerType) {
        case CustomerType.distributor:
          basePrice = distributorPrice > 0 ? distributorPrice : price;
          break;
        case CustomerType.medicalOfficeA:
        case CustomerType.medicalOfficeB:
          basePrice = officePrice > 0 ? officePrice : price;
          break;
        case CustomerType.majorClient:
          basePrice = officePrice > 0 ? officePrice : price;
          break;
        default:
          basePrice = price;
      }

      // التأكد من أن السعر الأساسي صحيح
      if (basePrice <= 0) {
        basePrice = price > 0
            ? price
            : 1.0; // استخدام السعر الأساسي أو 1.0 كبديل
      }

      // حساب السعر حسب الوحدة
      switch (unit) {
        case AppStrings.carton:
          // سعر الكرتونة = سعر القطعة × عدد القطع في الكرتونة
          // التأكد من أن عدد القطع في الكرتونة صحيح
          final cartonPieces = piecesPerCarton > 0 ? piecesPerCarton : 1;
          return basePrice * cartonPieces;
        case AppStrings.piece:
        default:
          // سعر القطعة = السعر الأساسي
          return basePrice;
      }
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع السعر الأساسي
      return price > 0 ? price : 1.0;
    }
  }

  // التحقق من أن الوحدة صحيحة للمنتج
  bool isValidUnit(String unit) {
    return AppStrings.allUnits.contains(unit);
  }

  // الحصول على الوحدات المتاحة لهذا المنتج
  List<String> getAvailableUnits() {
    try {
      final units = [AppStrings.piece];

      // إضافة كرتونة فقط إذا كان عدد القطع في الكرتونة أكبر من 1
      if (piecesPerCarton > 1) {
        units.add(AppStrings.carton);
      }

      return units;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع الوحدات الأساسية
      return [AppStrings.piece];
    }
  }

  // الحصول على نوع العميل حسب السعر - موزع ومكتب فقط
  String getCustomerTypeForPrice(double price) {
    if (price == distributorPrice && distributorPrice > 0) return 'distributor';
    if (price == officePrice && officePrice > 0) return 'office';
    return 'general';
  }

  // التحقق من صحة الأسعار - موزع ومكتب فقط
  bool get hasValidPrices => distributorPrice > 0 || officePrice > 0;

  // الحصول على السعر الأقل - موزع ومكتب فقط
  double get lowestPrice {
    final prices = [
      distributorPrice,
      officePrice,
    ].where((price) => price > 0).toList();
    if (prices.isEmpty) return price;
    return prices.reduce((a, b) => a < b ? a : b);
  }

  // الحصول على السعر الأعلى - موزع ومكتب فقط
  double get highestPrice {
    final prices = [
      distributorPrice,
      officePrice,
    ].where((price) => price > 0).toList();
    if (prices.isEmpty) return price;
    return prices.reduce((a, b) => a > b ? a : b);
  }

  // حساب هامش الربح
  double get profitMargin => cost > 0 ? ((price - cost) / cost) * 100 : 0;

  // التحقق من توفر المنتج
  bool get isAvailable => quantity > 0 && isActive;

  // التحقق من انخفاض المخزون
  bool get isLowStock => quantity <= minQuantity;

  // الحصول على حالة المخزون
  String get stockStatus {
    if (quantity == 0) return 'نفذ';
    if (isLowStock) return 'منخفض';
    return 'متوفر';
  }

  // الحصول على معلومات المنتج الأساسية
  Map<String, dynamic> get basicInfo => {
    'id': id,
    'name': name,
    'code': code,
    'category': category,
    'price': price,
    'quantity': quantity,
    'unit': unit,
  };

  // الحصول على معلومات الأسعار
  Map<String, dynamic> get pricingInfo => {
    'distributorPrice': distributorPrice,
    'officePrice': officePrice,
    'lowestPrice': lowestPrice,
    'highestPrice': highestPrice,
  };

  // الحصول على معلومات المخزون
  Map<String, dynamic> get inventoryInfo => {
    'quantity': quantity,
    'minQuantity': minQuantity,
    'piecesPerCarton': piecesPerCarton,
    'stockStatus': stockStatus,
    'isAvailable': isAvailable,
    'isLowStock': isLowStock,
  };

  // الحصول على اسم الفئة المعروض
  String get categoryDisplayName {
    switch (category) {
      case 'devices':
        return 'أجهزة طبية';
      case 'consumables':
        return 'مستهلكات';
      case 'sterilization':
        return 'معقمات';
      case 'laboratory':
        return 'مستلزمات معمل';
      case 'general_supplies':
        return 'مستلزمات عامة';
      default:
        return 'غير محدد';
    }
  }

  // التحقق من وجود باركود
  bool get hasBarcode => barcode != null && barcode!.isNotEmpty;

  // الحصول على اسم الوحدة المعروض
  String get unitDisplayName {
    switch (unit) {
      case 'piece':
        return 'قطعة';
      case 'box':
        return 'علبة';
      case 'pack':
        return 'عبوة';
      case 'bottle':
        return 'زجاجة';
      case 'tube':
        return 'أنبوبة';
      case 'roll':
        return 'لفة';
      case 'meter':
        return 'متر';
      case 'liter':
        return 'لتر';
      case 'gram':
        return 'جرام';
      case 'milliliter':
        return 'ملليلتر';
      default:
        return unit;
    }
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, code: $code, category: $category, price: $price, distributorPrice: $distributorPrice, officePrice: $officePrice, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.category == category;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ code.hashCode ^ category.hashCode;
  }
}
