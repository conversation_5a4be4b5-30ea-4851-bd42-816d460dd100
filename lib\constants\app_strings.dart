class AppStrings {
  // العامة
  static const String appName = 'Atlas Medical Supplies';
  static const String appSubtitle = 'نظام إدارة توزيع المستلزمات الطبية';
  static const String version = 'الإصدار 1.0.0';

  // التنقل والواجهة
  static const String dashboard = 'لوحة التحكم';
  static const String customers = 'العملاء';
  static const String products = 'المنتجات';
  static const String inventory = 'المخزون';

  static const String returns = 'المرتجعات';
  static const String reports = 'التقارير';
  static const String settings = 'الإعدادات';
  static const String profile = 'الملف الشخصي';

  // تسجيل الدخول والمصادقة
  static const String login = 'تسجيل الدخول';
  static const String logout = 'تسجيل الخروج';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String rememberMe = 'تذكرني';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String welcomeBack = 'أهلاً بعودتك';
  static const String enterCredentials = 'أدخل بيانات الدخول للمتابعة';

  // العمليات العامة
  static const String add = 'إضافة';
  static const String edit = 'تعديل';
  static const String delete = 'حذف';
  static const String save = 'حفظ';
  static const String cancel = 'إلغاء';
  static const String confirm = 'تأكيد';
  static const String submit = 'إرسال';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  static const String sort = 'ترتيب';
  static const String clear = 'مسح';
  static const String refresh = 'تحديث';
  static const String loading = 'جارٍ التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String warning = 'تحذير';
  static const String info = 'معلومات';

  // إدارة العملاء
  static const String addCustomer = 'إضافة عميل جديد';
  static const String editCustomer = 'تعديل بيانات العميل';
  static const String customerName = 'اسم العميل';
  static const String customerType = 'نوع العميل';
  static const String customerActivity = 'النشاط';
  static const String customerAddress = 'العنوان';
  static const String customerPhone = 'رقم الهاتف';
  static const String customerSecondPhone = 'رقم هاتف آخر';
  static const String customerNotes = 'ملاحظات';
  static const String customerBalance = 'الرصيد';
  static const String customerLocation = 'الموقع';
  static const String customerGovernorate = 'المحافظة';
  static const String customerCity = 'المدينة/المركز';
  static const String customerVillage = 'القرية';

  // أنواع العملاء
  static const String distributor = 'موزع';
  static const String medicalOfficeA = 'مكتب طبي A';
  static const String medicalOfficeB = 'مكتب طبي B';
  static const String majorClient = 'عميل كبير';

  // إدارة المنتجات
  static const String addProduct = 'إضافة منتج جديد';
  static const String editProduct = 'تعديل المنتج';
  static const String productName = 'اسم المنتج';
  static const String productCode = 'كود المنتج';
  static const String productDescription = 'وصف المنتج';
  static const String productPrice = 'السعر';
  static const String productUnit = 'الوحدة';
  static const String productCategory = 'الفئة';
  static const String productImage = 'صورة المنتج';
  static const String productBarcode = 'الباركود';
  static const String piecesPerCarton = 'عدد القطع بالكرتونة';

  // فئات المنتجات
  static const String devices = 'أجهزة طبية';
  static const String consumables = 'مستهلكات';
  static const String sterilization = 'معقمات';
  static const String laboratory = 'مستلزمات معمل';
  static const String generalSupplies = 'مستلزمات عامة';

  // إدارة المخزون
  static const String totalQuantity = 'الكمية الإجمالية';
  static const String availableQuantity = 'الكمية المتاحة';
  static const String reservedQuantity = 'الكمية المحجوزة';
  static const String lowStock = 'مخزون منخفض';
  static const String outOfStock = 'نفد المخزون';
  static const String stockAlert = 'تنبيه مخزون';
  static const String warehouse = 'المخزن';
  static const String mainWarehouse = 'المخزن الرئيسي';
  static const String branchWarehouse = 'مخزن الفرع';
  static const String salesCarWarehouse = 'مخزن سيارة المندوب';

  // المرتجعات
  static const String addReturn = 'تسجيل مرتجع';
  static const String returnReason = 'سبب المرتجع';
  static const String returnQuantity = 'كمية المرتجع';
  static const String returnValue = 'قيمة المرتجع';
  static const String returnNotes = 'ملاحظات المرتجع';
  static const String returnImage = 'صورة المرتجع';

  // التقارير
  static const String dailyReport = 'تقرير يومي';
  static const String monthlyReport = 'تقرير شهري';
  static const String salesReport = 'تقرير المبيعات';

  static const String inventoryReport = 'تقرير المخزون';
  static const String customersReport = 'تقرير العملاء';
  static const String productsReport = 'تقرير المنتجات';

  // الإعدادات
  static const String generalSettings = 'الإعدادات العامة';
  static const String userManagement = 'إدارة المستخدمين';
  static const String permissions = 'الصلاحيات';
  static const String darkMode = 'الوضع المظلم';
  static const String language = 'اللغة';
  static const String notifications = 'الإشعارات';
  static const String backup = 'النسخ الاحتياطي';
  static const String about = 'حول التطبيق';

  // رسائل التأكيد والأخطاء
  static const String confirmDeleteGeneral = 'هل أنت متأكد من الحذف؟';
  static const String confirmLogout = 'هل تريد تسجيل الخروج؟';
  static const String deleteSuccessGeneral = 'تم الحذف بنجاح';
  static const String saveSuccess = 'تم الحفظ بنجاح';
  static const String updateSuccess = 'تم التحديث بنجاح';
  static const String connectionError = 'خطأ في الاتصال';
  static const String invalidCredentials = 'بيانات دخول غير صحيحة';
  static const String requiredField = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'بريد إلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';

  // الوحدات
  static const String piece = 'قطعة';
  static const String carton = 'كرتونة';
  static const String kg = 'كيلوجرام';
  static const String liter = 'لتر';
  static const String meter = 'متر';
  static const String pack = 'عبوة';
  static const String gram = 'جرام';
  static const String cm = 'سم';
  static const String mm = 'مم';
  static const String set = 'مجموعة';
  static const String dozen = 'دستة';
  static const String strip = 'شريط';
  static const String tablet = 'قرص';
  static const String capsule = 'كبسولة';
  static const String syringe = 'حقنة';
  static const String ampoule = 'أمبول';

  // قائمة الوحدات الكاملة والمتسقة
  static const List<String> allUnits = [
    piece,
    carton,
    kg,
    liter,
    meter,
    pack,
    gram,
    cm,
    mm,
    set,
    dozen,
    strip,
    tablet,
    capsule,
    syringe,
    ampoule,
  ];

  // العملة
  static const String currency = 'ريال';
  static const String currencySymbol = 'ر.س';

  // التواريخ
  static const String today = 'اليوم';
  static const String yesterday = 'أمس';
  static const String thisWeek = 'هذا الأسبوع';
  static const String thisMonth = 'هذا الشهر';
  static const String thisYear = 'هذا العام';

  // أدوار المستخدمين
  static const String admin = 'MOHAMED FAYED';
  static const String accountant = 'محاسب';
  static const String salesRep = 'مندوب مبيعات';
  static const String supervisor = 'مشرف';

  // نظام الحماية بكلمة المرور
  static const String deleteConfirmation = 'تأكيد الحذف';
  static const String passwordRequired = 'كلمة المرور مطلوبة';
  static const String enterPassword = 'أدخل كلمة المرور';
  static const String passwordHint = 'كلمة المرور';
  static const String confirmDeleteProtected = 'تأكيد الحذف';
  static const String cancelAction = 'إلغاء';
  static const String wrongPassword = 'كلمة المرور غير صحيحة';
  static const String deleteSuccessProtected = 'تم الحذف بنجاح';
  static const String deleteFailed = 'فشل في الحذف';
  static const String deleteWarning = 'تحذير: لا يمكن التراجع عن هذه العملية';
}
