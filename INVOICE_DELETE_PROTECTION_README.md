# نظام حماية حذف الفواتير - ATLAS2

## نظرة عامة

تم تطبيق نظام حماية شامل لحذف الفواتير في التطبيق، حيث يتطلب إدخال كلمة المرور الخاصة بالمستخدم قبل تنفيذ عملية الحذف.

## الميزات

### 🔒 حماية شاملة للفواتير
- ✅ حماية حذف الفواتير في شاشة التفاصيل
- ✅ حماية حذف الفواتير في الشاشة الرئيسية
- ✅ حماية حذف الفواتير في شاشة الفواتير حسب المحافظات
- ✅ حماية حذف الفواتير في بطاقات الفواتير

### 🛡️ نظام مصادقة آمن
- كلمة مرور مخصصة للحذف
- تشفير كلمة المرور
- تخزين آمن في التخزين المحلي
- التحقق من صحة كلمة المرور

### ⚙️ إدارة سهلة
- تعيين كلمة المرور من شاشة الإعدادات
- تغيير كلمة المرور
- إعادة تعيين كلمة المرور
- واجهة مستخدم بسيطة وواضحة

## كيفية الاستخدام

### 1. تعيين كلمة المرور

1. انتقل إلى **الإعدادات** → **الأمان والحماية**
2. ابحث عن قسم **"كلمة المرور للحذف"**
3. اضغط على **"تعيين كلمة المرور"**
4. أدخل كلمة المرور الجديدة (6 أحرف على الأقل)
5. أكد كلمة المرور
6. اضغط **"حفظ"**

### 2. حذف الفواتير

عند محاولة حذف أي فاتورة:

1. سيظهر حوار **"حماية حذف الفاتورة"**
2. أدخل كلمة المرور الخاصة بك
3. اضغط **"تأكيد الحذف"**
4. سيتم تنفيذ عملية الحذف

## الشاشات المحمية

### 📱 شاشة تفاصيل الفاتورة
- **الموقع**: `lib/features/invoices/screens/invoice_details_screen.dart`
- **الوظيفة**: حذف الفاتورة من شاشة التفاصيل
- **الحماية**: ✅ مفعلة
- **الشرط**: الفاتورة يجب أن تكون في حالة "معلق" (`pending`)

### 📱 الشاشة الرئيسية للفواتير
- **الموقع**: `lib/features/invoices/screens/invoices_screen.dart`
- **الوظيفة**: حذف الفاتورة من القائمة الرئيسية
- **الحماية**: ✅ مفعلة

### 📱 شاشة الفواتير حسب المحافظات
- **الموقع**: `lib/features/invoices/screens/governorate_invoices_screen.dart`
- **الوظيفة**: حذف الفاتورة من قائمة المحافظة
- **الحماية**: ✅ مفعلة

### 📱 بطاقات الفواتير
- **الموقع**: `lib/features/invoices/widgets/invoice_card.dart`
- **الوظيفة**: حذف الفاتورة من البطاقة
- **الحماية**: ✅ مفعلة

## الملفات المحدثة

### ملفات معدلة
```
lib/features/invoices/screens/invoice_details_screen.dart
lib/features/invoices/screens/invoices_screen.dart
lib/features/invoices/screens/governorate_invoices_screen.dart
lib/features/invoices/widgets/invoice_card.dart
```

### ملفات مستخدمة
```
lib/widgets/delete_protection_dialog.dart
lib/core/settings/app_settings.dart
```

## التفاصيل التقنية

### نظام إدارة كلمة المرور
- تخزين آمن في SharedPreferences
- تشفير كلمة المرور
- دوال للتحقق والتحديث والحذف

### واجهة المستخدم
- حوار حماية موحد لجميع عمليات حذف الفواتير
- رسائل خطأ واضحة
- تصميم متجاوب ومتسق

### الأمان
- التحقق من طول كلمة المرور (6 أحرف على الأقل)
- التحقق من تطابق كلمة المرور
- معالجة الأخطاء بشكل آمن

## شروط الحذف

### الفواتير القابلة للحذف
- الفواتير في حالة "معلق" (`pending`) فقط
- الفواتير التي لم يتم دفع أي مبلغ عليها
- الفواتير التي لم يتم تحصيل أي مبلغ منها

### الفواتير غير القابلة للحذف
- الفواتير المدفوعة بالكامل
- الفواتير التي تم تحصيل جزء منها
- الفواتير الملغية

## كيفية التطوير

### إضافة الحماية لشاشة جديدة

```dart
// 1. استيراد الملفات المطلوبة
import '../../../widgets/delete_protection_dialog.dart';
import '../../../core/settings/app_settings.dart';

// 2. إنشاء دالة الحذف
Future<void> _deleteInvoice(InvoiceModel invoice) async {
  final appSettings = AppSettings();
  final isDeleteProtected = await appSettings.isPasswordRequiredForInvoiceDelete();
  
  if (isDeleteProtected) {
    showDeleteProtectionDialog(
      context: context,
      title: 'حماية حذف الفاتورة',
      message: 'حذف الفاتورة محمي بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة.',
      itemName: 'الفاتورة رقم ${invoice.invoiceNumber}',
      onConfirm: () async {
        // منطق الحذف هنا
        await _invoiceService.deleteInvoice(invoice.id);
      },
    );
  } else {
    // حوار التأكيد العادي
    // ...
  }
}
```

### التحقق من إمكانية الحذف

```dart
// في نموذج الفاتورة
bool get canDelete => status == InvoiceStatus.pending;

// في الواجهة
if (invoice.canDelete) {
  // عرض زر الحذف
}
```

## اختبار النظام

### اختبارات مطلوبة
- [ ] تعيين كلمة المرور
- [ ] تغيير كلمة المرور
- [ ] حذف فاتورة معلقة
- [ ] محاولة حذف فاتورة مدفوعة
- [ ] اختبار كلمة المرور الخاطئة
- [ ] اختبار عدم تعيين كلمة المرور
- [ ] اختبار الحذف من جميع الشاشات

## الأمان والخصوصية

### حماية البيانات
- كلمة المرور مشفرة في التخزين المحلي
- لا يتم إرسال كلمة المرور عبر الشبكة
- التحقق المحلي من صحة كلمة المرور

### إدارة الجلسة
- كلمة المرور مطلوبة لكل عملية حذف
- لا يتم حفظ حالة المصادقة
- إعادة طلب كلمة المرور عند كل عملية

## الدعم والمساعدة

### في حالة نسيان كلمة المرور
1. انتقل إلى الإعدادات
2. ابحث عن قسم "كلمة المرور للحذف"
3. اضغط "إعادة تعيين كلمة المرور"
4. أدخل كلمة المرور الجديدة

### في حالة المشاكل التقنية
- تأكد من أن التطبيق محدث لأحدث إصدار
- أعد تشغيل التطبيق
- امسح بيانات التطبيق إذا لزم الأمر

## التحديثات المستقبلية

### الميزات المخطط لها
- [ ] دعم البصمة للحذف
- [ ] تسجيل عمليات الحذف
- [ ] تأكيد مزدوج للحذف
- [ ] حماية إضافية للفواتير المهمة

### التحسينات المقترحة
- [ ] واجهة مستخدم محسنة
- [ ] رسائل خطأ أكثر وضوحاً
- [ ] دعم اللغات الإضافية
- [ ] تخصيص رسائل الحماية
