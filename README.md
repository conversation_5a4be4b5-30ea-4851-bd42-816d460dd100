# أطلس للمستلزمات الطبية
## Atlas Medical Supplies

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-039BE5?style=for-the-badge&logo=Firebase&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white)

**نظام إدارة شامل لشركات توزيع المستلزمات الطبية في مصر**

</div>

---

## 📋 نظرة عامة

تطبيق أطلس للمستلزمات الطبية هو نظام إدارة متكامل مصمم خصيصاً لشركات ومحلات توزيع المستلزمات الطبية في مصر. يخدم التطبيق جميع أنواع المستخدمين من مندوبي المبيعات والمحاسبين إلى المشرفين والإدارة.

## ✨ الميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن باستخدام Firebase Authentication
- أدوار متعددة: مدير، محاسب، مندوب مبيعات، مشرف
- صلاحيات مخصصة لكل دور
- إمكانية تغيير كلمة المرور واستعادتها

### 🏠 لوحة التحكم التفاعلية
- إحصائيات مباشرة ومحدثة لحظياً
- رسوم بيانية تفاعلية للمبيعات والتحصيل
- تنبيهات ذكية لانخفاض المخزون والدفعات المتأخرة
- الأنشطة الأخيرة والإجراءات السريعة

### 👥 إدارة العملاء الشاملة
- إضافة وتعديل بيانات العملاء
- تصنيف العملاء (موزع، مكتب طبي A/B، عميل كبير)
- تتبع الأرصدة والمدفوعات
- نظام عناوين تفصيلي بالمحافظات والمدن
- إمكانية تحديد الموقع الجغرافي

### 📦 إدارة المنتجات والمخزون
- كتالوج منتجات شامل مع الصور والأوصاف
- نظام أسعار متعدد حسب نوع العميل
- تتبع المخزون الفوري
- تنبيهات نقص المخزون
- دعم الباركود والرموز المرجعية

### 💰 نظام الفواتير المتطور
- إنشاء فواتير احترافية
- حساب تلقائي للأسعار والضرائب
- دعم البيع بالقطعة والكرتونة
- طباعة ومشاركة الفواتير
- تتبع حالة الدفع والمبالغ المستحقة

### 💸 إدارة التحصيل
- تسجيل المدفوعات النقدية والتحويلات
- متابعة الأرصدة المفتوحة
- تقارير التحصيل المفصلة
- تنبيهات الدفعات المتأخرة

### 🔄 نظام المرتجعات
- تسجيل المرتجعات مع الأسباب
- رفع صور للمنتجات المرتجعة
- إعادة الكميات للمخزون تلقائياً
- تتبع قيم المرتجعات

### 📊 التقارير والتحليلات
- تقارير شاملة للمبيعات والتحصيل
- إحصائيات العملاء والمنتجات
- رسوم بيانية تفاعلية
- تصدير التقارير بصيغة PDF وExcel

## 🛠 التقنيات المستخدمة

### Frontend
- **Flutter** - إطار العمل الأساسي
- **Dart** - لغة البرمجة
- **Flutter ScreenUtil** - للتصميم المتجاوب
- **Provider/Riverpod** - إدارة الحالة

### Backend & Database
- **Firebase Core** - الخدمات الأساسية
- **Firebase Auth** - المصادقة والأمان
- **Cloud Firestore** - قاعدة البيانات السحابية
- **Firebase Storage** - تخزين الملفات والصور
- **Firebase Messaging** - الإشعارات الفورية

### UI & Charts
- **FL Chart** - الرسوم البيانية
- **Syncfusion Charts** - مخططات متقدمة
- **Cached Network Image** - تحسين عرض الصور
- **Lottie** - الرسوم المتحركة

### Utilities
- **PDF & Printing** - طباعة وتصدير التقارير
- **Image Picker** - اختيار الصور
- **QR Flutter** - إنشاء أكواد QR
- **Mobile Scanner** - قراءة الباركود
- **Geolocator** - تحديد المواقع
- **URL Launcher** - فتح الروابط

## 🎨 التصميم والتجربة

### التصميم العربي الأصيل
- دعم كامل للغة العربية واتجاه RTL
- خطوط عربية واضحة (Cairo, Tajawal)
- ألوان مستوحاة من الهوية المصرية
- تجربة مستخدم بديهية ومألوفة

### التجاوب والمرونة
- تصميم متجاوب لجميع الأحجام
- دعم الهواتف والأجهزة اللوحية والديسكتوب
- الوضع الليلي والنهاري
- أيقونات حديثة ومعبرة

## 🚀 التثبيت والتشغيل

### المتطلبات
```bash
Flutter SDK >= 3.8.1
Dart SDK >= 3.8.1
```

### خطوات التثبيت

1. **نسخ المشروع**
```bash
git clone https://github.com/your-repo/atlas-medical-supplies.git
cd atlas-medical-supplies
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Firebase**
- أنشئ مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
- فعّل Authentication و Firestore و Storage
- حمّل ملفات الإعداد (google-services.json للأندرويد، GoogleService-Info.plist لـ iOS)
- حدّث firebase_options.dart بمعلومات مشروعك

4. **تشغيل التطبيق**
```bash
flutter run
```

## 📱 المنصات المدعومة

- ✅ **Android** (API 21+)
- ✅ **iOS** (iOS 12.0+)
- ✅ **Web** (Chrome, Firefox, Safari, Edge)
- ✅ **Windows** (Windows 10+)
- ✅ **macOS** (macOS 10.14+)
- ✅ **Linux** (Ubuntu 18.04+)

## 🏗 البنية والتنظيم

```
lib/
├── constants/          # الثوابت والألوان والنصوص
├── models/            # نماذج البيانات
├── services/          # خدمات Firebase والواجهات البرمجية
├── features/          # ميزات التطبيق
│   ├── auth/         # المصادقة وتسجيل الدخول
│   ├── dashboard/    # لوحة التحكم الرئيسية
│   ├── customers/    # إدارة العملاء
│   ├── products/     # إدارة المنتجات
│   ├── inventory/    # إدارة المخزون
│   ├── invoices/     # الفواتير
│   ├── collection/   # التحصيل
│   ├── returns/      # المرتجعات
│   ├── reports/      # التقارير
│   └── settings/     # الإعدادات
├── shared/           # الملفات المشتركة
├── widgets/          # الواجهات المعاد استخدامها
└── utils/            # الأدوات المساعدة
```

## 🔒 الأمان والخصوصية

- تشفير جميع البيانات الحساسة
- مصادقة متعددة المستويات
- نسخ احتياطية تلقائية
- حماية من SQL Injection و XSS
- صلاحيات مجزأة حسب الدور

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'إضافة ميزة رائعة'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 123 456 7890
- **الموقع**: [www.atlas-medical.com](https://www.atlas-medical.com)

---

<div align="center">

**صُنع بـ ❤️ للمجتمع الطبي المصري**

إبداع محلي، جودة عالمية 🇪🇬

</div>