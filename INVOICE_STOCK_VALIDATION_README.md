# تحديث التحقق من الكميات المتوفرة في المخزون

## الوصف
تم إضافة تحقق لمنع إضافة كمية منتج في الفاتورة تتجاوز الكمية المتوفرة في المخزون. هذا يضمن عدم بيع منتجات غير متوفرة ويحافظ على دقة المخزون.

## التغييرات المطبقة

### 1. Widget تعديل عنصر الفاتورة (`invoice_item_form.dart`)
- **جلب معلومات المنتج**: تم إضافة جلب معلومات المنتج من قاعدة البيانات للتحقق من الكمية المتوفرة.
- **التحقق المباشر**: تم إضافة تحقق فوري عند تغيير الكمية لعرض رسالة خطأ إذا تجاوزت الكمية المخزون المتوفر.
- **عرض معلومات المخزون**: تم إضافة عرض الكمية المتوفرة في المخزون لكل منتج.
- **رسائل الخطأ**: تم إضافة رسائل خطأ واضحة عند تجاوز الكمية المطلوبة للمخزون المتوفر.

### 2. شاشة إضافة الفاتورة (`add_invoice_screen.dart`)
- **التحقق قبل الحفظ**: تم إضافة تحقق شامل لجميع المنتجات في الفاتورة قبل الحفظ.
- **رسائل خطأ مفصلة**: تم إضافة رسائل خطأ مفصلة تظهر جميع المنتجات التي تتجاوز كمياتها المخزون المتوفر.

### 3. خدمة الفواتير (`invoice_service.dart`)
- **التحقق في إنشاء الفاتورة**: تم إضافة تحقق في دالة `createInvoice()` لرفع استثناء إذا كانت الكميات تتجاوز المخزون.
- **التحقق في تحديث الفاتورة**: تم إضافة تحقق في دالة `updateInvoice()` لرفع استثناء إذا كانت الكميات تتجاوز المخزون.

## كيفية عمل التحقق

### في واجهة المستخدم
1. **عند إضافة منتج**: يتم عرض الكمية المتوفرة في المخزون لكل منتج.
2. **عند تغيير الكمية**: يتم التحقق الفوري وعرض رسالة خطأ إذا تجاوزت الكمية المخزون المتوفر.
3. **عند الحفظ**: يتم التحقق من جميع المنتجات في الفاتورة قبل الحفظ.

### في مستوى الخدمة
1. يتم التحقق من الكميات قبل إنشاء أو تحديث الفاتورة.
2. إذا كانت الكميات تتجاوز المخزون، يتم رفع استثناء مع رسالة مفصلة.

## الكود المضافة

### جلب معلومات المنتج
```dart
Future<void> _loadProductInfo() async {
  try {
    _product = await _productService.getProductById(widget.item.productId);
    setState(() {});
  } catch (e) {
    debugPrint('خطأ في جلب معلومات المنتج: $e');
  }
}
```

### التحقق من الكمية
```dart
void _updateTotal() {
  final quantity = int.tryParse(_quantityController.text) ?? 0;
  final price = double.tryParse(_priceController.text) ?? 0.0;
  
  // التحقق من الكمية المتوفرة
  String errorMessage = '';
  if (_product != null && quantity > _product!.quantity) {
    errorMessage = 'الكمية المطلوبة (${quantity}) تتجاوز الكمية المتوفرة (${_product!.quantity})';
    setState(() {
      _errorMessage = errorMessage;
    });
    return;
  } else {
    setState(() {
      _errorMessage = '';
    });
  }
  
  // ... باقي الكود
}
```

### التحقق في الحفظ
```dart
// التحقق من الكميات المتوفرة في المخزون
final List<String> insufficientStockItems = [];

for (final item in _invoiceItems) {
  try {
    final product = await _productService.getProductById(item.productId);
    if (product != null && item.quantity > product.quantity) {
      insufficientStockItems.add('${item.productName}: المطلوب ${item.quantity}، المتوفر ${product.quantity}');
    }
  } catch (e) {
    debugPrint('خطأ في التحقق من مخزون المنتج ${item.productName}: $e');
  }
}

if (insufficientStockItems.isNotEmpty) {
  final errorMessage = 'الكميات المطلوبة تتجاوز المخزون المتوفر:\n${insufficientStockItems.join('\n')}';
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(errorMessage),
      backgroundColor: Colors.red,
      duration: const Duration(seconds: 5),
    ),
  );
  return;
}
```

### التحقق في الخدمة
```dart
// التحقق من الكميات المتوفرة في المخزون
final List<String> insufficientStockItems = [];

for (final item in items) {
  try {
    final product = await _databaseService.getProductById(item.productId);
    if (product != null && item.quantity > product.quantity) {
      insufficientStockItems.add('${item.productName}: المطلوب ${item.quantity}، المتوفر ${product.quantity}');
    }
  } catch (e) {
    debugPrint('خطأ في التحقق من مخزون المنتج ${item.productName}: $e');
  }
}

if (insufficientStockItems.isNotEmpty) {
  final errorMessage = 'الكميات المطلوبة تتجاوز المخزون المتوفر:\n${insufficientStockItems.join('\n')}';
  throw Exception(errorMessage);
}
```

## الميزات الجديدة

### 1. عرض معلومات المخزون
- يتم عرض الكمية المتوفرة لكل منتج في الفاتورة
- لون أخضر للمنتجات المتوفرة، برتقالي للمنتجات منخفضة المخزون

### 2. التحقق الفوري
- يتم التحقق من الكمية فوراً عند تغييرها
- رسائل خطأ واضحة ومباشرة

### 3. التحقق الشامل
- يتم التحقق من جميع المنتجات قبل حفظ الفاتورة
- رسائل خطأ مفصلة تظهر جميع المشاكل

### 4. حماية على مستوى الخدمة
- التحقق موجود في مستوى الخدمة لمنع أي تجاوز
- رسائل خطأ مفصلة في الاستثناءات

## الفوائد
1. **منع البيع الزائد**: يمنع بيع منتجات غير متوفرة في المخزون.
2. **دقة المخزون**: يحافظ على دقة معلومات المخزون.
3. **تجربة مستخدم أفضل**: يوفر رسائل واضحة ومعلومات مفيدة.
4. **حماية من الأخطاء**: يمنع الأخطاء البشرية في إدخال الكميات.

## التوافق
- هذا التحديث متوافق مع جميع الإصدارات السابقة.
- لا يؤثر على الفواتير الموجودة مسبقاً.
- يعمل مع جميع أنواع المنتجات.

## الاختبار
يجب اختبار:
1. إضافة منتج بكمية أقل من المخزون المتوفر (يجب أن يعمل).
2. إضافة منتج بكمية تساوي المخزون المتوفر (يجب أن يعمل).
3. إضافة منتج بكمية أكبر من المخزون المتوفر (يجب أن يظهر خطأ).
4. محاولة حفظ فاتورة تحتوي على منتجات تتجاوز كمياتها المخزون (يجب أن يفشل).
5. عرض معلومات المخزون بشكل صحيح لجميع المنتجات.

## ملاحظات مهمة
- يتم التحقق من المخزون في الوقت الفعلي
- إذا تم تحديث المخزون من مكان آخر، سيتم رؤية التحديث فوراً
- الرسائل واضحة ومفصلة لتسهيل حل المشاكل
