import 'invoice_model.dart';

class SalesModel {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime invoiceDate;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final InvoiceStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalesModel({
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.invoiceDate,
    required this.totalAmount,
    required this.paidAmount,
    required this.remainingAmount,
    required this.status,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalesModel.fromInvoice(InvoiceModel invoice) {
    return SalesModel(
      id: invoice.id,
      invoiceId: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      customerId: invoice.customerId,
      customerName: invoice.customerName,
      customerPhone: invoice.customerPhone,
      invoiceDate: invoice.invoiceDate,
      totalAmount: invoice.total,
      paidAmount: invoice.paidAmount,
      remainingAmount: invoice.calculateRemainingAmount(),
      status: invoice.status,
      notes: invoice.notes,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
    );
  }

  factory SalesModel.fromJson(Map<String, dynamic> json) {
    return SalesModel(
      id: json['id'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        json['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      totalAmount: (json['totalAmount'] ?? 0.0).toDouble(),
      paidAmount: (json['paidAmount'] ?? 0.0).toDouble(),
      remainingAmount: (json['remainingAmount'] ?? 0.0).toDouble(),
      status: _parseStatus(json['status']),
      notes: json['notes'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  factory SalesModel.fromMap(Map<String, dynamic> map) {
    return SalesModel(
      id: map['id'] ?? '',
      invoiceId: map['invoiceId'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      invoiceDate: DateTime.parse(
        map['invoiceDate'] ?? DateTime.now().toIso8601String(),
      ),
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      paidAmount: (map['paidAmount'] ?? 0.0).toDouble(),
      remainingAmount: (map['remainingAmount'] ?? 0.0).toDouble(),
      status: _parseStatus(map['status']),
      notes: map['notes'],
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  static InvoiceStatus _parseStatus(dynamic statusValue) {
    if (statusValue == null) return InvoiceStatus.pending;

    if (statusValue is int) {
      switch (statusValue) {
        case 0:
          return InvoiceStatus.pending;
        case 1:
          return InvoiceStatus.paid;
        case 2:
          return InvoiceStatus.partial;
        case 3:
          return InvoiceStatus.cancelled;
        default:
          return InvoiceStatus.pending;
      }
    } else if (statusValue is String) {
      switch (statusValue) {
        case '0':
          return InvoiceStatus.pending;
        case '1':
          return InvoiceStatus.paid;
        case '2':
          return InvoiceStatus.partial;
        case '3':
          return InvoiceStatus.cancelled;
        case 'معلق':
          return InvoiceStatus.pending;
        case 'مدفوعة':
          return InvoiceStatus.paid;
        case 'دفع جزئي':
          return InvoiceStatus.partial;
        case 'ملغي':
          return InvoiceStatus.cancelled;
        default:
          return InvoiceStatus.pending;
      }
    }

    return InvoiceStatus.pending;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'remainingAmount': remainingAmount,
      'status': status.index,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'invoiceDate': invoiceDate.toIso8601String(),
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'remainingAmount': remainingAmount,
      'status': status.index,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  SalesModel copyWith({
    String? id,
    String? invoiceId,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    DateTime? invoiceDate,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    InvoiceStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalesModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // الحصول على حالة الدفع
  String get paymentStatus {
    if (status == InvoiceStatus.paid) {
      return 'مدفوع بالكامل';
    } else if (status == InvoiceStatus.partial) {
      return 'مدفوع جزئياً';
    } else if (status == InvoiceStatus.cancelled) {
      return 'ملغي';
    } else {
      return 'غير مدفوع';
    }
  }

  // الحصول على لون حالة الدفع
  String get statusColor {
    switch (status) {
      case InvoiceStatus.pending:
        return '#FFA500'; // برتقالي
      case InvoiceStatus.paid:
        return '#4CAF50'; // أخضر
      case InvoiceStatus.partial:
        return '#2196F3'; // أزرق
      case InvoiceStatus.cancelled:
        return '#F44336'; // أحمر
    }
  }

  // الحصول على تاريخ الفاتورة المنسق
  String get formattedDate {
    return '${invoiceDate.day}/${invoiceDate.month}/${invoiceDate.year}';
  }

  // التحقق من اكتمال الدفع
  bool get isFullyPaid => status == InvoiceStatus.paid;

  // حساب نسبة الدفع
  double get paymentPercentage {
    if (totalAmount > 0) {
      return (paidAmount / totalAmount) * 100;
    }
    return 0.0;
  }

  @override
  String toString() {
    return 'SalesModel(\n'
        '  id: "$id",\n'
        '  invoiceNumber: "$invoiceNumber",\n'
        '  customerName: "$customerName",\n'
        '  totalAmount: $totalAmount,\n'
        '  paidAmount: $paidAmount,\n'
        '  remainingAmount: $remainingAmount,\n'
        '  status: ${status.displayName},\n'
        '  invoiceDate: ${invoiceDate.toIso8601String()},\n'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalesModel &&
        other.id == id &&
        other.invoiceNumber == invoiceNumber;
  }

  @override
  int get hashCode {
    return id.hashCode ^ invoiceNumber.hashCode;
  }
}
