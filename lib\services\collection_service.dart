import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../models/collection_model.dart';
import '../models/invoice_model.dart';
import 'database_service.dart';
import 'invoice_service.dart';

class CollectionService {
  static final CollectionService _instance = CollectionService._internal();
  factory CollectionService() => _instance;
  CollectionService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final InvoiceService _invoiceService = InvoiceService();
  final Uuid _uuid = const Uuid();

  /// الحصول على جميع عمليات التحصيل
  Future<List<CollectionModel>> getAllCollections() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'collections',
        orderBy: 'paymentDate DESC',
      );

      return List.generate(maps.length, (i) {
        return CollectionModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('خطأ في الحصول على عمليات التحصيل: $e');
      return [];
    }
  }

  /// الحصول على عمليات التحصيل لفاتورة معينة
  Future<List<CollectionModel>> getCollectionsByInvoiceId(
    String invoiceId,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'collections',
        where: 'invoiceId = ?',
        whereArgs: [invoiceId],
        orderBy: 'paymentDate DESC',
      );

      return List.generate(maps.length, (i) {
        return CollectionModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('خطأ في الحصول على عمليات التحصيل للفاتورة: $e');
      return [];
    }
  }

  /// الحصول على عمليات التحصيل لعميل معين
  Future<List<CollectionModel>> getCollectionsByCustomerId(
    String customerId,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'collections',
        where: 'customerId = ?',
        whereArgs: [customerId],
        orderBy: 'paymentDate DESC',
      );

      return List.generate(maps.length, (i) {
        return CollectionModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('خطأ في الحصول على عمليات التحصيل للعميل: $e');
      return [];
    }
  }

  /// إضافة عملية تحصيل جديدة
  Future<bool> addCollection(CollectionModel collection) async {
    try {
      final db = await _databaseService.database;

      // بدء المعاملة
      await db.transaction((txn) async {
        // إضافة عملية التحصيل
        await txn.insert('collections', collection.toMap());

        // تحديث الفاتورة
        await _updateInvoiceAfterCollection(txn, collection);
      });

      print('تم إضافة عملية التحصيل بنجاح');
      return true;
    } catch (e) {
      print('خطأ في إضافة عملية التحصيل: $e');
      return false;
    }
  }

  /// تحديث الفاتورة بعد استلام الدفعة
  Future<void> _updateInvoiceAfterCollection(
    Transaction txn,
    CollectionModel collection,
  ) async {
    try {
      // الحصول على الفاتورة الحالية
      final List<Map<String, dynamic>> invoiceMaps = await txn.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [collection.invoiceId],
      );

      if (invoiceMaps.isNotEmpty) {
        final currentInvoice = InvoiceModel.fromMap(invoiceMaps.first);
        final newPaidAmount =
            currentInvoice.paidAmount + collection.paymentAmount;

        // تحديد حالة الفاتورة الجديدة
        InvoiceStatus newStatus;
        if (newPaidAmount >= currentInvoice.total) {
          newStatus = InvoiceStatus.paid;
        } else if (newPaidAmount > 0) {
          newStatus = InvoiceStatus.partial;
        } else {
          newStatus = InvoiceStatus.pending;
        }

        // تحديث الفاتورة
        await txn.update(
          'invoices',
          {
            'paidAmount': newPaidAmount,
            'status': newStatus.index,
            'updatedAt': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [collection.invoiceId],
        );
      }
    } catch (e) {
      print('خطأ في تحديث الفاتورة بعد استلام الدفعة: $e');
      rethrow;
    }
  }

  /// الحصول على الفواتير المستحقة للتحصيل
  Future<List<InvoiceModel>> getPendingInvoices() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'invoices',
        where: 'status IN (0, 2) AND total > paidAmount', // معلق أو دفع جزئي
        orderBy: 'invoiceDate DESC',
      );

      return List.generate(maps.length, (i) {
        return InvoiceModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('خطأ في الحصول على الفواتير المستحقة: $e');
      return [];
    }
  }

  /// الحصول على إجمالي التحصيلات
  Future<double> getTotalCollections() async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections',
      );

      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      print('خطأ في الحصول على إجمالي التحصيلات: $e');
      return 0.0;
    }
  }

  /// الحصول على إجمالي التحصيلات لعميل معين
  Future<double> getTotalCollectionsByCustomer(String customerId) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections WHERE customerId = ?',
        [customerId],
      );

      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      print('خطأ في الحصول على إجمالي التحصيلات للعميل: $e');
      return 0.0;
    }
  }

  /// الحصول على إجمالي التحصيلات لفترة زمنية
  Future<double> getTotalCollectionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections WHERE paymentDate BETWEEN ? AND ?',
        [startDate.toIso8601String(), endDate.toIso8601String()],
      );

      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      print('خطأ في الحصول على إجمالي التحصيلات للفترة: $e');
      return 0.0;
    }
  }

  /// إنشاء عملية تحصيل جديدة
  Future<CollectionModel?> createCollection({
    required String invoiceId,
    required double collectionAmount,
    String? notes,
    String? createdBy,
  }) async {
    try {
      // الحصول على الفاتورة
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice == null) {
        print('الفاتورة غير موجودة');
        return null;
      }

      // التحقق من المبلغ المتبقي
      final remainingAmount = invoice.calculateRemainingAmount();
      if (collectionAmount > remainingAmount) {
        print('مبلغ التحصيل أكبر من المبلغ المتبقي');
        return null;
      }

      if (collectionAmount <= 0) {
        print('مبلغ التحصيل يجب أن يكون أكبر من صفر');
        return null;
      }

      // إنشاء عملية التحصيل
      final collection = CollectionModel(
        id: _uuid.v4(),
        invoiceId: invoiceId,
        invoiceNumber: invoice.invoiceNumber,
        customerId: invoice.customerId,
        customerName: invoice.customerName,
        customerPhone: invoice.customerPhone,
        invoiceDate: invoice.invoiceDate,
        invoiceTotal: invoice.total,
        previousPaidAmount: invoice.paidAmount,
        paymentAmount: collectionAmount,
        remainingAmount: remainingAmount - collectionAmount,
        paymentDate: DateTime.now(),
        notes: notes,
        createdBy: createdBy,
        createdAt: DateTime.now(),
      );

      // حفظ عملية التحصيل
      final success = await addCollection(collection);
      if (success) {
        return collection;
      } else {
        return null;
      }
    } catch (e) {
      print('خطأ في إنشاء عملية التحصيل: $e');
      return null;
    }
  }

  /// حذف عملية تحصيل
  Future<bool> deleteCollection(String collectionId) async {
    try {
      final db = await _databaseService.database;

      // الحصول على عملية التحصيل قبل الحذف
      final List<Map<String, dynamic>> maps = await db.query(
        'collections',
        where: 'id = ?',
        whereArgs: [collectionId],
      );

      if (maps.isEmpty) {
        print('عملية التحصيل غير موجودة');
        return false;
      }

      final collection = CollectionModel.fromMap(maps.first);

      // بدء المعاملة
      await db.transaction((txn) async {
        // حذف عملية التحصيل
        await txn.delete(
          'collections',
          where: 'id = ?',
          whereArgs: [collectionId],
        );

        // إعادة تحديث الفاتورة
        await _revertInvoiceAfterCollectionDeletion(txn, collection);
      });

      print('تم حذف عملية التحصيل بنجاح');
      return true;
    } catch (e) {
      print('خطأ في حذف عملية التحصيل: $e');
      return false;
    }
  }

  /// إعادة تحديث الفاتورة بعد حذف التحصيل
  Future<void> _revertInvoiceAfterCollectionDeletion(
    Transaction txn,
    CollectionModel collection,
  ) async {
    try {
      // الحصول على الفاتورة الحالية
      final List<Map<String, dynamic>> invoiceMaps = await txn.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [collection.invoiceId],
      );

      if (invoiceMaps.isNotEmpty) {
        final currentInvoice = InvoiceModel.fromMap(invoiceMaps.first);
        final newPaidAmount =
            currentInvoice.paidAmount - collection.paymentAmount;

        // تحديد حالة الفاتورة الجديدة
        InvoiceStatus newStatus;
        if (newPaidAmount >= currentInvoice.total) {
          newStatus = InvoiceStatus.paid;
        } else if (newPaidAmount > 0) {
          newStatus = InvoiceStatus.partial;
        } else {
          newStatus = InvoiceStatus.pending;
        }

        // تحديث الفاتورة
        await txn.update(
          'invoices',
          {
            'paidAmount': newPaidAmount,
            'status': newStatus.index,
            'updatedAt': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [collection.invoiceId],
        );
      }
    } catch (e) {
      print('خطأ في إعادة تحديث الفاتورة بعد حذف التحصيل: $e');
      rethrow;
    }
  }

  /// البحث في عمليات التحصيل
  Future<List<CollectionModel>> searchCollections(String query) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'collections',
        where: 'customerName LIKE ? OR invoiceNumber LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'paymentDate DESC',
      );

      return List.generate(maps.length, (i) {
        return CollectionModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('خطأ في البحث في عمليات التحصيل: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات التحصيل
  Future<Map<String, dynamic>> getCollectionStatistics() async {
    try {
      final db = await _databaseService.database;

      // إجمالي التحصيلات
      final totalResult = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections',
      );
      final totalCollections =
          (totalResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // عدد عمليات التحصيل
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM collections',
      );
      final collectionsCount = (countResult.first['count'] as int?) ?? 0;

      // التحصيلات اليوم
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final todayEnd = todayStart.add(const Duration(days: 1));

      final todayResult = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections WHERE paymentDate BETWEEN ? AND ?',
        [todayStart.toIso8601String(), todayEnd.toIso8601String()],
      );
      final todayCollections =
          (todayResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // التحصيلات هذا الشهر
      final monthStart = DateTime(today.year, today.month, 1);
      final monthEnd = DateTime(today.year, today.month + 1, 1);

      final monthResult = await db.rawQuery(
        'SELECT SUM(paymentAmount) as total FROM collections WHERE paymentDate BETWEEN ? AND ?',
        [monthStart.toIso8601String(), monthEnd.toIso8601String()],
      );
      final monthCollections =
          (monthResult.first['total'] as num?)?.toDouble() ?? 0.0;

      return {
        'totalCollections': totalCollections,
        'collectionsCount': collectionsCount,
        'todayCollections': todayCollections,
        'monthCollections': monthCollections,
      };
    } catch (e) {
      print('خطأ في الحصول على إحصائيات التحصيل: $e');
      return {
        'totalCollections': 0.0,
        'collectionsCount': 0,
        'todayCollections': 0.0,
        'monthCollections': 0.0,
      };
    }
  }
}
