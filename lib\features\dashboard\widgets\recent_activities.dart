import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../activities/models/activity_model.dart';

class RecentActivities extends StatelessWidget {
  const RecentActivities({super.key});

  @override
  Widget build(BuildContext context) {
    final activities = [
      _Activity(
        title: 'عميل جديد',
        subtitle: 'مكتب طبي الشفاء',
        amount: '',
        time: 'منذ ساعة',
        icon: Icons.person_add,
        color: AppColors.customers,
        type: ActivityType.customer,
      ),
      _Activity(
        title: 'مرتجع منتج',
        subtitle: 'منتج: جهاز قياس ضغط',
        amount: '-- ر.س',
        time: 'منذ ساعتين',
        icon: Icons.keyboard_return,
        color: AppColors.returns,
        type: ActivityType.returns,
      ),
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس القسم
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                'آخر ${activities.length} أنشطة',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  fontFamily: 'Cairo',
                ),
              ),
            ),
          ),

          // قائمة الأنشطة
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.w),
            itemCount: activities.length,
            separatorBuilder: (context, index) =>
                Divider(height: 24.h, color: AppColors.border),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return _ActivityItem(activity: activity);
            },
          ),
        ],
      ),
    );
  }
}

// enum ActivityType moved to activities/models/activity_model.dart

class _Activity {
  final String title;
  final String subtitle;
  final String amount;
  final String time;
  final IconData icon;
  final Color color;
  final ActivityType type;

  const _Activity({
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.time,
    required this.icon,
    required this.color,
    required this.type,
  });
}

class _ActivityItem extends StatelessWidget {
  final _Activity activity;

  const _ActivityItem({required this.activity});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // الأيقونة
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: activity.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Icon(activity.icon, color: activity.color, size: 20.sp),
        ),

        SizedBox(width: 12.w),

        // المحتوى
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity.title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 4.h),

              Text(
                activity.subtitle,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        SizedBox(width: 8.w),

        // المبلغ والوقت
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (activity.amount.isNotEmpty)
              Text(
                activity.amount,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: activity.type == ActivityType.returns
                      ? AppColors.error
                      : AppColors.textPrimary,
                ),
              ),

            SizedBox(height: 4.h),

            Text(
              activity.time,
              style: TextStyle(fontSize: 10.sp, color: AppColors.textHint),
            ),
          ],
        ),
      ],
    );
  }
}
