# البيزنس لوجيك الخاص بصفحة المنتجات

## نظرة عامة
يحتوي التطبيق على نظام إدارة منتجات متكامل يدعم إدارة المخزون الطبي مع أسعار مختلفة لأنواع العملاء المختلفة.

## نموذج البيانات (ProductModel)

### الخصائص الأساسية
```dart
class ProductModel {
  final String id;                    // معرف فريد للمنتج
  final String name;                  // اسم المنتج
  final String description;           // وصف المنتج
  final String category;              // فئة المنتج
  final String code;                  // كود المنتج
  final double price;                 // السعر الأساسي
  final double cost;                  // تكلفة المنتج
  final int quantity;                 // الكمية المتوفرة
  final int minQuantity;              // الكمية الدنيا للمخزون
  final int piecesPerCarton;          // عدد القطع في الكرتونة
  final String unit;                  // وحدة القياس
  final String? barcode;              // الباركود
  final String? imageUrl;             // رابط الصورة
  final String? image;                // بيانات الصورة
  final bool isActive;                // حالة المنتج (نشط/غير نشط)
  final DateTime createdAt;           // تاريخ الإنشاء
  final DateTime updatedAt;           // تاريخ آخر تحديث
  final String? createdBy;            // منشئ المنتج
}
```

### أسعار العملاء المختلفة
```dart
final double distributorPrice;        // سعر الموزع
final double medicalOfficeAPrice;     // سعر العيادة الطبية أ
final double medicalOfficeBPrice;     // سعر العيادة الطبية ب
final double majorClientPrice;        // سعر العميل الكبير
```

### الخصائص المحسوبة
```dart
bool get isLowStock => quantity <= minQuantity;           // منتج منخفض المخزون
double get profit => price - cost;                        // الربح
double get profitMargin => ((price - cost) / cost) * 100; // هامش الربح
bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty; // هل له صورة
bool get hasBarcode => barcode != null && barcode!.isNotEmpty; // هل له باركود
```

## خدمة المنتجات (ProductService)

### 1. إدارة المنتجات الأساسية

#### إضافة منتج جديد
```dart
Future<bool> addProduct(ProductModel product)
```
- **الوظيفة**: إضافة منتج جديد إلى قاعدة البيانات
- **التحقق**: التحقق من صحة البيانات قبل الحفظ
- **المزامنة**: تحديث stream المنتجات تلقائياً
- **التحديثات المؤقتة**: إضافة للتحديثات المؤقتة للمزامنة

#### تحديث منتج موجود
```dart
Future<bool> updateProduct(ProductModel product)
```
- **الوظيفة**: تحديث بيانات منتج موجود
- **التحقق**: التحقق من صحة البيانات
- **التاريخ**: تحديث تاريخ آخر تعديل تلقائياً
- **المزامنة**: تحديث stream المنتجات

#### حذف منتج
```dart
Future<bool> deleteProduct(String id)
```
- **الوظيفة**: حذف منتج من قاعدة البيانات
- **التنظيف**: إزالة من التحديثات المؤقتة
- **المزامنة**: تحديث stream المنتجات

### 2. استعلامات المنتجات

#### جلب جميع المنتجات
```dart
Future<List<ProductModel>> getAllProducts()
```
- **الوظيفة**: جلب جميع المنتجات من قاعدة البيانات
- **الاستخدام**: للعرض في قائمة المنتجات

#### جلب منتج بواسطة المعرف
```dart
Future<ProductModel?> getProductById(String id)
```
- **الوظيفة**: جلب منتج محدد بواسطة المعرف
- **الاستخدام**: لعرض تفاصيل المنتج أو التعديل

#### البحث في المنتجات
```dart
Future<List<ProductModel>> searchProducts(String query)
```
- **الوظيفة**: البحث في المنتجات حسب:
  - اسم المنتج
  - وصف المنتج
  - كود المنتج
  - الباركود
  - فئة المنتج
- **البحث**: غير حساس لحالة الأحرف

#### جلب المنتجات حسب الفئة
```dart
Future<List<ProductModel>> getProductsByCategory(String category)
```
- **الوظيفة**: تصفية المنتجات حسب الفئة
- **الفئات المدعومة**:
  - `devices` - أجهزة طبية
  - `consumables` - مستهلكات
  - `sterilization` - معقمات
  - `laboratory` - مستلزمات معمل
  - `general_supplies` - مستلزمات عامة
  - `protective_equipment` - معدات حماية

#### جلب المنتجات منخفضة المخزون
```dart
Future<List<ProductModel>> getLowStockProducts()
```
- **الوظيفة**: جلب المنتجات التي وصلت للكمية الدنيا
- **الشرط**: `quantity <= minQuantity`

### 3. إدارة المخزون

#### تحديث كمية المنتج
```dart
Future<bool> updateProductQuantity(String productId, int newQuantity)
```
- **الوظيفة**: تحديث كمية منتج محدد
- **التحقق**: التأكد من وجود المنتج
- **التحديث**: تحديث تاريخ آخر تعديل

### 4. إدارة الأسعار

#### الحصول على السعر حسب نوع العميل
```dart
double getPriceForCustomerType(String customerType)
```
- **أنواع العملاء**:
  - `distributor` - الموزع (80% من السعر الأساسي)
  - `medical_office_a` - العيادة الطبية أ (90% من السعر الأساسي)
  - `medical_office_b` - العيادة الطبية ب (95% من السعر الأساسي)
  - `major_client` - العميل الكبير (85% من السعر الأساسي)
- **الافتراضي**: السعر الأساسي إذا لم يتم تحديد سعر خاص

### 5. التحقق والتحليل

#### التحقق من صحة بيانات المنتج
```dart
bool _validateProductData(ProductModel product)
```
**التحققات المطلوبة**:
- اسم المنتج مطلوب وغير فارغ
- فئة المنتج مطلوبة
- السعر يجب أن يكون أكبر من صفر
- التكلفة يجب أن تكون أكبر من أو تساوي صفر
- الكمية يجب أن تكون أكبر من أو تساوي صفر
- الكمية الدنيا يجب أن تكون أكبر من أو تساوي صفر
- وحدة المنتج مطلوبة

#### التحقق من وجود كود المنتج
```dart
Future<bool> isCodeExists(String code, {String? excludeId})
```
- **الوظيفة**: التحقق من عدم تكرار كود المنتج
- **الاستثناء**: استثناء منتج محدد (للتحديث)

#### توليد كود منتج فريد
```dart
Future<String> generateProductCode()
```
- **التنسيق**: `PROD0001`, `PROD0002`, إلخ
- **الضمان**: عدم تكرار الكود

### 6. إدارة Streams والتحديثات

#### Stream المنتجات النشطة
```dart
Stream<List<ProductModel>> get productsStream
```
- **الوظيفة**: stream للتحديثات المباشرة لقائمة المنتجات
- **الاستخدام**: لتحديث واجهة المستخدم تلقائياً

#### تحديث Stream المنتجات
```dart
Future<void> _updateProductsStream()
```
- **الوظيفة**: تحديث stream بالمنتجات النشطة فقط
- **التصفية**: استبعاد المنتجات غير النشطة

#### إدارة التحديثات المؤقتة
```dart
void _addToPendingUpdates(ProductModel product)
Future<void> _processPendingUpdates()
```
- **الوظيفة**: إدارة التحديثات المؤقتة للمزامنة
- **المعالجة**: معالجة التحديثات في الخلفية

### 7. وظائف المساعدة والتطوير

#### إنشاء منتج تجريبي
```dart
Future<bool> createTestProduct()
```
- **الوظيفة**: إنشاء منتج تجريبي لاختبار قاعدة البيانات
- **الاستخدام**: للتطوير والاختبار

#### إضافة منتجات تجريبية
```dart
Future<bool> addDemoProducts()
```
- **الوظيفة**: إضافة مجموعة من المنتجات التجريبية
- **المنتجات**: باراسيتامول، قفازات طبية، مقياس حرارة

#### فحص قاعدة البيانات
```dart
Future<void> checkDatabase()
```
- **الوظيفة**: فحص وإصلاح قاعدة البيانات
- **الاستخدام**: للصيانة والتشخيص

### 8. إدارة قاعدة البيانات

#### حذف جميع المنتجات
```dart
Future<bool> clearAllProducts()
```
- **الوظيفة**: حذف جميع المنتجات من قاعدة البيانات
- **التحديث**: تحديث stream المنتجات

#### حذف جميع البيانات
```dart
Future<bool> clearAllData()
```
- **الوظيفة**: حذف جميع البيانات (منتجات، عملاء، فواتير)
- **الحماية**: عدم حذف بيانات المستخدمين

## قواعد العمل (Business Rules)

### 1. قواعد إضافة المنتجات
- يجب أن يكون اسم المنتج فريداً
- يجب أن يكون كود المنتج فريداً
- يجب أن تكون جميع الأسعار موجبة
- يجب أن تكون الكميات غير سالبة

### 2. قواعد الأسعار
- السعر الأساسي هو مرجع لجميع الأسعار الأخرى
- أسعار العملاء المختلفة يمكن أن تكون مخصصة
- إذا لم يتم تحديد سعر خاص، يتم حساب نسبة من السعر الأساسي

### 3. قواعد المخزون
- المنتج يعتبر منخفض المخزون عندما تصل الكمية للحد الأدنى
- لا يمكن أن تكون الكمية سالبة
- الكمية الدنيا تحدد متى يتم تنبيه المستخدم

### 4. قواعد الحالة
- المنتجات النشطة فقط تظهر في القوائم العامة
- المنتجات غير النشطة محفوظة ولكن غير مرئية
- يمكن إعادة تفعيل المنتجات غير النشطة

## معالجة الأخطاء

### أنواع الأخطاء المدعومة
1. **أخطاء التحقق**: بيانات غير صحيحة
2. **أخطاء قاعدة البيانات**: مشاكل في الاتصال أو الاستعلام
3. **أخطاء التكرار**: كود أو اسم مكرر
4. **أخطاء المزامنة**: مشاكل في التحديثات المؤقتة

### استراتيجية معالجة الأخطاء
- تسجيل جميع الأخطاء للتشخيص
- إرجاع رسائل خطأ واضحة للمستخدم
- الحفاظ على سلامة البيانات
- إعادة المحاولة التلقائية عند الحاجة

## الأداء والتحسين

### تحسينات الأداء
1. **التحديثات المؤقتة**: لتقليل عمليات قاعدة البيانات
2. **Streams**: للتحديثات المباشرة
3. **التصفية**: جلب المنتجات النشطة فقط
4. **المؤشرات**: استخدام معرفات فريدة للبحث السريع

### استراتيجيات التخزين المؤقت
- قائمة مؤقتة للتحديثات السريعة
- تحديث Stream عند الحاجة فقط
- معالجة التحديثات في الخلفية

## الأمان والخصوصية

### حماية البيانات
- التحقق من صحة جميع المدخلات
- حماية من حقن SQL
- تشفير البيانات الحساسة
- نسخ احتياطية منتظمة

### صلاحيات المستخدم
- تسجيل منشئ كل منتج
- تتبع التعديلات
- حماية من التعديل غير المصرح به
