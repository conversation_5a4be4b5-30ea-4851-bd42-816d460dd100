# نظام إعدادات التطبيق 📋

## نظرة عامة

نظام إعدادات شامل لتطبيق Atlas Medical Supplies يوفر تحكم كامل في جميع جوانب التطبيق من الأداء إلى الأمان.

## الملفات الرئيسية

### 1. `app_settings.dart`
النموذج الرئيسي للإعدادات يحتوي على جميع الإعدادات المطلوبة:

#### إعدادات الأداء (Performance)
- `splashOptimization`: تحسين شاشة البداية
- `enableLazyLoading`: التحميل التدريجي
- `enableImageCompression`: ضغط الصور
- `enablePrecache`: التحميل المسبق
- `removeDebugLogs`: إزالة رسائل التصحيح
- `enableTreeShaking`: تحسين الحجم

#### إعدادات المظهر (UI/UX)
- `enableAdaptiveTheme`: المظهر التكيفي
- `enableRTL`: دعم اللغة العربية
- `enableResponsiveLayout`: التصميم المتجاوب
- `selectedLanguage`: اللغة المختارة
- `selectedTheme`: المظهر المختار

#### إعدادات الأمان (Security)
- `enableAppSignatureVerification`: التحقق من التوقيع
- `enableDatabaseEncryption`: تشفير قاعدة البيانات
- `disableScreenCapture`: منع التقاط الشاشة
- `enableRootDetection`: كشف الأجهزة المعدلة
- `enableCodeObfuscation`: إخفاء الكود

#### إعدادات البيانات (Data)
- `enableBackupRestore`: النسخ الاحتياطي
- `cacheExpirationDays`: مدة صلاحية التخزين المؤقت
- `enableDataCompression`: ضغط البيانات
- `enableSecureStorage`: التخزين الآمن

#### إعدادات الاتصال (Connectivity)
- `enableOfflineMode`: الوضع بدون إنترنت
- `enablePushNotifications`: الإشعارات الفورية
- `enableAutoSync`: المزامنة التلقائية
- `syncIntervalMinutes`: فترة المزامنة

#### إعدادات التجربة (UX)
- `showOnboarding`: شاشة الترحيب
- `enableStatePersistence`: حفظ الحالة
- `enablePullToRefresh`: السحب للتحديث
- `enableInAppNotifications`: الإشعارات الداخلية

#### إعدادات التوزيع (Build)
- `appVersion`: إصدار التطبيق
- `buildNumber`: رقم البناء
- `enableAppSigning`: توقيع التطبيق
- `enableMinification`: ضغط التطبيق

### 2. `settings_manager.dart`
مدير الإعدادات الذي يتعامل مع حفظ وتحميل وتطبيق الإعدادات:

```dart
// استخدام مدير الإعدادات
final settingsManager = ref.read(settingsProvider.notifier);

// تغيير إعداد
settingsManager.toggleSplashOptimization();

// حفظ الإعدادات
await settingsManager.saveSettings();

// إعادة تعيين الإعدادات
settingsManager.resetSettings();
```

### 3. `advanced_settings_service.dart`
خدمة الإعدادات المتقدمة التي تطبق الإعدادات على التطبيق:

```dart
// تهيئة الخدمة
final advancedService = AdvancedSettingsService();
await advancedService.initialize(settings);

// تنظيف التخزين المؤقت
await advancedService.clearCache();

// إنشاء نسخة احتياطية
await advancedService.createBackup();
```

### 4. `settings_integration.dart`
دمج الإعدادات مع التطبيق الرئيسي:

```dart
// تهيئة الإعدادات
final integration = SettingsIntegration();
await integration.initializeSettings(ref);

// تطبيق إعدادات الأمان
integration.applySecuritySettings(ref);

// تطبيق إعدادات الأداء
integration.applyPerformanceSettings(ref);
```

### 5. `settings_screen.dart`
شاشة الإعدادات الرئيسية مع واجهة مستخدم كاملة.

## الاستخدام

### 1. إضافة الإعدادات إلى التطبيق الرئيسي

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/settings/settings_integration.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // تهيئة الإعدادات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SettingsIntegration().initializeSettings(ref);
    });

    return MaterialApp(
      theme: ref.watch(themeProvider),
      locale: ref.watch(localeProvider),
      home: HomeScreen(),
    );
  }
}
```

### 2. الوصول للإعدادات في أي مكان

```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(settingsProvider);
    
    return Container(
      child: Text('الإعدادات مفعلة: ${settings.enableRTL}'),
    );
  }
}
```

### 3. تغيير الإعدادات

```dart
class SettingsWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsManager = ref.read(settingsProvider.notifier);
    
    return Switch(
      value: ref.watch(settingsProvider).enableRTL,
      onChanged: (value) {
        settingsManager.toggleRTL();
      },
    );
  }
}
```

## الميزات المتقدمة

### 1. النسخ الاحتياطي والاستعادة
```dart
// إنشاء نسخة احتياطية
await AdvancedSettingsService().createBackup();

// استعادة نسخة احتياطية
await AdvancedSettingsService().restoreBackup(backupPath);
```

### 2. فحص الأجهزة المعدلة
```dart
// فحص حالة الجهاز
bool isRooted = await AdvancedSettingsService()._isDeviceRooted();
```

### 3. تنظيف التخزين المؤقت
```dart
// تنظيف التخزين المؤقت
await AdvancedSettingsService().clearCache();
```

### 4. فحص الاتصال بالإنترنت
```dart
// فحص الاتصال
bool isConnected = await AdvancedSettingsService().checkInternetConnection();
```

## إعدادات البناء (Build Settings)

### 1. تحسين الأداء
```bash
# بناء نسخة محسنة
flutter build apk --release --tree-shake-icons

# بناء نسخة مضغوطة
flutter build apk --release --obfuscate --split-debug-info=build/debug-info
```

### 2. إعدادات Android
```gradle
android {
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0.0"
    }
    
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 3. إعدادات iOS
```xml
<key>CFBundleShortVersionString</key>
<string>1.0.0</string>
<key>CFBundleVersion</key>
<string>1</string>
```

## الأمان

### 1. تشفير قاعدة البيانات
```dart
// تفعيل تشفير قاعدة البيانات
if (settings.enableDatabaseEncryption) {
  // تطبيق التشفير
}
```

### 2. منع التقاط الشاشة
```dart
// منع التقاط الشاشة للصفحات الحساسة
if (settings.disableScreenCapture) {
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual);
}
```

### 3. كشف الأجهزة المعدلة
```dart
// فحص الأجهزة المعدلة
if (settings.enableRootDetection) {
  bool isRooted = await _isDeviceRooted();
  if (isRooted) {
    // إظهار تحذير أو منع الاستخدام
  }
}
```

## الدعم والمساهمة

للمساهمة في تطوير نظام الإعدادات:

1. تأكد من اتباع معايير الكود
2. أضف اختبارات للوظائف الجديدة
3. حدث التوثيق عند إضافة ميزات جديدة
4. تأكد من توافق الإعدادات مع جميع المنصات

## الترخيص

هذا النظام جزء من تطبيق Atlas Medical Supplies ويخضع لنفس شروط الترخيص.
