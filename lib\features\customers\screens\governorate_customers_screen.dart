import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../widgets/customer_card.dart';
import 'customer_details_screen.dart';
import 'add_customer_screen.dart'; // Added import for AddCustomerScreen
import '../../../services/customer_service.dart'; // Added import for CustomerService

class GovernorateCustomersScreen extends StatefulWidget {
  final String governorateName;
  final List<CustomerModel> customers;
  final bool isSelectionMode;

  const GovernorateCustomersScreen({
    super.key,
    required this.governorateName,
    required this.customers,
    this.isSelectionMode = false,
  });

  @override
  State<GovernorateCustomersScreen> createState() =>
      _GovernorateCustomersScreenState();
}

class _GovernorateCustomersScreenState
    extends State<GovernorateCustomersScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // AppBar مخصص مربع في أعلى الصفحة
          Container(
            color: AppColors.primary,
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              bottom: 16.h,
              left: 16.w,
              right: 16.w,
            ),
            child: Row(
              children: [
                CustomBackButton(color: Colors.white, size: 20.sp),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'عملاء ${widget.governorateName}',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18.sp,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    '${widget.customers.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ],
            ),
          ),

          // قائمة العملاء
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: widget.customers.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 64.sp,
                            color: AppColors.textSecondary,
                          ),
                          SizedBox(height: 24.h),
                          Text(
                            'لا يوجد عملاء في هذه المحافظة',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textSecondary,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.all(16.w),
                      itemCount: widget.customers.length,
                      itemBuilder: (context, index) {
                        final customer = widget.customers[index];
                        return CustomerCard(
                          customer: customer,
                          onTap: () {
                            if (widget.isSelectionMode) {
                              // إرسال العميل المحدد للشاشة الأم في وضع الاختيار
                              Navigator.pop(context, customer);
                            } else {
                              // فتح صفحة تفاصيل العميل في الوضع العادي
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      CustomerDetailsScreen(customer: customer),
                                ),
                              );
                            }
                          },
                          onEdit: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    AddCustomerScreen(customer: customer),
                              ),
                            );

                            // إذا تم تحديث العميل، أرسل النتيجة للشاشة الأم
                            if (result != null &&
                                result is Map &&
                                result['updated'] == true) {
                              Navigator.pop(context, result);
                            }
                          },
                          onDelete: () async {
                            showDeleteProtectionDialog(
                              context: context,
                              title: 'حذف العميل',
                              message:
                                  'سيتم حذف العميل نهائياً من قاعدة البيانات. لا يمكن التراجع عن هذا الإجراء.',
                              itemName: customer.name ?? 'العميل المحدد',
                              onConfirm: () async {
                                try {
                                  final customerService = CustomerService();
                                  final success = await customerService
                                      .deleteCustomer(customer.id);

                                  if (success) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'تم حذف العميل بنجاح',
                                          style: TextStyle(fontFamily: 'Cairo'),
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );

                                    // إرسال إشارة أن العميل تم حذفه بنجاح
                                    Navigator.pop(context, {
                                      'deleted': true,
                                      'customerId': customer.id,
                                    });
                                  } else {
                                    throw Exception('فشل في حذف العميل');
                                  }
                                } catch (e) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'خطأ في حذف العميل: $e',
                                        style: TextStyle(fontFamily: 'Cairo'),
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                            );
                          },
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
