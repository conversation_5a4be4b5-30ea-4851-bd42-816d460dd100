import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/sales_model.dart';
import '../models/invoice_model.dart';
import '../models/customer_model.dart';
import 'database_service.dart';
import 'invoice_service.dart';
import 'customer_service.dart';

class SalesService {
  static final SalesService _instance = SalesService._internal();
  factory SalesService() => _instance;
  SalesService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();

  // Stream للاستماع لتغييرات المبيعات
  final StreamController<List<SalesModel>> _salesController = 
      StreamController<List<SalesModel>>.broadcast();

  Stream<List<SalesModel>> get salesStream => _salesController.stream;

  /// الحصول على جميع المبيعات
  Future<List<SalesModel>> getAllSales() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        orderBy: 'invoiceDate DESC',
      );

      final sales = maps.map((map) => SalesModel.fromMap(map)).toList();
      _salesController.add(sales);
      return sales;
    } catch (e) {
      debugPrint('خطأ في الحصول على المبيعات: $e');
      return [];
    }
  }

  /// الحصول على مبيعات محددة حسب التاريخ
  Future<List<SalesModel>> getSalesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'invoiceDate BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'invoiceDate DESC',
      );

      return maps.map((map) => SalesModel.fromMap(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المبيعات حسب التاريخ: $e');
      return [];
    }
  }

  /// الحصول على مبيعات عميل محدد
  Future<List<SalesModel>> getSalesByCustomer(String customerId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'customerId = ?',
        whereArgs: [customerId],
        orderBy: 'invoiceDate DESC',
      );

      return maps.map((map) => SalesModel.fromMap(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على مبيعات العميل: $e');
      return [];
    }
  }

  /// الحصول على مبيعات حسب الحالة
  Future<List<SalesModel>> getSalesByStatus(InvoiceStatus status) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'status = ?',
        whereArgs: [status.index],
        orderBy: 'invoiceDate DESC',
      );

      return maps.map((map) => SalesModel.fromMap(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المبيعات حسب الحالة: $e');
      return [];
    }
  }

  /// إضافة مبيعات جديدة من فاتورة
  Future<void> addSalesFromInvoice(InvoiceModel invoice) async {
    try {
      final sales = SalesModel.fromInvoice(invoice);
      await _addSales(sales);
      debugPrint('تم إضافة المبيعات من الفاتورة: ${invoice.invoiceNumber}');
    } catch (e) {
      debugPrint('خطأ في إضافة المبيعات من الفاتورة: $e');
    }
  }

  /// تحديث المبيعات من فاتورة محدثة
  Future<void> updateSalesFromInvoice(InvoiceModel invoice) async {
    try {
      final sales = SalesModel.fromInvoice(invoice);
      await _updateSales(sales);
      debugPrint('تم تحديث المبيعات من الفاتورة: ${invoice.invoiceNumber}');
    } catch (e) {
      debugPrint('خطأ في تحديث المبيعات من الفاتورة: $e');
    }
  }

  /// حذف المبيعات عند حذف الفاتورة
  Future<void> deleteSalesByInvoiceId(String invoiceId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'sales',
        where: 'invoiceId = ?',
        whereArgs: [invoiceId],
      );
      debugPrint('تم حذف المبيعات للفاتورة: $invoiceId');
      await _refreshSales();
    } catch (e) {
      debugPrint('خطأ في حذف المبيعات: $e');
    }
  }

  /// الحصول على إجمالي المبيعات
  Future<double> getTotalSales() async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(totalAmount) as total FROM sales WHERE status != 3', // استثناء الملغية
      );
      
      final total = result.first['total'] as num? ?? 0.0;
      return total.toDouble();
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي المبيعات: $e');
      return 0.0;
    }
  }

  /// الحصول على إجمالي المبيعات المدفوعة
  Future<double> getTotalPaidSales() async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(paidAmount) as total FROM sales WHERE status != 3', // استثناء الملغية
      );
      
      final total = result.first['total'] as num? ?? 0.0;
      return total.toDouble();
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي المبيعات المدفوعة: $e');
      return 0.0;
    }
  }

  /// الحصول على إجمالي المبيعات المتبقية
  Future<double> getTotalRemainingSales() async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(remainingAmount) as total FROM sales WHERE status != 3', // استثناء الملغية
      );
      
      final total = result.first['total'] as num? ?? 0.0;
      return total.toDouble();
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي المبيعات المتبقية: $e');
      return 0.0;
    }
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      final totalSales = await getTotalSales();
      final totalPaid = await getTotalPaidSales();
      final totalRemaining = await getTotalRemainingSales();
      final allSales = await getAllSales();

      // حساب عدد الفواتير حسب الحالة
      final pendingCount = allSales.where((s) => s.status == InvoiceStatus.pending).length;
      final paidCount = allSales.where((s) => s.status == InvoiceStatus.paid).length;
      final partialCount = allSales.where((s) => s.status == InvoiceStatus.partial).length;
      final cancelledCount = allSales.where((s) => s.status == InvoiceStatus.cancelled).length;

      return {
        'totalSales': totalSales,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
        'totalInvoices': allSales.length,
        'pendingCount': pendingCount,
        'paidCount': paidCount,
        'partialCount': partialCount,
        'cancelledCount': cancelledCount,
        'paymentRate': totalSales > 0 ? (totalPaid / totalSales) * 100 : 0.0,
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المبيعات: $e');
      return {
        'totalSales': 0.0,
        'totalPaid': 0.0,
        'totalRemaining': 0.0,
        'totalInvoices': 0,
        'pendingCount': 0,
        'paidCount': 0,
        'partialCount': 0,
        'cancelledCount': 0,
        'paymentRate': 0.0,
      };
    }
  }

  /// مزامنة جميع المبيعات من الفواتير الموجودة
  Future<void> syncSalesFromInvoices() async {
    try {
      final invoices = await _invoiceService.getAllInvoices();
      final db = await _databaseService.database;

      // حذف جميع المبيعات الحالية
      await db.delete('sales');

      // إضافة المبيعات من جميع الفواتير
      for (final invoice in invoices) {
        final sales = SalesModel.fromInvoice(invoice);
        await _addSales(sales);
      }

      debugPrint('تم مزامنة المبيعات من ${invoices.length} فاتورة');
      await _refreshSales();
    } catch (e) {
      debugPrint('خطأ في مزامنة المبيعات: $e');
    }
  }

  /// البحث في المبيعات
  Future<List<SalesModel>> searchSales(String query) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'customerName LIKE ? OR invoiceNumber LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'invoiceDate DESC',
      );

      return maps.map((map) => SalesModel.fromMap(map)).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المبيعات: $e');
      return [];
    }
  }

  /// الحصول على أفضل العملاء (حسب إجمالي المبيعات)
  Future<List<Map<String, dynamic>>> getTopCustomers({int limit = 10}) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT 
          customerId,
          customerName,
          COUNT(*) as invoiceCount,
          SUM(totalAmount) as totalSales,
          SUM(paidAmount) as totalPaid,
          SUM(remainingAmount) as totalRemaining
        FROM sales 
        WHERE status != 3
        GROUP BY customerId, customerName
        ORDER BY totalSales DESC
        LIMIT ?
      ''', [limit]);

      return maps;
    } catch (e) {
      debugPrint('خطأ في الحصول على أفضل العملاء: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات المبيعات الشهرية
  Future<List<Map<String, dynamic>>> getMonthlySalesStats() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT 
          strftime('%Y-%m', invoiceDate) as month,
          COUNT(*) as invoiceCount,
          SUM(totalAmount) as totalSales,
          SUM(paidAmount) as totalPaid,
          SUM(remainingAmount) as totalRemaining
        FROM sales 
        WHERE status != 3
        GROUP BY strftime('%Y-%m', invoiceDate)
        ORDER BY month DESC
        LIMIT 12
      ''');

      return maps;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المبيعات الشهرية: $e');
      return [];
    }
  }

  // Private methods

  Future<void> _addSales(SalesModel sales) async {
    try {
      final db = await _databaseService.database;
      await db.insert('sales', sales.toMap());
      await _refreshSales();
    } catch (e) {
      debugPrint('خطأ في إضافة المبيعات: $e');
    }
  }

  Future<void> _updateSales(SalesModel sales) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'sales',
        sales.toMap(),
        where: 'id = ?',
        whereArgs: [sales.id],
      );
      await _refreshSales();
    } catch (e) {
      debugPrint('خطأ في تحديث المبيعات: $e');
    }
  }

  Future<void> _refreshSales() async {
    try {
      final sales = await getAllSales();
      _salesController.add(sales);
    } catch (e) {
      debugPrint('خطأ في تحديث stream المبيعات: $e');
    }
  }

  /// إغلاق الخدمة
  void dispose() {
    _salesController.close();
  }
}
