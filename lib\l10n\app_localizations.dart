import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart' as intl;

class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const supportedLocales = [Locale('ar', 'EG'), Locale('en', 'US')];

  static final Map<String, Map<String, String>> _localizedValues = {
    'ar_EG': {
      'appName': 'Atlas Medical Supplies',
      'appSubtitle': 'نظام إدارة توزيع المستلزمات الطبية',
      'login': 'تسجيل الدخول',
      'forgotPassword': 'نسيت كلمة المرور؟',
      'enterCredentials': 'أدخل بيانات الدخول للمتابعة',
      'sendCode': 'إرسال كود',
      'confirm': 'تأكيد',
      'cancel': 'إلغاء',
      'phone': 'رقم الهاتف',
      'password': 'كلمة المرور',
      'otp': 'كود التحقق',
      'newPassword': 'كلمة مرور جديدة',
      'confirmPassword': 'تأكيد كلمة المرور',
    },
    'en_US': {
      'appName': 'Atlas Medical Supplies',
      'appSubtitle': 'Customer and invoicing management system',
      'login': 'Login',
      'forgotPassword': 'Forgot password?',
      'enterCredentials': 'Enter your credentials to continue',
      'sendCode': 'Send code',
      'confirm': 'Confirm',
      'cancel': 'Cancel',
      'phone': 'Phone',
      'password': 'Password',
      'otp': 'OTP Code',
      'newPassword': 'New password',
      'confirmPassword': 'Confirm password',
    },
  };

  String _key(Locale locale) => '${locale.languageCode}_${locale.countryCode}';

  String t(String key) {
    return _localizedValues[_key(locale)]?[key] ?? key;
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['ar', 'en'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    intl.Intl.defaultLocale = '${locale.languageCode}_${locale.countryCode}';
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(LocalizationsDelegate<AppLocalizations> old) => false;
}
