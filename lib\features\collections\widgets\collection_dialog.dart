import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../models/invoice_model.dart';

class CollectionDialog extends StatefulWidget {
  final InvoiceModel invoice;

  const CollectionDialog({super.key, required this.invoice});

  @override
  State<CollectionDialog> createState() => _CollectionDialogState();
}

class _CollectionDialogState extends State<CollectionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  double _remainingAmount = 0.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _remainingAmount = widget.invoice.calculateRemainingAmount();
    // إزالة القيمة الافتراضية لتجنب الخلط - المستخدم يجب أن يدخل المبلغ المطلوب تحصيله فقط
    _amountController.text = '';
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      final amount = double.tryParse(_amountController.text) ?? 0.0;
      final notes = _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim();

      Navigator.of(context).pop({'amount': amount, 'notes': notes});
    }
  }

  void _cancel() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        padding: EdgeInsets.all(20.w),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Icon(Icons.payment, color: Colors.blue.shade700, size: 24.sp),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      'تحصيل مبلغ من الفاتورة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 20.h),

              // معلومات الفاتورة
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الفاتورة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    _InfoRow(
                      label: 'رقم الفاتورة:',
                      value: widget.invoice.invoiceNumber,
                    ),
                    _InfoRow(
                      label: 'العميل:',
                      value: widget.invoice.customerName,
                    ),
                    _InfoRow(
                      label: 'تاريخ الفاتورة:',
                      value: widget.invoice.formattedDate,
                    ),
                    _InfoRow(
                      label: 'إجمالي الفاتورة:',
                      value: NumberFormat.currency(
                        locale: 'ar_SA',
                        symbol: 'ر.س ',
                        decimalDigits: 2,
                      ).format(widget.invoice.total),
                    ),
                    _InfoRow(
                      label: 'المدفوع سابقاً:',
                      value: NumberFormat.currency(
                        locale: 'ar_SA',
                        symbol: 'ر.س ',
                        decimalDigits: 2,
                      ).format(widget.invoice.paidAmount),
                    ),
                    _InfoRow(
                      label: 'المتبقي:',
                      value: NumberFormat.currency(
                        locale: 'ar_SA',
                        symbol: 'ر.س ',
                        decimalDigits: 2,
                      ).format(_remainingAmount),
                      valueColor: Colors.orange.shade700,
                    ),
                  ],
                ),
              ),

              SizedBox(height: 20.h),

              // مبلغ التحصيل
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade600,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'مبلغ التحصيل المطلوب',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4.h),
              Text(
                'أدخل المبلغ الذي تريد تحصيله فقط (وليس المبلغ الكامل)',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 11.sp,
                  color: Colors.orange.shade700,
                  fontStyle: FontStyle.italic,
                ),
              ),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _amountController,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                decoration: InputDecoration(
                  hintText: 'أدخل المبلغ المطلوب تحصيله',
                  hintStyle: TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.grey.shade500,
                    fontSize: 12.sp,
                  ),
                  prefixIcon: Icon(
                    Icons.attach_money,
                    color: Colors.grey.shade600,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.blue.shade300),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                style: TextStyle(fontFamily: 'Cairo', fontSize: 16.sp),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ المطلوب تحصيله';
                  }

                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح أكبر من صفر';
                  }

                  if (amount > _remainingAmount) {
                    return 'مبلغ التحصيل أكبر من المبلغ المتبقي (${_remainingAmount.toStringAsFixed(2)} ر.س)';
                  }

                  return null;
                },
              ),

              SizedBox(height: 16.h),

              // ملاحظات
              Text(
                'ملاحظات (اختياري)',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'أضف ملاحظات حول عملية التحصيل...',
                  hintStyle: TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.grey.shade500,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(color: Colors.blue.shade300),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                style: TextStyle(fontFamily: 'Cairo', fontSize: 14.sp),
              ),

              SizedBox(height: 24.h),

              // أزرار التحكم
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _cancel,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade300,
                        foregroundColor: Colors.grey.shade700,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        'إلغاء',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade600,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: _isLoading
                          ? SizedBox(
                              height: 20.h,
                              width: 20.w,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Text(
                              'تحصيل',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;

  const _InfoRow({required this.label, required this.value, this.valueColor});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: valueColor ?? Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
