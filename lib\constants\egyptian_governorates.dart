// constants/egyptian_governorates.dart

class EgyptianGovernorates {
  static const Map<String, List<String>> _governorates = {
    "القاهرة": [
      "المعادي",
      "حلوان",
      "مدينة نصر",
      "مصر الجديدة",
      "شبرا",
      "السيدة زينب",
      "التجمع الخامس",
      "الزمالك",
      "وسط البلد",
      "عين شمس",
      "المرج",
      "المطرية",
      "العباسية",
      "الزاوية الحمراء",
      "مدينة بدر",
      "مدينة الشروق",
      "15 مايو",
      "الزيتون",
      "حدائق القبة",
      "روض الفرج",
      "شبرا الخيمة",
      "الشرابية",
      "باب الشعرية",
      "الجمالية",
      "الدرب الأحمر",
      "الموسكي",
      "بولاق الدكرور",
      "الوراق",
      "أبو رواش",
      "كرداسة",
      "أوسيم",
      "الرحاب",
      "القصر العيني",
      "جاردن سيتي",
      "مصر القديمة",
      "الفسطاط",
      "المقطم",
      "طرة",
      "التبين",
      "المعادي الجديدة",
      "مدينة الرحاب",
      "مدينة الشروق الجديدة",
      "مدينة بدر الجديدة",
      "مدينة 15 مايو الجديدة",
      "مدينة العبور الجديدة",
      "مدينة العاشر من رمضان",
      "مدينة السادس من أكتوبر",
      "مدينة الشيخ زايد"
    ],
    "الجيزة": [
      "الدقي",
      "المهندسين",
      "الهرم",
      "العمرانية",
      "6 أكتوبر",
      "الشيخ زايد",
      "البدرشين",
      "أوسيم",
      "كرداسة",
      "الحوامدية",
      "العياط",
      "أبو النمرس",
      "المنيب",
      "بولاق الدكرور",
      "الوراق",
      "أبو رواش",
      "الحرانية",
      "أطفيح",
      "الواسطى",
      "المنصورية",
      "الطالبية",
      "الزمالك",
      "جزيرة الوراق",
      "مدينة السادس من أكتوبر",
      "مدينة الشيخ زايد"
    ],
    "الإسكندرية": [
      "سيدي جابر",
      "سموحة",
      "العجمي",
      "المنتزه",
      "محطة الرمل",
      "برج العرب",
      "المعمورة",
      "أبو قير",
      "باكوس",
      "سيدي بشر",
      "العطارين",
      "المنشية",
      "محرم بك",
      "الإبراهيمية",
      "جناكليس",
      "كامب شيزار",
      "ستانلي",
      "سابا باشا",
      "أجيون",
      "ميامي",
      "المندرة",
      "سيدي كرير",
      "برج العرب الجديدة",
      "العلمين",
      "مرسى مطروح",
      "الأزاريطة"
    ],
    "الدقهلية": [
      "المنصورة",
      "طلخا",
      "ميت غمر",
      "أجا",
      "السنبلاوين",
      "شربين",
      "بني عبيد",
      "بلقاس",
      "الجمالية",
      "المنزلة",
      "ميت سلسيل",
      "المنصورة الجديدة",
      "طلخا الجديدة",
      "أجا الجديدة",
      "السنبلاوين الجديدة",
      "شربين الجديدة",
      "بني عبيد الجديدة",
      "بلقاس الجديدة",
      "الجمالية الجديدة",
      "المنزلة الجديدة"
    ],
    "الشرقية": [
      "الزقازيق",
      "بلبيس",
      "العاشر من رمضان",
      "منيا القمح",
      "أبو حماد",
      "أبو كبير",
      "فاقوس",
      "الحسينية",
      "الصالحية الجديدة",
      "ههيا"
    ],
    "الغربية": [
      "طنطا",
      "المحلة الكبرى",
      "زفتى",
      "كفر الزيات",
      "سمنود",
      "السنطة",
      "بسيون",
      "قطور"
    ],
    "القليوبية": [
      "بنها",
      "قليوب",
      "شبرا الخيمة",
      "الخانكة",
      "طوخ",
      "كفر شكر",
      "العبور",
      "الخصوص",
      "القناطر الخيرية",
      "شبين القناطر",
      "أبو زعبل"
    ],
    "المنوفية": [
      "شبين الكوم",
      "منوف",
      "سرس الليان",
      "أشمون",
      "الباجور",
      "تلا",
      "قويسنا",
      "بركة السبع",
      "الشهداء"
    ],
    "كفر الشيخ": [
      "كفر الشيخ",
      "دسوق",
      "فوه",
      "بلطيم",
      "الحامول",
      "بيلا",
      "قلين",
      "مطوبس",
      "سيدي سالم",
      "الرياض",
      "سيدي غازي",
      "البرلس"
    ],
    "البحيرة": [
      "دمنهور",
      "كفر الدوار",
      "إيتاي البارود",
      "رشيد",
      "إدكو",
      "أبو حمص",
      "المحمودية",
      "شبراخيت",
      "كوم حمادة",
      "الدلنجات",
      "أبو المطامير",
      "حوش عيسى",
      "النوبارية الجديدة",
      "وادي النطرون",
      "النهضة",
      "العلمين",
      "الضبعة",
      "النجيلة",
      "سيدي براني",
      "السلوم",
      "سيوة"
    ],
    "دمياط": [
      "دمياط",
      "دمياط الجديدة",
      "رأس البر",
      "كفر سعد",
      "كفر البطيخ",
      "فارسكور",
      "الزرقا",
      "السرو",
      "الروضة"
    ],
    "بورسعيد": [
      "بورفؤاد",
      "حي الشرق",
      "حي العرب",
      "حي الضواحي",
      "حي المناخ",
      "حي الزهور",
      "حي السلام",
      "حي النصر",
      "حي الشاطئ",
      "حي الميناء",
      "حي القناة",
      "حي الفردوس",
      "حي الأمل",
      "حي المستقبل"
    ],
    "الإسماعيلية": [
      "الإسماعيلية",
      "فايد",
      "التل الكبير",
      "القنطرة شرق",
      "القنطرة غرب",
      "أبو صوير",
      "القصاصين"
    ],
    "السويس": [
      "حي السويس",
      "حي الأربعين",
      "حي فيصل",
      "حي الجناين",
      "حي عتاقة",
      "حي بورتوفيق"
    ],
    "شمال سيناء": [
      "العريش",
      "الشيخ زويد",
      "رفح",
      "بئر العبد",
      "الحسنة",
      "نخل"
    ],
    "جنوب سيناء": [
      "طور سيناء",
      "شرم الشيخ",
      "دهب",
      "نويبع",
      "طابا",
      "أبو رديس",
      "أبو زنيمة",
      "سانت كاترين",
      "الطور"
    ],
    "الفيوم": [
      "الفيوم",
      "سنورس",
      "إطسا",
      "طامية",
      "يوسف الصديق",
      "إبشواي",
      "الفيوم الجديدة",
      "سنورس الجديدة",
      "إطسا الجديدة",
      "طامية الجديدة",
      "يوسف الصديق الجديدة",
      "إبشواي الجديدة"
    ],
    "بني سويف": [
      "بني سويف",
      "الواسطى",
      "ناصر",
      "إهناسيا",
      "ببا",
      "الفشن",
      "سمسطا"
    ],
    "المنيا": [
      "المنيا",
      "العدوة",
      "مغاغة",
      "بني مزار",
      "مطاي",
      "سمالوط",
      "أبو قرقاص",
      "ملوي",
      "دير مواس"
    ],
    "أسيوط": [
      "أسيوط",
      "ديروط",
      "القوصية",
      "أبنوب",
      "منفلوط",
      "الغنايم",
      "ساحل سليم",
      "البداري",
      "أسيوط الجديدة",
      "ديروط الجديدة",
      "القوصية الجديدة",
      "أبنوب الجديدة",
      "منفلوط الجديدة",
      "الغنايم الجديدة",
      "ساحل سليم الجديدة",
      "البداري الجديدة"
    ],
    "سوهاج": [
      "سوهاج",
      "طما",
      "طهطا",
      "المراغة",
      "جرجا",
      "أخميم",
      "البلينا",
      "دار السلام",
      "سوهاج الجديدة",
      "طما الجديدة",
      "طهطا الجديدة",
      "المراغة الجديدة",
      "جرجا الجديدة",
      "أخميم الجديدة",
      "البلينا الجديدة",
      "دار السلام الجديدة"
    ],
    "قنا": [
      "قنا",
      "أبوتشت",
      "نجع حمادي",
      "دشنا",
      "الوقف",
      "قفط",
      "قوص",
      "قنا الجديدة",
      "أبوتشت الجديدة",
      "نجع حمادي الجديدة",
      "دشنا الجديدة",
      "الوقف الجديدة",
      "قفط الجديدة",
      "قوص الجديدة"
    ],
    "الأقصر": [
      "الأقصر",
      "الزينية",
      "البياضية",
      "القرنة",
      "أرمنت",
      "الطود",
      "الأقصر الجديدة",
      "الزينية الجديدة",
      "البياضية الجديدة",
      "القرنة الجديدة",
      "أرمنت الجديدة",
      "الطود الجديدة"
    ],
    "أسوان": [
      "أسوان",
      "دراو",
      "كوم أمبو",
      "نصر النوبة",
      "إدفو",
      "أسوان الجديدة",
      "دراو الجديدة",
      "كوم أمبو الجديدة",
      "نصر النوبة الجديدة",
      "إدفو الجديدة"
    ],
    "الوادي الجديد": [
      "الخارجة",
      "الداخلة",
      "الفرافرة",
      "باريس",
      "بلاط",
      "الخارجة الجديدة",
      "الداخلة الجديدة",
      "الفرافرة الجديدة",
      "باريس الجديدة",
      "بلاط الجديدة"
    ],
    "مطروح": [
      "مرسى مطروح",
      "الحمام",
      "العلمين",
      "الضبعة",
      "النجيلة",
      "سيدي براني",
      "السلوم",
      "سيوة",
      "مرسى مطروح الجديدة",
      "الحمام الجديدة",
      "العلمين الجديدة",
      "الضبعة الجديدة",
      "النجيلة الجديدة",
      "سيدي براني الجديدة",
      "السلوم الجديدة",
      "سيوة الجديدة"
    ],
    "البحر الأحمر": [
      "الغردقة",
      "رأس غارب",
      "سفاجا",
      "القصير",
      "مرسى علم",
      "شلاتين",
      "حلايب",
      "الغردقة الجديدة",
      "رأس غارب الجديدة",
      "سفاجا الجديدة",
      "القصير الجديدة",
      "مرسى علم الجديدة",
      "شلاتين الجديدة",
      "حلايب الجديدة"
    ]
  };

  // Get all governorates
  static List<String> getAllGovernorates() {
    return _governorates.keys.toList();
  }

  // Get cities for a specific governorate
  static List<String> getCitiesForGovernorate(String governorate) {
    return _governorates[governorate] ?? [];
  }

  // Get villages for a specific city (for now, return empty list as villages are not defined)
  static List<String> getVillagesForCity(String city) {
    // This could be expanded later to include actual village data
    return [];
  }

  // Check if a governorate exists
  static bool hasGovernorate(String governorate) {
    return _governorates.containsKey(governorate);
  }

  // Check if a city exists in a specific governorate
  static bool hasCityInGovernorate(String governorate, String city) {
    final cities = _governorates[governorate];
    return cities != null && cities.contains(city);
  }

  // Check if a village exists in a specific city
  static bool hasVillageInCity(String city, String village) {
    // For now, always return false as villages are not implemented
    return false;
  }

  // Get total count of governorates
  static int getTotalGovernoratesCount() {
    return _governorates.length;
  }

  // Get total count of cities across all governorates
  static int getTotalCitiesCount() {
    int total = 0;
    for (var cities in _governorates.values) {
      total += cities.length;
    }
    return total;
  }

  // Search for cities by name across all governorates
  static List<MapEntry<String, String>> searchCitiesByName(String searchTerm) {
    List<MapEntry<String, String>> results = [];
    for (var entry in _governorates.entries) {
      for (var city in entry.value) {
        if (city.contains(searchTerm)) {
          results.add(MapEntry(entry.key, city));
        }
      }
    }
    return results;
  }

  // Get governorates with most cities
  static List<MapEntry<String, int>> getGovernoratesByCityCount() {
    List<MapEntry<String, int>> governorateCounts = [];
    for (var entry in _governorates.entries) {
      governorateCounts.add(MapEntry(entry.key, entry.value.length));
    }
    governorateCounts.sort((a, b) => b.value.compareTo(a.value));
    return governorateCounts;
  }
}
