import 'package:flutter_test/flutter_test.dart';
import '../lib/services/database_service.dart';
import '../lib/services/product_service.dart';

void main() {
  group('Simple Database Tests', () {
    late DatabaseService databaseService;
    late ProductService productService;

    setUp(() async {
      databaseService = DatabaseService();
      productService = ProductService();
    });

    test('Test database connection', () async {
      print('🔧 Testing database connection...');
      
      try {
        final db = await databaseService.database;
        print('✅ Database connected successfully');
        
        // فحص الجداول
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table'"
        );
        print('📋 Found ${tables.length} tables');
        
        for (var table in tables) {
          print('  - ${table['name']}');
        }
        
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed: $e');
      }
    });

    test('Test products table', () async {
      print('🔧 Testing products table...');
      
      try {
        final db = await databaseService.database;
        
        // فحص وجود جدول المنتجات
        final productsTable = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'"
        );
        
        if (productsTable.isNotEmpty) {
          print('✅ Products table exists');
          
          // فحص عدد المنتجات
          final countResult = await db.rawQuery("SELECT COUNT(*) as count FROM products");
          final productCount = countResult.first['count'] as int;
          print('📦 Total products: $productCount');
          
          // فحص المنتجات النشطة
          final activeCountResult = await db.rawQuery("SELECT COUNT(*) as count FROM products WHERE isActive = 1");
          final activeProductCount = activeCountResult.first['count'] as int;
          print('✅ Active products: $activeProductCount');
          
        } else {
          print('❌ Products table not found');
        }
        
      } catch (e) {
        print('❌ Products table test failed: $e');
        fail('Products table test failed: $e');
      }
    });

    test('Test getAllProducts method', () async {
      print('🔧 Testing getAllProducts method...');
      
      try {
        final products = await productService.getAllProducts();
        print('📦 ProductService.getAllProducts() returned ${products.length} products');
        
        if (products.isNotEmpty) {
          print('📋 First 3 products:');
          for (int i = 0; i < products.length && i < 3; i++) {
            final product = products[i];
            print('  ${i + 1}. ${product.name} (Active: ${product.isActive})');
          }
        } else {
          print('⚠️ No products found');
        }
        
      } catch (e) {
        print('❌ getAllProducts test failed: $e');
        fail('getAllProducts test failed: $e');
      }
    });
  });
}
