import '../models/product_model.dart';
import 'database_service.dart';

class ProductService {
  final DatabaseService _databaseService = DatabaseService();

  Future<ProductModel?> getProductById(String productId) async {
    try {
      final productData = await _databaseService.getProductById(productId);
      if (productData != null) {
        return ProductModel.fromMap(productData);
      }
      return null;
    } catch (e) {
      print('Error getting product by ID: $e');
      return null;
    }
  }

  Future<List<ProductModel>> getAllProducts() async {
    try {
      final productsData = await _databaseService.getAllProducts();
      return productsData.map((data) => ProductModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting all products: $e');
      return [];
    }
  }

  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      final productsData = await _databaseService.searchProducts(query);
      return productsData.map((data) => ProductModel.fromMap(data)).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }
}
