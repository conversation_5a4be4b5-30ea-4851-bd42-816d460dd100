import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';

class AtlasLogo extends StatelessWidget {
  final double size;
  final bool showText;

  const AtlasLogo({
    super.key,
    this.size = 140,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size.w,
      height: size.w,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFE3F2FD),
            Color(0xFFBBDEFB),
            Color(0xFF90CAF9),
          ],
        ),
        borderRadius: BorderRadius.circular(25.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة Atlas مع خلفية دائرية
          Container(
            width: (size * 0.4).w,
            height: (size * 0.4).w,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary,
                  AppColors.secondary,
                ],
              ),
              borderRadius: BorderRadius.circular((size * 0.2).r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.inventory_2,
              size: (size * 0.2).sp,
              color: AppColors.textOnPrimary,
            ),
          ),
          
          if (showText) ...[
            SizedBox(height: 12.h),
            
            // نص Atlas مع تأثير جمالي
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                'ATLAS',
                style: TextStyle(
                  fontSize: (size * 0.12).sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                  fontFamily: 'Cairo',
                  letterSpacing: 3,
                ),
              ),
            ),
            
            SizedBox(height: 6.h),
            
            // نص Medical Supplies
            Text(
              'MEDICAL SUPPLIES',
              style: TextStyle(
                fontSize: (size * 0.08).sp,
                fontWeight: FontWeight.w500,
                color: AppColors.secondary,
                fontFamily: 'Cairo',
                letterSpacing: 1,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
