# فلترة الفواتير حسب التاريخ

## نظرة عامة
تم إضافة ميزة جديدة لفلترة الفواتير حسب التاريخ في شاشة إدارة الفواتير. هذه الميزة تسمح للمستخدمين بعرض الفواتير حسب فترات زمنية محددة.

## الميزات المضافة

### 1. خيارات الفلترة حسب التاريخ
- **اليوم**: عرض فواتير اليوم الحالي
- **يومين**: عرض فواتير آخر يومين
- **أسبوع**: عرض فواتير هذا الأسبوع (من الاثنين)
- **شهر**: عرض فواتير آخر شهر

### 2. واجهة المستخدم
- أزرار ملونة ومميزة لكل فترة زمنية
- عرض الفترة المحددة في العنوان
- عداد الفواتير المفلترة
- زر إعادة تعيين الفلترة

### 3. المؤشرات البصرية
- عرض الفترة المحددة في العنوان مع عدد الفواتير
- رسائل تأكيد عند تطبيق الفلترة
- مؤشر بصري يوضح أن الفلترة مفعلة

## كيفية الاستخدام

### تطبيق فلترة التاريخ:
1. انتقل إلى شاشة إدارة الفواتير
2. في قسم "الفواتير حسب التاريخ"، اختر الفترة المطلوبة:
   - اضغط على "اليوم" لعرض فواتير اليوم
   - اضغط على "يومين" لعرض فواتير آخر يومين
   - اضغط على "أسبوع" لعرض فواتير هذا الأسبوع
   - اضغط على "شهر" لعرض فواتير آخر شهر

### إعادة تعيين الفلترة:
- اضغط على زر "إعادة تعيين الفلترة" لعرض جميع الفواتير مرة أخرى

## التغييرات التقنية

### الملفات المعدلة:
- `lib/features/invoices/screens/invoices_management_screen.dart`

### المتغيرات الجديدة:
- `_isDateFiltered`: لتتبع حالة فلترة التاريخ
- `_selectedPeriod`: لتخزين الفترة المحددة

### الدوال الجديدة:
- `_filterInvoicesByDate(String period)`: لفلترة الفواتير حسب التاريخ
- `_resetDateFilter()`: لإعادة تعيين فلترة التاريخ

## منطق الفلترة

### اليوم:
```dart
startDate = DateTime(now.year, now.month, now.day);
```

### يومين:
```dart
startDate = DateTime(now.year, now.month, now.day - 2);
```

### أسبوع:
```dart
startDate = DateTime(now.year, now.month, now.day - now.weekday + 1);
```

### شهر:
```dart
startDate = DateTime(now.year, now.month - 1, now.day);
```

## المزايا

1. **سهولة الوصول**: فلترة سريعة للفواتير حسب الفترات الزمنية الشائعة
2. **تجربة مستخدم محسنة**: واجهة واضحة ومفهومة
3. **كفاءة**: عرض سريع للفواتير المطلوبة
4. **مرونة**: إمكانية إعادة تعيين الفلترة بسهولة
5. **مؤشرات بصرية**: عرض واضح لحالة الفلترة وعدد النتائج

## الاختبار

لاختبار الميزة:
1. تأكد من وجود فواتير بتواريخ مختلفة
2. جرب كل خيار من خيارات التاريخ
3. تأكد من أن العداد يعرض العدد الصحيح
4. تأكد من أن زر إعادة التعيين يعمل بشكل صحيح
5. تأكد من عرض الفترة المحددة في العنوان

## ملاحظات تقنية

- تستخدم الميزة `DateTime` من Flutter للتعامل مع التواريخ
- يتم تحديث الواجهة تلقائياً عند تطبيق الفلترة
- الرسائل التوضيحية تظهر لمدة ثانيتين
- الفلترة تعمل مع البحث النصي الموجود مسبقاً
