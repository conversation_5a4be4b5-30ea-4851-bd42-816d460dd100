import 'dart:async';
import 'package:flutter/material.dart';
import '../../../models/invoice_model.dart';
import '../../../constants/app_colors.dart';

class InvoiceEditStatusWidget extends StatefulWidget {
  final InvoiceModel invoice;

  const InvoiceEditStatusWidget({Key? key, required this.invoice})
    : super(key: key);

  @override
  State<InvoiceEditStatusWidget> createState() =>
      _InvoiceEditStatusWidgetState();
}

class _InvoiceEditStatusWidgetState extends State<InvoiceEditStatusWidget> {
  Timer? _timer;
  Duration? _remainingTime;

  @override
  void initState() {
    super.initState();
    _updateRemainingTime();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _updateRemainingTime();
        });
      }
    });
  }

  void _updateRemainingTime() {
    _remainingTime = widget.invoice.remainingEditTime;
  }

  @override
  Widget build(BuildContext context) {
    final canEdit = widget.invoice.canEditAfterTimeLimit;
    final remainingTime = _remainingTime;

    if (!canEdit) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.lock_clock, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                widget.invoice.editStatusMessage,
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (remainingTime == null) {
      return const SizedBox.shrink();
    }

    final minutes = remainingTime.inMinutes;
    final seconds = remainingTime.inSeconds % 60;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.access_time, color: Colors.orange),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'يمكن التعديل خلال:',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
