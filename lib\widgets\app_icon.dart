import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

enum AppIconType {
  home,
  login,
  profile,
  settings,
  notifications,
  messages,
  search,
  camera,
  location,
  logout,
  edit,
  delete,
  save,
  share,
}

class AppIcon extends StatelessWidget {
  final AppIconType type;
  final double? size;
  final Color? color;
  final bool isActive;

  const AppIcon({
    super.key,
    required this.type,
    this.size,
    this.color,
    this.isActive = true,
  });

  String _getIconPath() {
    switch (type) {
      case AppIconType.home:
        return 'assets/icons/home_icon.svg';
      case AppIconType.login:
        return 'assets/icons/login_icon.svg';
      case AppIconType.profile:
        return 'assets/icons/profile_icon.svg';
      case AppIconType.settings:
        return 'assets/icons/settings_icon.svg';
      case AppIconType.notifications:
        return 'assets/icons/notifications_icon.svg';
      case AppIconType.messages:
        return 'assets/icons/messages_icon.svg';
      case AppIconType.search:
        return 'assets/icons/search_icon.svg';
      case AppIconType.camera:
        return 'assets/icons/camera_icon.svg';
      case AppIconType.location:
        return 'assets/icons/location_icon.svg';
      case AppIconType.logout:
        return 'assets/icons/logout_icon.svg';
      case AppIconType.edit:
        return 'assets/icons/edit_icon.svg';
      case AppIconType.delete:
        return 'assets/icons/delete_icon.svg';
      case AppIconType.save:
        return 'assets/icons/save_icon.svg';
      case AppIconType.share:
        return 'assets/icons/share_icon.svg';
    }
  }

  @override
  Widget build(BuildContext context) {
    final iconColor =
        color ??
        (isActive ? Theme.of(context).primaryColor : Colors.grey.shade600);

    return SvgPicture.asset(
      _getIconPath(),
      width: size ?? 24,
      height: size ?? 24,
      colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
    );
  }
}

// Convenience widgets for common icon sizes
class AppIconSmall extends StatelessWidget {
  final AppIconType type;
  final Color? color;
  final bool isActive;

  const AppIconSmall({
    super.key,
    required this.type,
    this.color,
    this.isActive = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppIcon(type: type, size: 16, color: color, isActive: isActive);
  }
}

class AppIconMedium extends StatelessWidget {
  final AppIconType type;
  final Color? color;
  final bool isActive;

  const AppIconMedium({
    super.key,
    required this.type,
    this.color,
    this.isActive = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppIcon(type: type, size: 24, color: color, isActive: isActive);
  }
}

class AppIconLarge extends StatelessWidget {
  final AppIconType type;
  final Color? color;
  final bool isActive;

  const AppIconLarge({
    super.key,
    required this.type,
    this.color,
    this.isActive = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppIcon(type: type, size: 32, color: color, isActive: isActive);
  }
}
