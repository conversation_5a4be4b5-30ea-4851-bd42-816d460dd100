# ميزة مشاركة الفاتورة مع فتح رقم العميل في الرسائل

## الوصف
تم إضافة ميزة متقدمة لمشاركة الفواتير تتيح للمستخدمين مشاركة الفواتير عبر WhatsApp والرسائل النصية مع فتح رقم العميل تلقائياً في التطبيق المطلوب.

## الميزات الجديدة

### 1. فتح رقم العميل تلقائياً في الرسائل 📱
- **الوظيفة**: عند اختيار "مشاركة عبر SMS"، يتم فتح تطبيق الرسائل مع:
  - رقم العميل محدد مسبقاً في حقل المستلم
  - نص الفاتورة جاهز في الرسالة
- **الاستخدام**: 
  - يفتح تطبيق الرسائل الافتراضي
  - رقم العميل محدد تلقائياً
  - النص جاهز للإرسال

### 2. فتح رقم العميل تلقائياً في WhatsApp 🟢
- **الوظيفة**: عند اختيار "مشاركة عبر WhatsApp"، يتم فتح WhatsApp مع:
  - رقم العميل محدد مسبقاً
  - نص الفاتورة جاهز للإرسال
- **الاستخدام**:
  - يفتح WhatsApp مباشرة
  - رقم العميل محدد تلقائياً
  - النص جاهز للإرسال

### 3. ذكاء في اختيار رقم الهاتف 🧠
- **الأولوية الأولى**: رقم الهاتف الأول للعميل (phone1)
- **الأولوية الثانية**: رقم الهاتف الثاني للعميل (phone2)
- **الأولوية الثالثة**: رقم الهاتف المخزن في الفاتورة
- **التنظيف التلقائي**: إزالة الرموز غير الرقمية
- **إضافة رمز الدولة**: إضافة رمز مصر (+20) تلقائياً

## الكود المضافة

### دوال مساعدة جديدة

#### دالة الحصول على أفضل رقم هاتف
```dart
String _getBestPhoneNumber() {
  // محاولة الحصول على رقم الهاتف من بيانات العميل إذا كانت متوفرة
  if (widget.customer != null) {
    // أولوية للهاتف الأول
    if (widget.customer!.phone1 != null && widget.customer!.phone1!.isNotEmpty) {
      return widget.customer!.phone1!;
    }
    // ثم الهاتف الثاني
    if (widget.customer!.phone2 != null && widget.customer!.phone2!.isNotEmpty) {
      return widget.customer!.phone2!;
    }
  }
  
  // إذا لم يكن هناك رقم هاتف من العميل، استخدم الرقم المخزن في الفاتورة
  return widget.invoice.customerPhone;
}
```

#### دالة تنظيف وتنسيق رقم الهاتف
```dart
String _formatPhoneNumber(String phoneNumber) {
  // تنظيف رقم الهاتف من الرموز غير الرقمية
  String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
  
  // التحقق من أن رقم الهاتف صحيح
  if (cleaned.isEmpty) {
    return '';
  }

  // إضافة رمز الدولة إذا لم يكن موجود
  if (!cleaned.startsWith('20')) {
    cleaned = '20$cleaned';
  }
  
  return cleaned;
}
```

### دالة مشاركة عبر الرسائل النصية المحدثة
```dart
void _shareViaSMS() async {
  final message = _buildInvoiceMessage();
  
  // الحصول على رقم الهاتف الأفضل للعميل
  final phoneNumber = _getBestPhoneNumber();
  final formattedPhone = _formatPhoneNumber(phoneNumber);
  
  // التحقق من أن رقم الهاتف صحيح
  if (formattedPhone.isEmpty) {
    _showErrorSnackBar('لا يوجد رقم هاتف صحيح للعميل');
    return;
  }

  // إنشاء رابط الرسائل مع رقم الهاتف والنص
  final smsUrl = 'sms:$formattedPhone?body=${Uri.encodeComponent(message)}';

  try {
    final uri = Uri.parse(smsUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
      Navigator.pop(context);
      
      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم فتح تطبيق الرسائل مع رقم العميل: $formattedPhone',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    } else {
      _showErrorSnackBar('لا يمكن فتح تطبيق الرسائل');
    }
  } catch (e) {
    _showErrorSnackBar('حدث خطأ أثناء فتح تطبيق الرسائل: $e');
  }
}
```

### دالة مشاركة عبر WhatsApp المحدثة
```dart
void _shareViaWhatsApp() async {
  final message = _buildInvoiceMessage();
  
  // الحصول على رقم الهاتف الأفضل للعميل
  final phoneNumber = _getBestPhoneNumber();
  final formattedPhone = _formatPhoneNumber(phoneNumber);
  
  // التحقق من أن رقم الهاتف صحيح
  if (formattedPhone.isEmpty) {
    _showErrorSnackBar('لا يوجد رقم هاتف صحيح للعميل');
    return;
  }

  final whatsappUrl =
      'https://wa.me/$formattedPhone?text=${Uri.encodeComponent(message)}';

  try {
    final uri = Uri.parse(whatsappUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
      Navigator.pop(context);
      
      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم فتح WhatsApp مع رقم العميل: $formattedPhone',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    } else {
      _showErrorSnackBar('لا يمكن فتح WhatsApp');
    }
  } catch (e) {
    _showErrorSnackBar('حدث خطأ أثناء فتح WhatsApp: $e');
  }
}
```

## كيفية العمل

### 1. مشاركة عبر الرسائل النصية
1. المستخدم يضغط على زر مشاركة الفاتورة
2. يختار "مشاركة عبر SMS"
3. النظام يحصل على أفضل رقم هاتف للعميل
4. ينظف ويصيغ رقم الهاتف
5. يفتح تطبيق الرسائل مع:
   - رقم العميل محدد في حقل المستلم
   - نص الفاتورة جاهز في الرسالة
6. يعرض رسالة تأكيد

### 2. مشاركة عبر WhatsApp
1. المستخدم يضغط على زر مشاركة الفاتورة
2. يختار "مشاركة عبر WhatsApp"
3. النظام يحصل على أفضل رقم هاتف للعميل
4. ينظف ويصيغ رقم الهاتف
5. يفتح WhatsApp مع:
   - رقم العميل محدد
   - نص الفاتورة جاهز
6. يعرض رسالة تأكيد

## الميزات التقنية

### 1. ذكاء في اختيار رقم الهاتف
- **الأولوية**: phone1 > phone2 > customerPhone
- **التنظيف**: إزالة الرموز غير الرقمية
- **التنسيق**: إضافة رمز الدولة تلقائياً

### 2. معالجة الأخطاء
- **رقم هاتف فارغ**: رسالة خطأ واضحة
- **تطبيق غير متاح**: رسالة خطأ مناسبة
- **أخطاء عامة**: رسائل خطأ مفصلة

### 3. تجربة المستخدم
- **رسائل تأكيد**: تأكيد نجاح العملية
- **إغلاق تلقائي**: إغلاق قائمة المشاركة
- **واجهة بديهية**: سهولة الاستخدام

## الفوائد

### 1. للمستخدم
- **سرعة**: فتح التطبيق مع الرقم محدد
- **دقة**: استخدام أفضل رقم هاتف متاح
- **سهولة**: لا حاجة لإدخال الرقم يدوياً

### 2. للنظام
- **احترافية**: ميزات متقدمة
- **تكامل**: مع التطبيقات الخارجية
- **موثوقية**: معالجة الأخطاء

## الاختبار

### 1. اختبار الرسائل النصية
- [ ] تطبيق الرسائل يعمل
- [ ] رقم العميل محدد تلقائياً
- [ ] النص جاهز في الرسالة
- [ ] رسالة التأكيد تظهر

### 2. اختبار WhatsApp
- [ ] WhatsApp يعمل
- [ ] رقم العميل محدد تلقائياً
- [ ] النص جاهز في الرسالة
- [ ] رسالة التأكيد تظهر

### 3. اختبار أرقام الهاتف
- [ ] رقم الهاتف الأول يعمل
- [ ] رقم الهاتف الثاني يعمل
- [ ] رقم الفاتورة يعمل
- [ ] التنظيف والتنسيق يعمل

## ملاحظات مهمة

### 1. الأذونات
- **Android**: لا يحتاج أذونات إضافية
- **iOS**: قد يحتاج أذونات للوصول للتطبيقات

### 2. التوافق
- **الرسائل**: يعتمد على التطبيق المثبت
- **WhatsApp**: يعمل على جميع الأجهزة
- **أرقام الهاتف**: يدعم جميع التنسيقات

### 3. الأمان
- **البيانات**: لا يتم إرسال بيانات حساسة
- **الروابط**: روابط آمنة ومشفرة
- **الأخطاء**: رسائل خطأ آمنة

## التطوير المستقبلي

### 1. مشاركة متقدمة
- **اختيار رقم الهاتف**: إتاحة اختيار رقم معين
- **قوالب رسائل**: قوالب مختلفة للرسائل
- **جدولة الإرسال**: إرسال في وقت محدد

### 2. تكامل إضافي
- **Telegram**: دعم مشاركة عبر Telegram
- **Signal**: دعم مشاركة عبر Signal
- **البريد الإلكتروني**: دعم الإرسال عبر البريد

### 3. تخصيص
- **رسائل مخصصة**: رسائل مختلفة حسب نوع العميل
- **أوقات الإرسال**: إرسال في أوقات محددة
- **إشعارات**: إشعارات عند الإرسال
