# ميزة المبيعات - Sales Feature

## نظرة عامة

تم إضافة ميزة المبيعات الجديدة إلى تطبيق أطلس للمستلزمات الطبية، والتي تتيح للمستخدمين عرض وإدارة تقارير المبيعات بشكل مفصل ومنظم.

## الميزات الرئيسية

### 1. إجمالي المبيعات
- عرض إجمالي مبالغ المبيعات
- عرض إجمالي المبالغ المدفوعة
- عرض إجمالي المبالغ المتبقية
- حساب نسبة الدفع

### 2. قائمة تفصيلية بالفواتير
- اسم العميل
- رقم الفاتورة
- تاريخ الفاتورة
- المبلغ الإجمالي
- المبلغ المدفوع
- المبلغ المتبقي
- حالة الدفع

### 3. فلترة المبيعات
- فلترة حسب التاريخ (من تاريخ - إلى تاريخ)
- فلترة حسب العميل
- فلترة حسب حالة الدفع
- البحث في المبيعات

### 4. تصدير المبيعات
- تصدير إلى Excel (سيتم إضافته قريباً)
- تصدير إلى PDF (سيتم إضافته قريباً)
- تصدير إلى CSV (سيتم إضافته قريباً)

## البنية التقنية

### النماذج (Models)

#### SalesModel
```dart
class SalesModel {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime invoiceDate;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final InvoiceStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

### الخدمات (Services)

#### SalesService
- `getAllSales()` - الحصول على جميع المبيعات
- `getSalesByDateRange()` - الحصول على مبيعات حسب التاريخ
- `getSalesByCustomer()` - الحصول على مبيعات عميل محدد
- `getSalesByStatus()` - الحصول على مبيعات حسب الحالة
- `getTotalSales()` - الحصول على إجمالي المبيعات
- `getTotalPaidSales()` - الحصول على إجمالي المبيعات المدفوعة
- `getTotalRemainingSales()` - الحصول على إجمالي المبيعات المتبقية
- `getSalesStatistics()` - الحصول على إحصائيات المبيعات
- `syncSalesFromInvoices()` - مزامنة المبيعات من الفواتير
- `searchSales()` - البحث في المبيعات

### الشاشات (Screens)

#### SalesScreen
الشاشة الرئيسية للمبيعات التي تعرض:
- شريط البحث
- الفلاتر النشطة
- بطاقة ملخص المبيعات
- قائمة تفصيلية بالمبيعات

### الـ Widgets

#### SalesSummaryCard
بطاقة تعرض ملخص المبيعات مع:
- إجمالي المبيعات
- المدفوع
- المتبقي
- نسبة الدفع

#### SalesListItem
عنصر قائمة يعرض تفاصيل كل عملية بيع مع:
- معلومات الفاتورة
- معلومات العميل
- المبالغ
- شريط تقدم نسبة الدفع

#### SalesFilterDialog
نافذة فلترة المبيعات مع:
- اختيار التاريخ
- اختيار العميل
- اختيار حالة الدفع

#### SalesExportDialog
نافذة تصدير المبيعات مع:
- معلومات التصدير
- خيارات التصدير المختلفة

## قاعدة البيانات

### جدول المبيعات (sales)
```sql
CREATE TABLE sales (
  id TEXT PRIMARY KEY,
  invoiceId TEXT NOT NULL,
  invoiceNumber TEXT NOT NULL,
  customerId TEXT NOT NULL,
  customerName TEXT NOT NULL,
  customerPhone TEXT,
  invoiceDate TEXT NOT NULL,
  totalAmount REAL NOT NULL,
  paidAmount REAL NOT NULL,
  remainingAmount REAL NOT NULL,
  status INTEGER NOT NULL,
  notes TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL,
  FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
);
```

## التكامل التلقائي

### مزامنة مع الفواتير
- عند إنشاء فاتورة جديدة، يتم إضافة مبيعات تلقائياً
- عند تحديث فاتورة، يتم تحديث المبيعات تلقائياً
- عند حذف فاتورة، يتم حذف المبيعات تلقائياً

### مزامنة عند بدء التطبيق
- يتم مزامنة جميع المبيعات من الفواتير الموجودة عند بدء التطبيق
- يتم تحديث البيانات تلقائياً

## الواجهة العربية (RTL)

تم تصميم جميع الشاشات والواجهات باللغة العربية مع دعم كامل للـ RTL:
- النصوص باللغة العربية
- اتجاه العناصر من اليمين إلى اليسار
- تنسيق التواريخ والأرقام بالشكل العربي
- العملة بالجنيه المصري

## الاستخدام

### الوصول إلى المبيعات
1. من الإجراءات السريعة في لوحة التحكم
2. من القائمة الجانبية
3. من شريط التنقل

### فلترة المبيعات
1. اضغط على أيقونة الفلترة
2. اختر التاريخ المطلوب
3. اختر العميل (اختياري)
4. اختر حالة الدفع (اختياري)
5. اضغط "تطبيق الفلاتر"

### البحث في المبيعات
1. استخدم شريط البحث
2. اكتب اسم العميل أو رقم الفاتورة
3. ستظهر النتائج تلقائياً

### تصدير المبيعات
1. اضغط على أيقونة التصدير
2. اختر نوع التصدير المطلوب
3. سيتم حفظ الملف في مجلد التنزيلات

## الإحصائيات المتاحة

### إحصائيات عامة
- إجمالي المبيعات
- إجمالي المدفوع
- إجمالي المتبقي
- نسبة الدفع

### إحصائيات حسب الحالة
- عدد الفواتير المعلقة
- عدد الفواتير المدفوعة بالكامل
- عدد الفواتير المدفوعة جزئياً
- عدد الفواتير الملغية

### إحصائيات متقدمة
- أفضل العملاء (حسب إجمالي المبيعات)
- إحصائيات المبيعات الشهرية
- معدل التحصيل

## التحديثات المستقبلية

### تصدير البيانات
- [ ] تصدير إلى Excel
- [ ] تصدير إلى PDF
- [ ] تصدير إلى CSV

### تحليلات متقدمة
- [ ] رسوم بيانية للمبيعات
- [ ] تحليل الاتجاهات
- [ ] توقعات المبيعات

### تقارير إضافية
- [ ] تقارير يومية
- [ ] تقارير أسبوعية
- [ ] تقارير شهرية
- [ ] تقارير سنوية

## الدعم التقني

### متطلبات النظام
- Flutter 3.19+
- SQLite
- دعم RTL

### الأخطاء المعروفة
- لا توجد أخطاء معروفة حالياً

### استكشاف الأخطاء
1. تأكد من تحديث قاعدة البيانات
2. تحقق من وجود الفواتير
3. أعد تشغيل التطبيق إذا لزم الأمر

## المساهمة

للمساهمة في تطوير ميزة المبيعات:
1. تأكد من اتباع معايير الكود
2. اختبر التغييرات جيداً
3. اكتب توثيقاً للتغييرات الجديدة
4. تأكد من دعم اللغة العربية والـ RTL
