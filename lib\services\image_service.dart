import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart';

class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  /// حفظ صورة محلياً وإرجاع المسار
  Future<String?> saveImageLocally(File imageFile, {String? customName}) async {
    try {
      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/product_images');
      
      // إنشاء مجلد الصور إذا لم يكن موجوداً
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // إنشاء اسم فريد للصورة
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = customName ?? 'product_${timestamp}$extension';
      final savedPath = '${imagesDir.path}/$fileName';

      // نسخ الصورة إلى المجلد المحلي
      await imageFile.copy(savedPath);
      
      debugPrint('تم حفظ الصورة محلياً: $savedPath');
      return savedPath;
    } catch (e) {
      debugPrint('خطأ في حفظ الصورة محلياً: $e');
      return null;
    }
  }

  /// حفظ صورة من مسار وإرجاع المسار المحلي
  Future<String?> saveImageFromPath(String imagePath, {String? customName}) async {
    try {
      final imageFile = File(imagePath);
      if (await imageFile.exists()) {
        return await saveImageLocally(imageFile, customName: customName);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في حفظ الصورة من المسار: $e');
      return null;
    }
  }

  /// حذف صورة محلية
  Future<bool> deleteLocalImage(String imagePath) async {
    try {
      if (imagePath.startsWith('/') || imagePath.startsWith('file://')) {
        final file = File(imagePath);
        if (await file.exists()) {
          await file.delete();
          debugPrint('تم حذف الصورة المحلية: $imagePath');
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف الصورة المحلية: $e');
      return false;
    }
  }

  /// التحقق من أن الصورة محلية
  bool isLocalImage(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;
    return imagePath.startsWith('/') || imagePath.startsWith('file://');
  }

  /// التحقق من أن الصورة من الشبكة
  bool isNetworkImage(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  /// الحصول على حجم الصورة
  Future<int?> getImageSize(String imagePath) async {
    try {
      if (isLocalImage(imagePath)) {
        final file = File(imagePath);
        if (await file.exists()) {
          return await file.length();
        }
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على حجم الصورة: $e');
      return null;
    }
  }

  /// تنظيف الصور المحلية غير المستخدمة
  Future<void> cleanupUnusedImages(List<String> usedImagePaths) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/product_images');
      
      if (!await imagesDir.exists()) return;

      final files = await imagesDir.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final filePath = file.path;
          if (!usedImagePaths.contains(filePath)) {
            try {
              await file.delete();
              debugPrint('تم حذف الصورة غير المستخدمة: $filePath');
            } catch (e) {
              debugPrint('خطأ في حذف الصورة: $filePath - $e');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف الصور: $e');
    }
  }

  /// الحصول على معلومات الصورة
  Future<Map<String, dynamic>?> getImageInfo(String imagePath) async {
    try {
      if (isLocalImage(imagePath)) {
        final file = File(imagePath);
        if (await file.exists()) {
          final stat = await file.stat();
          return {
            'path': imagePath,
            'size': stat.size,
            'modified': stat.modified,
            'type': 'local',
          };
        }
      } else if (isNetworkImage(imagePath)) {
        return {
          'path': imagePath,
          'type': 'network',
        };
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الصورة: $e');
      return null;
    }
  }
}
