import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../models/invoice_model.dart';
import '../../../models/product_model.dart';
import '../../../models/customer_model.dart';
import '../../../services/invoice_service.dart';
import '../../../services/product_service.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../widgets/custom_text_field.dart';

class InvoiceItemForm extends StatefulWidget {
  final InvoiceItem item;
  final Function(InvoiceItem) onUpdate;
  final VoidCallback onRemove;
  final String? invoiceId;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final CustomerType customerType; // إضافة نوع العميل

  const InvoiceItemForm({
    Key? key,
    required this.item,
    required this.onUpdate,
    required this.onRemove,
    this.invoiceId,
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    required this.customerType, // إضافة نوع العميل كمعلمة مطلوبة
  }) : super(key: key);

  @override
  State<InvoiceItemForm> createState() => _InvoiceItemFormState();
}

class _InvoiceItemFormState extends State<InvoiceItemForm> {
  final InvoiceService _invoiceService = InvoiceService();
  final ProductService _productService = ProductService();

  late TextEditingController _quantityController;
  late TextEditingController _totalController;

  ProductModel? _product;
  String _errorMessage = '';
  String _selectedUnit = AppStrings.piece; // القيمة الافتراضية

  @override
  void initState() {
    super.initState();
    _quantityController = TextEditingController(
      text: widget.item.quantity.toString(),
    );
    _totalController = TextEditingController(
      text: widget.item.total.toStringAsFixed(2),
    );

    // إصلاح مشكلة الوحدة الفارغة - التأكد من أن الوحدة صحيحة
    try {
      if (widget.item.unit.isNotEmpty &&
          AppStrings.allUnits.contains(widget.item.unit)) {
        _selectedUnit = widget.item.unit;
      } else {
        _selectedUnit = AppStrings.piece; // القيمة الافتراضية
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة الوحدة: $e');
      _selectedUnit = AppStrings.piece; // القيمة الافتراضية في حالة الخطأ
    }

    _quantityController.addListener(_updateTotal);

    // جلب معلومات المنتج للتحقق من الكمية المتوفرة
    _loadProductInfo();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _totalController.dispose();
    super.dispose();
  }

  Future<void> _loadProductInfo() async {
    try {
      _product = await _productService.getProductById(widget.item.productId);
      setState(() {});
    } catch (e) {
      debugPrint('خطأ في جلب معلومات المنتج: $e');
    }
  }

  void _updateTotal() {
    try {
      final quantity = int.tryParse(_quantityController.text) ?? 0;

      // الحصول على السعر حسب الوحدة المختارة
      double price;
      if (_product != null) {
        try {
          price = _product!.getPriceForUnitAndCustomerType(
            _selectedUnit,
            widget.customerType,
          );
        } catch (e) {
          debugPrint('خطأ في الحصول على السعر: $e');
          price = widget.item.unitPrice; // استخدام السعر الحالي كبديل
        }
      } else {
        price = widget.item.unitPrice; // استخدام السعر الحالي كبديل
      }

      // التحقق من صحة السعر
      if (price <= 0) {
        price = widget.item.unitPrice > 0 ? widget.item.unitPrice : 1.0;
      }

      // التحقق من صحة الكمية
      if (quantity <= 0) {
        setState(() {
          _errorMessage = 'يجب أن تكون الكمية أكبر من صفر';
        });
        return;
      }

      // التحقق من الكمية المتوفرة
      if (_product != null && quantity > _product!.quantity) {
        setState(() {
          _errorMessage =
              'الكمية المطلوبة (${quantity}) تتجاوز الكمية المتوفرة (${_product!.quantity})';
        });
      } else {
        setState(() {
          _errorMessage = '';
        });
      }

      final total = quantity * price;
      _totalController.text = total.toStringAsFixed(2);

      try {
        final updatedItem = _invoiceService.updateInvoiceItem(
          item: widget.item,
          customer: CustomerModel(
            id: widget.customerId ?? '',
            name: widget.customerName ?? '',
            type: widget.customerType,
            activity: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ), // إنشاء نموذج عميل مؤقت
          quantity: quantity,
          unit: _selectedUnit,
        );

        widget.onUpdate(updatedItem);
      } catch (e) {
        debugPrint('خطأ في تحديث العنصر: $e');
        // لا نريد إيقاف العملية بسبب هذا الخطأ
      }
    } catch (e) {
      debugPrint('خطأ في تحديث الإجمالي: $e');
      // في حالة حدوث خطأ، إعادة تعيين الإجمالي
      _totalController.text = widget.item.total.toStringAsFixed(2);
    }
  }

  void _onUnitChanged(String? newUnit) {
    try {
      if (newUnit != null && AppStrings.allUnits.contains(newUnit)) {
        setState(() {
          _selectedUnit = newUnit;
        });

        // تحديث السعر والإجمالي حسب الوحدة الجديدة
        _updatePriceAndTotalForUnit(newUnit);
      } else {
        // إذا كانت الوحدة غير صحيحة، استخدم القيمة الافتراضية
        setState(() {
          _selectedUnit = AppStrings.piece;
        });

        // تحديث السعر والإجمالي للوحدة الافتراضية
        _updatePriceAndTotalForUnit(AppStrings.piece);
      }
    } catch (e) {
      debugPrint('خطأ في تغيير الوحدة: $e');
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      setState(() {
        _selectedUnit = AppStrings.piece;
      });

      // محاولة تحديث السعر والإجمالي للوحدة الافتراضية
      try {
        _updatePriceAndTotalForUnit(AppStrings.piece);
      } catch (updateError) {
        debugPrint('خطأ في تحديث السعر والإجمالي: $updateError');
      }
    }
  }

  // تحديث السعر والإجمالي حسب الوحدة المختارة
  void _updatePriceAndTotalForUnit(String unit) {
    try {
      if (_product == null) {
        debugPrint('المنتج فارغ، لا يمكن تحديث السعر');
        return;
      }

      // حساب السعر الجديد حسب الوحدة المختارة
      final newUnitPrice = _product!.getPriceForUnitAndCustomerType(
        unit,
        widget.customerType,
      );

      // تحديث الكمية الحالية
      final quantity = int.tryParse(_quantityController.text) ?? 0;

      // حساب الإجمالي الجديد
      final newTotal = newUnitPrice * quantity;

      // تحديث العرض
      setState(() {
        _totalController.text = newTotal.toStringAsFixed(2);
      });

      // تحديث العنصر في الفاتورة
      try {
        final updatedItem = _invoiceService.updateInvoiceItem(
          item: widget.item,
          customer: CustomerModel(
            id: widget.customerId ?? '',
            name: widget.customerName ?? '',
            type: widget.customerType,
            activity: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ), // إنشاء نموذج عميل مؤقت
          quantity: quantity,
          unit: unit,
        );

        // تحديث السعر في العنصر (نحتاج لتعديل InvoiceItem ليدعم تحديث السعر)
        final finalUpdatedItem = updatedItem.copyWith(
          unitPrice: newUnitPrice,
          total: newTotal,
        );

        widget.onUpdate(finalUpdatedItem);

        // رسالة نجاح
        if (quantity > 0) {
          String message;
          if (unit == AppStrings.carton && _product != null) {
            message =
                'تم تحديث السعر: ${newUnitPrice.toStringAsFixed(2)} ر.س للكرتونة (${_product!.piecesPerCarton} قطعة)';
          } else {
            message =
                'تم تحديث السعر: ${newUnitPrice.toStringAsFixed(2)} ر.س للقطعة';
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } catch (updateError) {
        debugPrint('خطأ في تحديث العنصر في الفاتورة: $updateError');
        // عرض رسالة خطأ للمستخدم
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الفاتورة: $updateError'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث السعر والإجمالي: $e');
      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث السعر: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // حساب سعر الوحدة الحالية
  double _getCurrentUnitPrice() {
    if (_product == null) return widget.item.unitPrice;
    return _product!.getPriceForUnitAndCustomerType(
      _selectedUnit,
      widget.customerType,
    );
  }

  void _onRemove() {
    widget.onRemove();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس العنصر
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.item.productName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primary.withOpacity(0.4),
                            ),
                          ),
                          child: Text(
                            _selectedUnit,
                            style: TextStyle(
                              color: AppColors.primary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (widget.item.productCode.isNotEmpty)
                      Text(
                        'الكود: ${widget.item.productCode}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _onRemove,
                icon: const Icon(Icons.delete, color: Colors.red),
                tooltip: 'حذف المنتج',
              ),
            ],
          ),

          const SizedBox(height: 16),

          // حقول الإدخال - الكمية والوحدة
          Row(
            children: [
              Expanded(
                flex: 3, // يعطي مساحة أكبر للكمية
                child: CustomTextField(
                  controller: _quantityController,
                  labelText: 'الكمية',
                  hintText: 'أدخل الكمية',
                  prefixIcon: Icons.shopping_cart,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                ),
              ),
              const SizedBox(width: 16),
              // اختيار الوحدة - تم تحسين التصميم ليكون أكثر توافقاً مع حجم الشاشة
              Expanded(
                flex: 2, // يعطي مساحة مناسبة للوحدة
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الوحدة',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 56, // ارتفاع ثابت للحقل
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.withOpacity(0.3)),
                      ),
                      child: DropdownButtonFormField<String>(
                        value: _selectedUnit,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        items: _product != null
                            ? _product!.getAvailableUnits().map((unit) {
                                return DropdownMenuItem<String>(
                                  value: unit,
                                  child: Text(unit),
                                );
                              }).toList()
                            : AppStrings.allUnits.map((unit) {
                                return DropdownMenuItem<String>(
                                  value: unit,
                                  child: Text(unit),
                                );
                              }).toList(),
                        onChanged: _onUnitChanged,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                        ),
                        dropdownColor: Colors.white,
                        icon: const Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        isExpanded: true,
                        menuMaxHeight: 250, // ارتفاع مناسب للقائمة
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // عرض الوحدة المختارة بشكل واضح ومحسن
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.category,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوحدة المختارة',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _selectedUnit,
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _selectedUnit,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // عرض سعر الوحدة حسب الوحدة المختارة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'سعر الوحدة',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _selectedUnit,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '${_getCurrentUnitPrice().toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'لكل $_selectedUnit',
                      style: TextStyle(color: Colors.black, fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // الإجمالي
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _totalController,
                  labelText: 'الإجمالي',
                  hintText: 'الإجمالي المحسوب',
                  prefixIcon: Icons.calculate,
                  readOnly: true,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Text(
                      'الكمية المطلوبة',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${_quantityController.text}',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _selectedUnit.isNotEmpty
                          ? _selectedUnit
                          : AppStrings.piece,
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // رسالة الخطأ
          if (_errorMessage.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تنبيه',
                          style: TextStyle(
                            color: Colors.red[700],
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _errorMessage,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],

          // معلومات المخزون
          if (_product != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: _product!.quantity > 0
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _product!.quantity > 0
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _product!.quantity > 0
                          ? Colors.green
                          : Colors.orange,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _product!.quantity > 0 ? Icons.inventory : Icons.warning,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المخزون المتوفر',
                          style: TextStyle(
                            color: _product!.quantity > 0
                                ? Colors.green[700]
                                : Colors.orange[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              '${_product!.quantity}',
                              style: TextStyle(
                                color: _product!.quantity > 0
                                    ? Colors.green[700]
                                    : Colors.orange[700],
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _product!.unit,
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'الوحدة المطلوبة: ',
                              style: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                                fontSize: 11,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                _selectedUnit,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
        ],
      ),
    );
  }
}
