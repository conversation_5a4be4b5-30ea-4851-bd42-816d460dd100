# إصلاح مشكلة Material Widget

## المشكلة
عند استخدام `TextField` أو أي widget من Material Design، قد تظهر رسالة خطأ:
```
No Material widget found.
TextField widgets require a Material widget ancestor within the closest LookupBoundary.
```

## السبب
`TextField` وأغلب widgets في Material Design تحتاج إلى `Material` widget كأب لها. هذا يحدث عادة عندما:
- لا يتم استخدام `Scaffold` في الصفحة
- يتم استخدام `Column` أو `Stack` مباشرة بدون `Scaffold`

## الحل

### 1. إضافة Scaffold
```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    body: Column(
      children: [
        // المحتوى هنا
      ],
    ),
  );
}
```

### 2. استخدام MaterialFix Helper
```dart
import '../../../utils/material_fix.dart';

@override
Widget build(BuildContext context) {
  return MaterialFix.wrapWithScaffoldIfNeeded(
    child: Column(
      children: [
        // المحتوى هنا
      ],
    ),
    appBar: MaterialFix.createCustomAppBar(
      title: 'عنوان الصفحة',
      onMenuPressed: () => Scaffold.of(context).openDrawer(),
      onBackPressed: () => Navigator.pop(context),
    ),
    floatingActionButton: FloatingActionButton(
      onPressed: () {},
      child: Icon(Icons.add),
    ),
  );
}
```

## الصفحات التي تم إصلاحها
- ✅ `ProductsScreen` - صفحة المنتجات
- ✅ `CustomersScreen` - صفحة العملاء  
- ✅ `InvoicesScreen` - صفحة الفواتير
- ✅ `InventoryScreen` - صفحة المخزون (كانت تعمل بشكل صحيح)

## الصفحات التي تحتاج فحص
- `CollectionScreen` - صفحة التحصيل
- `AddProductScreen` - صفحة إضافة منتج
- `AddCustomerScreen` - صفحة إضافة عميل
- `AddInvoiceScreen` - صفحة إضافة فاتورة

## نصائح للوقاية
1. دائماً استخدم `Scaffold` في الصفحات الرئيسية
2. تأكد من أن `TextField` موجود داخل `Scaffold`
3. استخدم `MaterialFix` helper للصفحات الجديدة
4. اختبر الصفحات التي تحتوي على `TextField` أو widgets أخرى من Material Design

## مثال كامل
```dart
class MyScreen extends StatefulWidget {
  @override
  _MyScreenState createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('عنوان الصفحة'),
      ),
      body: Column(
        children: [
          TextField(
            decoration: InputDecoration(
              labelText: 'حقل النص',
            ),
          ),
          // باقي المحتوى
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        child: Icon(Icons.add),
      ),
    );
  }
}
```
