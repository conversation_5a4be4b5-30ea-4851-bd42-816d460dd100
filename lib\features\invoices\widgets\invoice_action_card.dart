import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/invoice_action_model.dart';

class InvoiceActionCard extends StatelessWidget {
  final InvoiceActionModel action;
  final bool compact;

  const InvoiceActionCard({
    super.key,
    required this.action,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    if (compact) {
      return _buildCompactCard();
    }
    return _buildFullCard();
  }

  Widget _buildFullCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة - نوع الإجراء والمبلغ
            Row(
              children: [
                // أيقونة نوع الإجراء
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: _getActionTypeColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getActionTypeIcon(),
                    color: _getActionTypeColor(),
                    size: 20.sp,
                  ),
                ),

                SizedBox(width: 12.w),

                // معلومات الإجراء
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        action.actionTypeDisplayName,
                        style: TextStyle(
                          color: _getActionTypeColor(),
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      Text(
                        action.formattedAmount,
                        style: TextStyle(
                          color: _getActionTypeColor(),
                          fontWeight: FontWeight.bold,
                          fontSize: 18.sp,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ),

                // التاريخ والوقت
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      action.formattedDate,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12.sp,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    Text(
                      action.formattedTime,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12.sp,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // معلومات العميل
            _buildInfoRow(
              icon: Icons.person,
              label: 'العميل:',
              value: action.customerName,
            ),

            SizedBox(height: 8.h),

            // رقم الفاتورة
            _buildInfoRow(
              icon: Icons.receipt,
              label: 'رقم الفاتورة:',
              value: action.invoiceNumber,
            ),

            SizedBox(height: 8.h),

            // المحافظة
            _buildInfoRow(
              icon: Icons.location_on,
              label: 'المحافظة:',
              value: action.governorate,
            ),

            // الملاحظات (إذا وجدت)
            if (action.notes != null && action.notes!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12.sp,
                        color: Colors.grey[700],
                        fontFamily: 'Cairo',
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      action.notes!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactCard() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Row(
          children: [
            // أيقونة نوع الإجراء
            Container(
              padding: EdgeInsets.all(6.w),
              decoration: BoxDecoration(
                color: _getActionTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                _getActionTypeIcon(),
                color: _getActionTypeColor(),
                size: 16.sp,
              ),
            ),

            SizedBox(width: 12.w),

            // معلومات الإجراء
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    action.actionTypeDisplayName,
                    style: TextStyle(
                      color: _getActionTypeColor(),
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  Text(
                    '${action.customerName} - ${action.invoiceNumber}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: 'Cairo',
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // المبلغ والتاريخ
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  action.formattedAmount,
                  style: TextStyle(
                    color: _getActionTypeColor(),
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  action.formattedDate,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 10.sp,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16.sp, color: Colors.grey[600]),
        SizedBox(width: 8.w),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12.sp,
            fontFamily: 'Cairo',
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: Colors.grey[800],
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Color _getActionTypeColor() {
    switch (action.actionType) {
      case InvoiceActionType.payment:
        return Colors.green;
    }
  }

  IconData _getActionTypeIcon() {
    switch (action.actionType) {
      case InvoiceActionType.payment:
        return Icons.payment;
    }
  }
}
