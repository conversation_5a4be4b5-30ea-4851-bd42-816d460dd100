# ميزة إرسال تفاصيل العميل عبر SMS

## ملخص الميزة

تم إضافة ميزة جديدة في شاشة تفاصيل العميل تسمح بإرسال تفاصيل العميل الكاملة عبر رسالة SMS مباشرة إلى رقم العميل.

## المزايا المضافة

### 1. أزرار إرسال SMS
- **زر إرسال للهاتف 1**: يفتح تطبيق الرسائل مع رقم الهاتف الأول للعميل
- **زر إرسال للهاتف 2**: يفتح تطبيق الرسائل مع رقم الهاتف الثاني للعميل (إذا كان موجوداً)

### 2. تنسيق الرسالة
تحتوي الرسالة على جميع تفاصيل العميل:
```
تفاصيل العميل:
الاسم: [اسم العميل]
النشاط: [نشاط العميل]
الهاتف 1: [رقم الهاتف الأول]
الهاتف 2: [رقم الهاتف الثاني]
البريد الإلكتروني: [البريد الإلكتروني]
المحافظة: [المحافظة]
المدينة: [المدينة]
العنوان: [العنوان]
ملاحظات: [الملاحظات]
```

### 3. معالجة أرقام الهاتف
- تنظيف رقم الهاتف من الرموز غير الرقمية
- إضافة رمز الدولة لمصر (+20) تلقائياً
- التحقق من صحة رقم الهاتف قبل الإرسال

### 4. فتح تطبيق الرسائل مباشرة
- يفتح تطبيق الرسائل الافتراضي مباشرة بدون قائمة اختيار
- يدعم جميع أنواع أرقام الهواتف المصرية
- يعمل مع تطبيقات الرسائل المختلفة

### 5. رسائل التأكيد
- رسالة نجاح عند فتح تطبيق الرسائل
- رسائل خطأ في حالة عدم توفر تطبيق الرسائل أو حدوث خطأ

## كيفية الاستخدام

### 1. الوصول للميزة
1. افتح شاشة تفاصيل العميل
2. انتقل إلى قسم "معلومات الاتصال"
3. ستجد أزرار إرسال SMS أسفل معلومات الهاتف

### 2. إرسال الرسالة
1. اضغط على "إرسال للهاتف 1" أو "إرسال للهاتف 2"
2. سيتم فتح تطبيق الرسائل الافتراضي مباشرة (بدون قائمة اختيار)
3. ستجد الرسالة معبأة مسبقاً بجميع تفاصيل العميل
4. يمكنك تعديل الرسالة أو إرسالها كما هي

## الملفات المحدثة

### `lib/features/customers/screens/customer_details_screen.dart`
- إضافة import لـ `url_launcher`
- إضافة دالة `_buildSMSButtons()` لبناء أزرار SMS
- إضافة دالة `_sendCustomerDetailsViaSMS()` لإرسال الرسالة مع 8 طرق مختلفة
- إضافة دالة `_buildCustomerDetailsMessage()` لبناء نص الرسالة
- إضافة دالة `_formatPhoneNumber()` لتنسيق رقم الهاتف
- إضافة دالة `_showErrorSnackBar()` لعرض رسائل الخطأ
- تبسيط الكود باستخدام حلقة تكرارية ذكية

## المتطلبات التقنية

### Dependencies
```yaml
dependencies:
  url_launcher: ^6.0.0
```

### الأذونات المطلوبة

#### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
```

#### iOS (`ios/Runner/Info.plist`)
```xml
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>sms</string>
</array>
```

## المزايا التقنية

### 1. فتح تطبيق الرسائل مباشرة
- استخدام `LaunchMode.externalApplication` و `LaunchMode.platformDefault` لفتح التطبيق مباشرة
- تجنب قائمة اختيار التطبيق
- دعم متعدد للمحاولات في حالة فشل الطريقة الأولى
- استخدام حلقة تكرارية ذكية لتجربة 8 طرق مختلفة

### 2. معالجة الأخطاء
- التحقق من صحة رقم الهاتف
- معالجة أخطاء فتح تطبيق الرسائل
- رسائل خطأ واضحة للمستخدم

### 2. تجربة المستخدم
- أزرار واضحة ومميزة بألوان مختلفة
- رسائل تأكيد فورية
- تنسيق رسالة منظم وواضح

### 3. التوافق
- يفتح تطبيق الرسائل الافتراضي مباشرة بدون قائمة اختيار
- يعمل مع جميع تطبيقات الرسائل المتاحة
- يدعم أرقام الهواتف المصرية
- متوافق مع Android و iOS

### 4. الطرق المستخدمة لفتح الرسائل
1. **sms: مع النص + externalApplication**
2. **sms: بدون نص + externalApplication**
3. **sms:// مع النص + externalApplication**
4. **sms:// بدون نص + externalApplication**
5. **sms: مع النص + platformDefault**
6. **sms: بدون نص + platformDefault**
7. **sms:// مع النص + platformDefault**
8. **sms:// بدون نص + platformDefault**

## أمثلة الاستخدام

### مثال 1: عميل له هاتف واحد
```
تفاصيل العميل:
الاسم: أحمد محمد
النشاط: صيدلية
الهاتف 1: 01234567890
المحافظة: القاهرة
المدينة: المعادي
```

### مثال 2: عميل له هاتفين
```
تفاصيل العميل:
الاسم: فاطمة علي
النشاط: عيادة طبية
الهاتف 1: 01234567890
الهاتف 2: 09876543210
البريد الإلكتروني: <EMAIL>
المحافظة: الإسكندرية
العنوان: شارع البحر، برج النور
ملاحظات: عميل VIP
```

## التطوير المستقبلي

### ميزات مقترحة
1. **إرسال جماعي**: إرسال الرسالة لعدة عملاء في نفس الوقت
2. **قوالب رسائل**: إمكانية تخصيص قوالب الرسائل
3. **جدولة الإرسال**: إرسال الرسائل في وقت محدد
4. **تتبع الإرسال**: معرفة حالة الرسائل المرسلة
5. **إرسال عبر WhatsApp**: إضافة خيار إرسال عبر WhatsApp

### تحسينات تقنية
1. **تخزين مؤقت**: حفظ الرسائل المرسلة محلياً
2. **إحصائيات**: عرض إحصائيات الرسائل المرسلة
3. **تصدير**: تصدير قائمة الرسائل المرسلة
4. **فلترة**: فلترة الرسائل حسب التاريخ أو العميل

## الدعم والمساعدة

في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت تطبيق رسائل على الجهاز
2. تحقق من صحة رقم الهاتف
3. تأكد من وجود اتصال بالإنترنت
4. راجع أذونات التطبيق في إعدادات الجهاز

## الخلاصة

هذه الميزة تضيف قيمة كبيرة للتطبيق من خلال:
- تسهيل التواصل مع العملاء
- توفير الوقت في إرسال تفاصيل العميل
- تحسين تجربة المستخدم
- زيادة كفاءة العمل

تم تطوير هذه الميزة مع مراعاة أفضل الممارسات في تطوير Flutter وضمان التوافق مع جميع الأجهزة.
