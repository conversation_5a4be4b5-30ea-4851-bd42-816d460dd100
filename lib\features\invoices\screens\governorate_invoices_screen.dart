import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_service.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/delete_protection_dialog.dart';
import '../../../core/settings/app_settings.dart';
import '../widgets/invoice_card.dart';
import 'invoice_details_screen.dart';
import 'collect_invoice_screen.dart';

class GovernorateInvoicesScreen extends StatefulWidget {
  final String governorateName;
  final List<InvoiceModel> invoices;

  const GovernorateInvoicesScreen({
    super.key,
    required this.governorateName,
    required this.invoices,
  });

  @override
  State<GovernorateInvoicesScreen> createState() =>
      _GovernorateInvoicesScreenState();
}

class _GovernorateInvoicesScreenState extends State<GovernorateInvoicesScreen> {
  final InvoiceService _invoiceService = InvoiceService();
  List<InvoiceModel> _invoices = [];

  @override
  void initState() {
    super.initState();
    _invoices = List.from(widget.invoices);
  }

  Future<void> _deleteInvoice(InvoiceModel invoice) async {
    // التحقق من حماية الحذف
    final appSettings = AppSettings();
    final isDeleteProtected = await appSettings.isPasswordRequiredForInvoiceDelete();
    
    if (isDeleteProtected) {
      // استخدام حوار الحماية مع كلمة المرور
      showDeleteProtectionDialog(
        context: context,
        title: 'حماية حذف الفاتورة',
        message: 'حذف الفاتورة محمي بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة.',
        itemName: 'الفاتورة رقم ${invoice.invoiceNumber}',
        onConfirm: () async {
          try {
            await _invoiceService.deleteInvoice(invoice.id);
            setState(() {
              _invoices.removeWhere((i) => i.id == invoice.id);
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم حذف الفاتورة رقم ${invoice.invoiceNumber} بنجاح',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }

            // إرجاع النتيجة للشاشة الأم
            Navigator.of(context).pop({'deleted': true, 'invoiceId': invoice.id});
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('خطأ في حذف الفاتورة: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      );
    } else {
      // استخدام حوار التأكيد العادي
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text(
              'تأكيد الحذف',
              style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من حذف الفاتورة رقم ${invoice.invoiceNumber}؟\nلا يمكن التراجع عن هذا الإجراء.',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await _invoiceService.deleteInvoice(invoice.id);
                    setState(() {
                      _invoices.removeWhere((i) => i.id == invoice.id);
                    });

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'تم حذف الفاتورة رقم ${invoice.invoiceNumber} بنجاح',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }

                    // إرجاع النتيجة للشاشة الأم
                    Navigator.of(context).pop({'deleted': true, 'invoiceId': invoice.id});
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في حذف الفاتورة: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text(
                  'حذف',
                  style: TextStyle(
                    color: Colors.red,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _updateInvoiceStatus(
    InvoiceModel invoice,
    InvoiceStatus newStatus,
  ) async {
    try {
      await _invoiceService.updateInvoiceStatus(invoice.id, newStatus);

      // تحديث الفاتورة في القائمة
      setState(() {
        final index = _invoices.indexWhere((i) => i.id == invoice.id);
        if (index != -1) {
          _invoices[index] = _invoices[index].copyWith(status: newStatus);
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث حالة الفاتورة إلى ${newStatus == InvoiceStatus.paid ? 'تم الدفع بالكامل' : 'معلق'}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      // إرجاع النتيجة للشاشة الأم
      Navigator.of(context).pop({'updated': true, 'invoiceId': invoice.id});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _collectInvoice(InvoiceModel invoice) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CollectInvoiceScreen(invoice: invoice),
      ),
    );

    // إذا تم التحصيل بنجاح، قم بتحديث قائمة الفواتير
    if (result != null && result['success'] == true) {
      // تحديث الفاتورة في القائمة المحلية
      final updatedInvoice = await _invoiceService.getInvoice(invoice.id);
      if (updatedInvoice != null) {
        setState(() {
          final index = _invoices.indexWhere((i) => i.id == invoice.id);
          if (index != -1) {
            _invoices[index] = updatedInvoice;
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Row(
          children: [
            Expanded(
              child: Text(
                'فواتير ${widget.governorateName}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.receipt_long, color: Colors.white, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${_invoices.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
      ),
      body: _invoices.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long,
                    size: 64.sp,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد فواتير في ${widget.governorateName}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: () async {
                // إعادة تحميل الفواتير
                setState(() {});
              },
              color: AppColors.primary,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _invoices.length,
                itemBuilder: (context, index) {
                  final invoice = _invoices[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: InvoiceCard(
                      invoice: invoice,
                      onTap: () => _navigateToInvoiceDetails(invoice),
                      onDelete: () => _deleteInvoice(invoice),
                      onStatusUpdate: (status) =>
                          _updateInvoiceStatus(invoice, status),
                      onCollect: () => _collectInvoice(invoice),
                    ),
                  );
                },
              ),
            ),
    );
  }

  void _navigateToInvoiceDetails(InvoiceModel invoice) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InvoiceDetailsScreen(invoice: invoice),
      ),
    );

    if (result == true) {
      // إعادة تحميل الفواتير
      setState(() {});
    }
  }
}
