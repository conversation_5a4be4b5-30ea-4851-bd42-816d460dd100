import 'package:flutter/material.dart';
import '../models/customer_model.dart';
import 'database_service.dart';

class CustomerService {
  static final CustomerService _instance = CustomerService._internal();
  factory CustomerService() => _instance;
  CustomerService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// جلب جميع العملاء
  Future<List<CustomerModel>> getAllCustomers() async {
    try {
      return await _databaseService.getAllCustomers();
    } catch (e) {
      debugPrint('خطأ في جلب العملاء: $e');
      rethrow;
    }
  }

  /// جلب عميل بواسطة المعرف
  Future<CustomerModel?> getCustomerById(String id) async {
    try {
      return await _databaseService.getCustomerById(id);
    } catch (e) {
      debugPrint('خطأ في جلب العميل: $e');
      rethrow;
    }
  }

  /// إضافة عميل جديد - سريع جداً
  Future<bool> addCustomer(CustomerModel customer) async {
    try {
      // التحقق السريع من البيانات الأساسية
      if (customer.name.trim().isEmpty ||
          customer.phone1?.trim().isEmpty == true) {
        throw Exception('الاسم ورقم الهاتف مطلوبان');
      }

      // إنشاء نموذج العميل مع المعرف
      final customerToSave = customer.id.isEmpty
          ? customer.copyWith(
              id: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              createdBy: 'current_user',
            )
          : customer;

      // حفظ العميل في قاعدة البيانات - بدون تأخير
      await _databaseService.insertCustomer(customerToSave);

      return true;
    } catch (e) {
      debugPrint('فشل في إضافة العميل: $e');
      rethrow;
    }
  }

  /// تحديث عميل موجود - سريع جداً
  Future<bool> updateCustomer(CustomerModel customer) async {
    try {
      // التحقق السريع من البيانات الأساسية
      if (customer.name.trim().isEmpty ||
          customer.phone1?.trim().isEmpty == true) {
        throw Exception('الاسم ورقم الهاتف مطلوبان');
      }

      // تحديث العميل
      await _databaseService.updateCustomer(
        customer.copyWith(updatedAt: DateTime.now()),
      );

      return true;
    } catch (e) {
      debugPrint('فشل في تحديث العميل: $e');
      rethrow;
    }
  }

  /// حذف عميل - سريع جداً
  Future<bool> deleteCustomer(String id) async {
    try {
      await _databaseService.deleteCustomer(id);
      return true;
    } catch (e) {
      debugPrint('فشل في حذف العميل: $e');
      rethrow;
    }
  }

  /// البحث في العملاء
  Future<List<CustomerModel>> searchCustomers(String query) async {
    try {
      final allCustomers = await _databaseService.getAllCustomers();

      if (query.trim().isEmpty) {
        return allCustomers;
      }

      final lowercaseQuery = query.toLowerCase();
      return allCustomers.where((customer) {
        return customer.name.toLowerCase().contains(lowercaseQuery) ||
            (customer.phone1?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (customer.email?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            (customer.city?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            (customer.governorate?.toLowerCase().contains(lowercaseQuery) ??
                false);
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن العملاء: $e');
      rethrow;
    }
  }

  /// الحصول على العملاء حسب المحافظة
  Stream<Map<String, List<CustomerModel>>> getCustomersByGovernorate() {
    return Stream.fromFuture(_getCustomersByGovernorateAsync());
  }

  /// الحصول على العملاء الذين لديهم رصيد
  Future<List<CustomerModel>> getCustomersWithBalance() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'balance > 0 AND isActive = 1',
        orderBy: 'balance DESC',
      );

      return List.generate(maps.length, (i) {
        return CustomerModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('Error getting customers with balance: $e');
      return [];
    }
  }

  /// الحصول على جميع العملاء النشطين
  Stream<List<CustomerModel>> getActiveCustomers() {
    return Stream.fromFuture(_getActiveCustomersAsync());
  }

  /// الحصول على جميع العملاء
  Future<List<CustomerModel>> getCustomers() async {
    try {
      return await _databaseService.getAllCustomers();
    } catch (e) {
      print('Error getting customers: $e');
      return [];
    }
  }

  Future<Map<String, List<CustomerModel>>>
  _getCustomersByGovernorateAsync() async {
    try {
      final customers = await _databaseService.getAllCustomers();

      final Map<String, List<CustomerModel>> customersByGovernorate = {};
      for (final customer in customers) {
        final governorate = customer.governorate ?? 'غير محدد';
        customersByGovernorate.putIfAbsent(governorate, () => []);
        customersByGovernorate[governorate]!.add(customer);
      }

      return customersByGovernorate;
    } catch (e) {
      print('Error getting customers by governorate: $e');
      return {};
    }
  }

  /// الحصول على العملاء النشطين بشكل متزامن
  Future<List<CustomerModel>> _getActiveCustomersAsync() async {
    try {
      return await _databaseService.getAllCustomers();
    } catch (e) {
      print('Error getting active customers: $e');
      return [];
    }
  }

  /// التحقق من صحة بيانات العميل - مبسط
  bool _validateCustomerData(CustomerModel customer) {
    return customer.name.trim().isNotEmpty &&
        customer.phone1?.trim().isNotEmpty == true &&
        customer.governorate?.trim().isNotEmpty == true;
  }

  /// التحقق من عدم تكرار رقم الهاتف - مبسط
  Future<bool> _checkPhoneUniqueness(String? phone) async {
    if (phone == null || phone.trim().isEmpty) return true;

    try {
      final customers = await _databaseService.getAllCustomers();
      return !customers.any((c) => c.phone1 == phone.trim());
    } catch (e) {
      return true; // في حالة الخطأ، نسمح بالحفظ
    }
  }

  /// التحقق من عدم تكرار رقم الهاتف للتحديث - مبسط
  Future<bool> _checkPhoneUniquenessForUpdate(
    String? phone,
    String currentId,
  ) async {
    if (phone == null || phone.trim().isEmpty) return true;

    try {
      final customers = await _databaseService.getAllCustomers();
      return !customers.any(
        (c) => c.phone1 == phone.trim() && c.id != currentId,
      );
    } catch (e) {
      return true; // في حالة الخطأ، نسمح بالتحديث
    }
  }

  /// التحقق من صحة بيانات العميل
  Future<bool> isCustomerDataValid(CustomerModel customer, {String? excludeId}) async {
    try {
      // التحقق من البيانات الأساسية
      if (customer.name.trim().isEmpty || 
          (customer.governorate?.trim().isEmpty ?? true)) {
        return false;
      }
      
      // المدينة اختيارية، لا نحتاج للتحقق منها
      
      // التحقق من عدم وجود تكرارات
      final duplicates = await checkAllDuplicates(customer, excludeId: excludeId);
      
      // إذا كان هناك أي تكرار، البيانات غير صحيحة
      return !duplicates.values.any((isDuplicate) => isDuplicate);
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة بيانات العميل: $e');
      return false;
    }
  }

  /// التحقق من وجود اسم العميل
  Future<bool> isNameExists(String name, {String? excludeId}) async {
    try {
      if (name.trim().isEmpty) return false;
      final customers = await _databaseService.getAllCustomers();
      return customers.any(
        (c) => c.name.trim().toLowerCase() == name.trim().toLowerCase() && c.id != excludeId,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود اسم العميل: $e');
      rethrow;
    }
  }

  /// التحقق من وجود الرقم الضريبي
  Future<bool> isTaxNumberExists(String taxNumber, {String? excludeId}) async {
    try {
      if (taxNumber.trim().isEmpty) return false;
      final customers = await _databaseService.getAllCustomers();
      return customers.any(
        (c) => c.taxNumber?.trim().toLowerCase() == taxNumber.trim().toLowerCase() && c.id != excludeId,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الرقم الضريبي: $e');
      rethrow;
    }
  }

  /// التحقق من عدم تكرار رقم الهاتف
  Future<bool> isPhoneExists(String phone, {String? excludeId}) async {
    try {
      if (phone.trim().isEmpty) return false;
      
      // التحقق من صحة التنسيق أولاً
      if (!isValidPhoneFormat(phone)) {
        return false; // تنسيق غير صحيح
      }
      
      final customers = await _databaseService.getAllCustomers();
      final normalizedPhone = _normalizePhoneNumber(phone);
      
      return customers.any(
        (c) {
          // التحقق من رقم الهاتف الأول
          if (c.phone1?.trim().isNotEmpty == true) {
            final normalizedPhone1 = _normalizePhoneNumber(c.phone1!);
            if (normalizedPhone1 == normalizedPhone && c.id != excludeId) {
              return true;
            }
          }
          
          // التحقق من رقم الهاتف الثاني
          if (c.phone2?.trim().isNotEmpty == true) {
            final normalizedPhone2 = _normalizePhoneNumber(c.phone2!);
            if (normalizedPhone2 == normalizedPhone && c.id != excludeId) {
              return true;
            }
          }
          
          return false;
        },
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من رقم الهاتف: $e');
      return false;
    }
  }

  /// التحقق من عدم تكرار رقم الهاتف الثاني
  Future<bool> isPhone2Exists(String phone, {String? excludeId}) async {
    try {
      if (phone.trim().isEmpty) return false;
      
      // التحقق من صحة التنسيق أولاً
      if (!isValidPhoneFormat(phone)) {
        return false; // تنسيق غير صحيح
      }
      
      final customers = await _databaseService.getAllCustomers();
      final normalizedPhone = _normalizePhoneNumber(phone);
      
      return customers.any(
        (c) {
          // التحقق من رقم الهاتف الأول
          if (c.phone1?.trim().isNotEmpty == true) {
            final normalizedPhone1 = _normalizePhoneNumber(c.phone1!);
            if (normalizedPhone1 == normalizedPhone && c.id != excludeId) {
              return true;
            }
          }
          
          // التحقق من رقم الهاتف الثاني
          if (c.phone2?.trim().isNotEmpty == true) {
            final normalizedPhone2 = _normalizePhoneNumber(c.phone2!);
            if (normalizedPhone2 == normalizedPhone && c.id != excludeId) {
              return true;
            }
          }
          
          return false;
        },
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من رقم الهاتف الثاني: $e');
      return false;
    }
  }

  /// التحقق من عدم تكرار البريد الإلكتروني
  Future<bool> isEmailExists(String email, {String? excludeId}) async {
    try {
      if (email.trim().isEmpty) return false;

      final customers = await _databaseService.getAllCustomers();
      return customers.any(
        (c) =>
            c.email?.trim().toLowerCase() == email.trim().toLowerCase() &&
            c.id != excludeId,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من البريد الإلكتروني: $e');
      return false;
    }
  }

  /// التحقق من عدم تكرار أرقام الهاتف في نفس العميل
  bool _hasDuplicatePhonesInCustomer(CustomerModel customer) {
    if (customer.phone1?.trim().isEmpty == true ||
        customer.phone2?.trim().isEmpty == true) {
      return false;
    }

    final normalizedPhone1 = _normalizePhoneNumber(customer.phone1!);
    final normalizedPhone2 = _normalizePhoneNumber(customer.phone2!);

    return normalizedPhone1 == normalizedPhone2;
  }

  /// تطبيع رقم الهاتف (إزالة المسافات والرموز)
  String _normalizePhoneNumber(String phone) {
    // إزالة جميع المسافات والرموز غير الرقمية
    String normalized = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // إزالة الرموز الدولية إذا كانت موجودة
    if (normalized.startsWith('00')) {
      normalized = normalized.substring(2);
    } else if (normalized.startsWith('+')) {
      normalized = normalized.substring(1);
    }
    
    // إزالة الرمز الدولي لمصر إذا كان موجوداً
    if (normalized.startsWith('20')) {
      normalized = normalized.substring(2);
    }
    
    return normalized;
  }

  /// التحقق من صحة تنسيق رقم الهاتف
  bool isValidPhoneFormat(String phone) {
    if (phone.trim().isEmpty) return false;
    
    // تطبيع الرقم
    final normalized = _normalizePhoneNumber(phone);
    
    // التحقق من أن الرقم يحتوي على 10-11 رقم (مصر)
    if (normalized.length < 10 || normalized.length > 11) {
      return false;
    }
    
    // التحقق من أن الرقم يبدأ بـ 01 (أرقام مصر)
    if (!normalized.startsWith('01')) {
      return false;
    }
    
    return true;
  }

  /// التحقق من أن رقم الهاتف متاح (غير مستخدم)
  Future<bool> isPhoneAvailable(String phone, {String? excludeId}) async {
    try {
      if (!isValidPhoneFormat(phone)) {
        return false; // تنسيق غير صحيح
      }
      
      return !await isPhoneExists(phone, excludeId: excludeId);
    } catch (e) {
      debugPrint('خطأ في التحقق من توفر رقم الهاتف: $e');
      return false;
    }
  }

  /// التحقق الشامل من عدم التكرار
  Future<Map<String, bool>> checkAllDuplicates(
    CustomerModel customer, {
    String? excludeId,
  }) async {
    final results = <String, bool>{};

    // التحقق من الاسم
    if (customer.name.trim().isNotEmpty) {
      results['name'] = await isNameExists(customer.name, excludeId: excludeId);
    }

    // التحقق من رقم الهاتف الأول
    if (customer.phone1?.trim().isNotEmpty == true) {
      results['phone1'] = await isPhoneExists(
        customer.phone1!,
        excludeId: excludeId,
      );
    }

    // التحقق من رقم الهاتف الثاني
    if (customer.phone2?.trim().isNotEmpty == true) {
      results['phone2'] = await isPhone2Exists(
        customer.phone2!,
        excludeId: excludeId,
      );
    }

    // التحقق من عدم تكرار أرقام الهاتف في نفس العميل
    if (customer.phone1?.trim().isNotEmpty == true &&
        customer.phone2?.trim().isNotEmpty == true) {
      results['duplicate_phones'] = _hasDuplicatePhonesInCustomer(customer);
    }

    // التحقق من الرقم الضريبي
    if (customer.taxNumber?.trim().isNotEmpty == true) {
      results['tax_number'] = await isTaxNumberExists(
        customer.taxNumber!,
        excludeId: excludeId,
      );
    }

    // التحقق من البريد الإلكتروني
    if (customer.email?.trim().isNotEmpty == true) {
      results['email'] = await isEmailExists(
        customer.email!,
        excludeId: excludeId,
      );
    }

    return results;
  }
}
