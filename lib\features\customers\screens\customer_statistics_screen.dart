import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../constants/app_colors.dart';
import '../../../models/customer_model.dart';
import '../../../services/customer_service.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';
import '../../../utils/chart_painters.dart';

class CustomerStatisticsScreen extends ConsumerStatefulWidget {
  const CustomerStatisticsScreen({super.key});

  @override
  ConsumerState<CustomerStatisticsScreen> createState() => _CustomerStatisticsScreenState();
}

class _CustomerStatisticsScreenState extends ConsumerState<CustomerStatisticsScreen> {
  final CustomerService _customerService = CustomerService();
  final CollectionService _collectionService = CollectionService();
  
  bool _isLoading = false;
  List<CustomerModel> _allCustomers = [];
  Map<String, dynamic> _statistics = {};
  Map<String, int> _customersByType = {};
  Map<String, int> _customersByGovernorate = {};
  Map<String, int> _customersByMonth = {};
  double _totalPendingCollections = 0.0; // إضافة متغير لمبالغ التحصيل المستحقة

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);
    try {
      _allCustomers = await _customerService.getAllCustomers();
      
      // حساب مبالغ التحصيل المستحقة
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalPendingCollections = 0.0;
      for (final invoice in pendingInvoices) {
        totalPendingCollections += invoice.calculateRemainingAmount();
      }
      _totalPendingCollections = totalPendingCollections;
      
      _calculateStatistics();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _calculateStatistics() {
    // إحصائيات عامة
    final activeCustomers = _allCustomers.where((c) => c.isActive).length;
    final inactiveCustomers = _allCustomers.where((c) => !c.isActive).length;
    final customersWithBalance = _allCustomers.where((c) => c.balance != 0).length;
    final totalBalance = _allCustomers.fold(0.0, (sum, c) => sum + c.balance);
    final positiveBalance = _allCustomers.where((c) => c.balance > 0).fold(0.0, (sum, c) => sum + c.balance);
    final negativeBalance = _allCustomers.where((c) => c.balance < 0).fold(0.0, (sum, c) => sum + c.balance.abs());

    // العملاء حسب النوع
    _customersByType = {};
    for (final customer in _allCustomers) {
      final typeKey = _getCustomerTypeLabel(customer.type);
      _customersByType[typeKey] = (_customersByType[typeKey] ?? 0) + 1;
    }

    // العملاء حسب المحافظة
    _customersByGovernorate = {};
    for (final customer in _allCustomers) {
      final governorate = customer.governorate ?? 'غير محدد';
      _customersByGovernorate[governorate] = (_customersByGovernorate[governorate] ?? 0) + 1;
    }

    // العملاء حسب الشهر
    _customersByMonth = {};
    for (final customer in _allCustomers) {
      if (customer.createdAt != null) {
        final monthKey = '${customer.createdAt!.month}/${customer.createdAt!.year}';
        _customersByMonth[monthKey] = (_customersByMonth[monthKey] ?? 0) + 1;
      }
    }

    setState(() {
      _statistics = {
        'total': _allCustomers.length,
        'active': activeCustomers,
        'inactive': inactiveCustomers,
        'withBalance': customersWithBalance,
        'totalBalance': totalBalance,
        'positiveBalance': positiveBalance,
        'negativeBalance': negativeBalance,
        'averageBalance': _allCustomers.isNotEmpty ? totalBalance / _allCustomers.length : 0.0,
        'customersWithLocation': _allCustomers.where((c) => c.hasLocation).length,
        'customersWithEmail': _allCustomers.where((c) => c.email?.isNotEmpty == true).length,
        'customersWithTaxNumber': _allCustomers.where((c) => c.taxNumber?.isNotEmpty == true).length,
      };
    });
  }

  String _getCustomerTypeLabel(CustomerType type) {
    switch (type) {
      case CustomerType.distributor:
        return 'موزع';
      case CustomerType.medicalOfficeA:
        return 'مكتب طبي أ';
      case CustomerType.medicalOfficeB:
        return 'مكتب طبي ب';
      case CustomerType.majorClient:
        return 'عميل كبير';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          'إحصائيات العملاء',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white, size: 20.sp),
        actions: [
          IconButton(
            onPressed: _loadStatistics,
            icon: Icon(Icons.refresh, color: Colors.white, size: 24.sp),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الإحصائيات العامة
                  _buildGeneralStatistics(),
                  
                  SizedBox(height: 24.h),
                  
                  // العملاء حسب النوع
                  _buildCustomersByTypeChart(),
                  
                  SizedBox(height: 24.h),
                  
                  // العملاء حسب المحافظة
                  _buildCustomersByGovernorateChart(),
                  
                  SizedBox(height: 24.h),
                  
                  // العملاء حسب الشهر
                  _buildCustomersByMonthChart(),
                  
                  SizedBox(height: 24.h),
                  
                  // إحصائيات مالية
                  _buildFinancialStatistics(),
                  
                  SizedBox(height: 24.h),
                  
                  // إحصائيات إضافية
                  _buildAdditionalStatistics(),
                ],
              ),
            ),
    );
  }

  Widget _buildGeneralStatistics() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'الإحصائيات العامة',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.people,
                  label: 'إجمالي العملاء',
                  value: '${_statistics['total'] ?? 0}',
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.check_circle,
                  label: 'العملاء النشطين',
                  value: '${_statistics['active'] ?? 0}',
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.cancel,
                  label: 'العملاء غير النشطين',
                  value: '${_statistics['inactive'] ?? 0}',
                  color: AppColors.error,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.account_balance_wallet,
                  label: 'العملاء ذوي الرصيد',
                  value: '${_statistics['withBalance'] ?? 0}',
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersByTypeChart() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.pie_chart, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'العملاء حسب النوع',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          SizedBox(
            height: 200.h,
            child: CustomPieChart(
              data: _customersByType.entries.map((entry) {
                return PieChartData(
                  label: entry.key,
                  value: entry.value.toDouble(),
                  color: _getRandomColor(entry.key),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersByGovernorateChart() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'العملاء حسب المحافظة',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          SizedBox(
            height: 200.h,
            child: CustomBarChart(
              data: _customersByGovernorate.entries.map((entry) {
                return BarChartData(
                  label: entry.key,
                  value: entry.value.toDouble(),
                  color: _getRandomColor(entry.key),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersByMonthChart() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.timeline, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'العملاء حسب الشهر',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          SizedBox(
            height: 200.h,
            child: CustomLineChart(
              data: _customersByMonth.entries.map((entry) {
                return LineChartData(
                  label: entry.key,
                  value: entry.value.toDouble(),
                  color: AppColors.primary,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialStatistics() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.account_balance_wallet, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'الإحصائيات المالية',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.account_balance_wallet,
                  label: 'إجمالي الرصيد',
                  value: '${(_statistics['totalBalance'] ?? 0.0).toStringAsFixed(2)}',
                  color: AppColors.warning,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.trending_up,
                  label: 'الرصيد الإيجابي',
                  value: '${(_statistics['positiveBalance'] ?? 0.0).toStringAsFixed(2)}',
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.trending_down,
                  label: 'الرصيد السلبي',
                  value: '${(_statistics['negativeBalance'] ?? 0.0).toStringAsFixed(2)}',
                  color: AppColors.error,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.payment,
                  label: 'مبالغ التحصيل',
                  value: '${_totalPendingCollections.toStringAsFixed(2)}',
                  color: const Color(0xFF9C27B0), // Purple
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(String label, String value, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalStatistics() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: AppColors.primary, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'إحصائيات إضافية',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildAdditionalRow('العملاء ذوي الموقع', '${_statistics['customersWithLocation'] ?? 0}', Icons.map),
          _buildAdditionalRow('العملاء ذوي البريد الإلكتروني', '${_statistics['customersWithEmail'] ?? 0}', Icons.email),
          _buildAdditionalRow('العملاء ذوي الرقم الضريبي', '${_statistics['customersWithTaxNumber'] ?? 0}', Icons.receipt),
        ],
      ),
    );
  }

  Widget _buildAdditionalRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRandomColor(String key) {
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
    ];
    
    final index = key.hashCode % colors.length;
    return colors[index];
  }
}

// بيانات الرسوم البيانية
class PieChartData {
  final String label;
  final double value;
  final Color color;

  PieChartData({
    required this.label,
    required this.value,
    required this.color,
  });
}

class BarChartData {
  final String label;
  final double value;
  final Color color;

  BarChartData({
    required this.label,
    required this.value,
    required this.color,
  });
}

class LineChartData {
  final String label;
  final double value;
  final Color color;

  LineChartData({
    required this.label,
    required this.value,
    required this.color,
  });
}

// الرسوم البيانية المخصصة
class CustomPieChart extends StatelessWidget {
  final List<PieChartData> data;

  const CustomPieChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        'رسم بياني دائري للعملاء حسب النوع',
        style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16.sp,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}

class CustomBarChart extends StatelessWidget {
  final List<BarChartData> data;

  const CustomBarChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        'رسم بياني بالأعمدة للعملاء حسب المحافظة',
        style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16.sp,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}

class CustomLineChart extends StatelessWidget {
  final List<LineChartData> data;

  const CustomLineChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        'رسم بياني خطي للعملاء حسب الشهر',
        style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16.sp,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}
