import 'package:flutter/material.dart';
import 'dart:async';

import '../models/customer_model.dart';

import '../services/customer_service.dart';

/// مدير الحفظ التلقائي المحسن - يعمل كل جزء من الثانية بدون ظهور أي إدارة
class AutoSaveManager {
  static final AutoSaveManager _instance = AutoSaveManager._internal();
  factory AutoSaveManager() => _instance;
  AutoSaveManager._internal();

  final CustomerService _customerService = CustomerService();

  // Timer للحفظ التلقائي كل جزء من الثانية (500 مللي ثانية)
  Timer? _autoSaveTimer;

  // قائمة العمليات المعلقة للعملاء
  final List<Map<String, dynamic>> _pendingCustomerOperations = [];

  bool _isProcessingCustomers = false;

  // StreamController للتحديثات الفورية للعملاء
  final StreamController<List<CustomerModel>> _customersUpdateController =
      StreamController<List<CustomerModel>>.broadcast();

  // Stream للتحديثات الفورية للعملاء
  Stream<List<CustomerModel>> get customersUpdateStream =>
      _customersUpdateController.stream;

  /// بدء نظام الحفظ التلقائي
  void startAutoSave() {
    // إيقاف Timer السابق إذا كان موجوداً
    _autoSaveTimer?.cancel();

    // بدء Timer جديد يعمل كل جزء من الثانية (500 مللي ثانية)
    _autoSaveTimer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      _processPendingCustomerOperations();
    });

    debugPrint('تم بدء نظام الحفظ التلقائي - يعمل كل جزء من الثانية');
  }

  /// إيقاف نظام الحفظ التلقائي
  void stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
    debugPrint('تم إيقاف نظام الحفظ التلقائي');
  }

  /// إضافة عميل جديد مع حفظ تلقائي فوري وتحديث القائمة
  Future<bool> addCustomerWithAutoSave(
    CustomerModel customer, {
    required BuildContext context,
    bool showNotification = false, // إيقاف الإشعارات
  }) async {
    try {
      // حفظ فوري في قاعدة البيانات
      final result = await _customerService.addCustomer(customer);

      if (result) {
        // تحديث فوري للقائمة
        await _updateCustomersList();
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في إضافة العميل: $e');
      return false;
    }
  }

  /// تحديث عميل مع حفظ تلقائي فوري وتحديث القائمة
  Future<bool> updateCustomerWithAutoSave(
    CustomerModel customer, {
    required BuildContext context,
    bool showNotification = false, // إيقاف الإشعارات
  }) async {
    try {
      // تحديث فوري في قاعدة البيانات
      final result = await _customerService.updateCustomer(customer);

      if (result) {
        // تحديث فوري للقائمة
        await _updateCustomersList();
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في تحديث العميل: $e');
      return false;
    }
  }

  /// حذف عميل مع حذف تلقائي فوري وتحديث القائمة
  Future<bool> deleteCustomerWithAutoSave(
    String customerId, {
    required BuildContext context,
    bool showNotification = false, // إيقاف الإشعارات
  }) async {
    try {
      // حذف فوري من قاعدة البيانات
      final result = await _customerService.deleteCustomer(customerId);

      if (result) {
        // تحديث فوري للقائمة
        await _updateCustomersList();
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في حذف العميل: $e');
      return false;
    }
  }

  // ========== العمليات المعلقة للعملاء ==========

  /// إضافة عملية عميل للقائمة المعلقة
  void addPendingCustomerOperation(
    String operation,
    Map<String, dynamic> data,
  ) {
    _pendingCustomerOperations.add({
      'operation': operation,
      'data': data,
      'timestamp': DateTime.now(),
    });

    debugPrint('تمت إضافة عملية عميل معلقة: $operation');
  }

  /// معالجة العمليات المعلقة للعملاء
  Future<void> _processPendingCustomerOperations() async {
    if (_isProcessingCustomers || _pendingCustomerOperations.isEmpty) return;

    _isProcessingCustomers = true;

    try {
      final operationsToProcess = List<Map<String, dynamic>>.from(
        _pendingCustomerOperations,
      );
      _pendingCustomerOperations.clear();

      for (final operation in operationsToProcess) {
        try {
          switch (operation['operation']) {
            case 'add':
              await _customerService.addCustomer(operation['data']['customer']);
              break;
            case 'update':
              await _customerService.updateCustomer(
                operation['data']['customer'],
              );
              break;
            case 'delete':
              await _customerService.deleteCustomer(
                operation['data']['customerId'],
              );
              break;
          }
        } catch (e) {
          debugPrint('خطأ في معالجة العملية المعلقة للعميل: $e');
          // إعادة إضافة العملية الفاشلة للقائمة
          _pendingCustomerOperations.add(operation);
        }
      }

      // تحديث القائمة بعد معالجة العمليات
      if (operationsToProcess.isNotEmpty) {
        await _updateCustomersList();
      }
    } catch (e) {
      debugPrint('خطأ في معالجة العمليات المعلقة للعملاء: $e');
    } finally {
      _isProcessingCustomers = false;
    }
  }

  // ========== تحديث القوائم ==========

  /// تحديث قائمة العملاء فوراً
  Future<void> _updateCustomersList() async {
    try {
      debugPrint('بدء تحديث قائمة العملاء...');
      final customers = await _customerService.getAllCustomers();
      debugPrint('تم جلب ${customers.length} عميل');

      _customersUpdateController.add(customers);
      debugPrint('تم تحديث stream العملاء بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحديث قائمة العملاء: $e');
      debugPrint('تفاصيل الخطأ: ${e.toString()}');

      // إرسال قائمة فارغة في حالة الخطأ
      _customersUpdateController.add([]);
      debugPrint('تم إرسال قائمة فارغة للعملاء');
    }
  }

  // ========== المزامنة ==========

  /// مزامنة جميع العمليات المعلقة للعملاء
  Future<void> syncAllPendingCustomerOperations() async {
    await _processPendingCustomerOperations();
  }

  /// مزامنة جميع العمليات المعلقة
  Future<void> syncAllPendingOperations() async {
    await _processPendingCustomerOperations();
  }

  /// حفظ فوري للعميل مع تحديث القائمة
  Future<bool> saveCustomerImmediately(
    CustomerModel customer, {
    required BuildContext context,
    bool showNotification = false, // إيقاف الإشعارات
  }) async {
    try {
      // حفظ فوري في قاعدة البيانات
      final result = await _customerService.addCustomer(customer);

      if (result) {
        // تحديث فوري للقائمة
        await _updateCustomersList();
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في حفظ العميل: $e');
      return false;
    }
  }

  /// إغلاق الموارد
  void dispose() {
    _autoSaveTimer?.cancel();
    _customersUpdateController.close();
  }
}
