import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/egyptian_governorates.dart';
import '../../../models/invoice_action_model.dart';
import '../../../services/invoice_action_service.dart';
import '../../../widgets/back_button.dart';
import '../widgets/invoice_action_card.dart';

class InvoiceActionsScreen extends StatefulWidget {
  const InvoiceActionsScreen({super.key});

  @override
  State<InvoiceActionsScreen> createState() => _InvoiceActionsScreenState();
}

class _InvoiceActionsScreenState extends State<InvoiceActionsScreen>
    with SingleTickerProviderStateMixin {
  final InvoiceActionService _actionService = InvoiceActionService();
  late TabController _tabController;

  List<InvoiceActionModel> _allActions = [];
  Map<String, List<InvoiceActionModel>> _actionsByGovernorate = {};
  List<InvoiceActionModel> _filteredActions = [];
  bool _isLoading = true;
  String _errorMessage = '';
  String _searchQuery = '';
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadActions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadActions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final allActions = await _actionService.getAllInvoiceActions();

      // تجميع الإجراءات حسب المحافظة
      final Map<String, List<InvoiceActionModel>> actionsByGovernorate = {};
      for (final governorate in EgyptianGovernorates.getAllGovernorates()) {
        actionsByGovernorate[governorate] = [];
      }

      for (final action in allActions) {
        if (action.governorate.isNotEmpty &&
            actionsByGovernorate.containsKey(action.governorate)) {
          actionsByGovernorate[action.governorate]!.add(action);
        }
      }

      setState(() {
        _allActions = allActions;
        _actionsByGovernorate = actionsByGovernorate;
        _filteredActions = allActions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل إجراءات الفواتير: $e';
        _isLoading = false;
      });
    }
  }

  void _filterActions() {
    setState(() {
      _filteredActions = _allActions.where((action) {
        bool matchesSearch =
            _searchQuery.isEmpty ||
            action.customerName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            action.invoiceNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );

        bool matchesDate = true;
        if (_selectedStartDate != null && _selectedEndDate != null) {
          matchesDate =
              action.actionDate.isAfter(
                _selectedStartDate!.subtract(const Duration(days: 1)),
              ) &&
              action.actionDate.isBefore(
                _selectedEndDate!.add(const Duration(days: 1)),
              );
        }

        return matchesSearch && matchesDate;
      }).toList();
    });
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedStartDate != null && _selectedEndDate != null
          ? DateTimeRange(start: _selectedStartDate!, end: _selectedEndDate!)
          : null,
      locale: const Locale('ar', 'EG'),
    );

    if (picked != null) {
      setState(() {
        _selectedStartDate = picked.start;
        _selectedEndDate = picked.end;
      });
      _filterActions();
    }
  }

  void _clearDateFilter() {
    setState(() {
      _selectedStartDate = null;
      _selectedEndDate = null;
    });
    _filterActions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'إجراءات الفواتير',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.list)),
            Tab(text: 'حسب المحافظات', icon: Icon(Icons.location_on)),
            Tab(text: 'حسب التاريخ', icon: Icon(Icons.calendar_today)),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          _buildSearchAndFilterBar(),

          // محتوى التاب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllActionsTab(),
                _buildGovernoratesTab(),
                _buildDateFilterTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // حقل البحث
          TextField(
            onChanged: (value) {
              _searchQuery = value;
              _filterActions();
            },
            decoration: InputDecoration(
              hintText: 'البحث في العميل أو رقم الفاتورة...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
          ),

          SizedBox(height: 12.h),

          // فلترة التاريخ
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.calendar_today),
                  label: Text(
                    _selectedStartDate != null && _selectedEndDate != null
                        ? '${_selectedStartDate!.day}/${_selectedStartDate!.month} - ${_selectedEndDate!.day}/${_selectedEndDate!.month}'
                        : 'اختيار فترة زمنية',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              if (_selectedStartDate != null && _selectedEndDate != null) ...[
                SizedBox(width: 8.w),
                IconButton(
                  onPressed: _clearDateFilter,
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح الفلترة',
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAllActionsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return _buildErrorWidget();
    }

    if (_filteredActions.isEmpty) {
      return _buildEmptyState('لا توجد إجراءات فواتير');
    }

    return RefreshIndicator(
      onRefresh: _loadActions,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _filteredActions.length,
        itemBuilder: (context, index) {
          final action = _filteredActions[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: InvoiceActionCard(action: action),
          );
        },
      ),
    );
  }

  Widget _buildGovernoratesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return _buildErrorWidget();
    }

    final governorates = EgyptianGovernorates.getAllGovernorates();
    final governoratesWithActions = governorates
        .where(
          (governorate) =>
              _actionsByGovernorate[governorate]?.isNotEmpty == true,
        )
        .toList();

    if (governoratesWithActions.isEmpty) {
      return _buildEmptyState('لا توجد إجراءات فواتير في أي محافظة');
    }

    return RefreshIndicator(
      onRefresh: _loadActions,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: governoratesWithActions.length,
        itemBuilder: (context, index) {
          final governorate = governoratesWithActions[index];
          final actions = _actionsByGovernorate[governorate] ?? [];

          return _buildGovernorateActionsCard(governorate, actions);
        },
      ),
    );
  }

  Widget _buildDateFilterTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return _buildErrorWidget();
    }

    // تجميع الإجراءات حسب التاريخ
    final Map<String, List<InvoiceActionModel>> actionsByDate = {};
    for (final action in _filteredActions) {
      final dateKey =
          '${action.actionDate.year}-${action.actionDate.month.toString().padLeft(2, '0')}-${action.actionDate.day.toString().padLeft(2, '0')}';
      if (!actionsByDate.containsKey(dateKey)) {
        actionsByDate[dateKey] = [];
      }
      actionsByDate[dateKey]!.add(action);
    }

    final sortedDates = actionsByDate.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    if (sortedDates.isEmpty) {
      return _buildEmptyState('لا توجد إجراءات فواتير في الفترة المحددة');
    }

    return RefreshIndicator(
      onRefresh: _loadActions,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: sortedDates.length,
        itemBuilder: (context, index) {
          final dateKey = sortedDates[index];
          final actions = actionsByDate[dateKey] ?? [];
          final date = DateTime.parse(dateKey);

          return _buildDateActionsCard(date, actions);
        },
      ),
    );
  }

  Widget _buildGovernorateActionsCard(
    String governorate,
    List<InvoiceActionModel> actions,
  ) {
    // حساب الإحصائيات
    double totalPayments = 0.0;
    int paymentsCount = 0;

    for (final action in actions) {
      if (action.actionType == InvoiceActionType.payment) {
        totalPayments += action.amount;
        paymentsCount++;
      }
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          child: Text(
            governorate.substring(0, 1),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          governorate,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16.sp,
            fontFamily: 'Cairo',
          ),
        ),
        subtitle: Text(
          '${actions.length} إجراء - ${totalPayments.toStringAsFixed(2)} ر.س',
          style: TextStyle(color: Colors.grey[600]),
        ),
        children: [
          // إحصائيات المحافظة
          Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'دفع',
                    paymentsCount.toString(),
                    '${totalPayments.toStringAsFixed(2)} ر.س',
                    Colors.green,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الإجراءات
          ...actions.map(
            (action) => Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
              child: InvoiceActionCard(action: action, compact: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateActionsCard(
    DateTime date,
    List<InvoiceActionModel> actions,
  ) {
    // حساب الإحصائيات
    double totalPayments = 0.0;
    int paymentsCount = 0;

    for (final action in actions) {
      if (action.actionType == InvoiceActionType.payment) {
        totalPayments += action.amount;
        paymentsCount++;
      }
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          child: Icon(Icons.calendar_today, color: Colors.white),
        ),
        title: Text(
          '${date.day}/${date.month}/${date.year}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16.sp,
            fontFamily: 'Cairo',
          ),
        ),
        subtitle: Text(
          '${actions.length} إجراء - ${totalPayments.toStringAsFixed(2)} ر.س',
          style: TextStyle(color: Colors.grey[600]),
        ),
        children: [
          // إحصائيات اليوم
          Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'دفع',
                    paymentsCount.toString(),
                    '${totalPayments.toStringAsFixed(2)} ر.س',
                    Colors.green,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الإجراءات
          ...actions.map(
            (action) => Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
              child: InvoiceActionCard(action: action, compact: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String count,
    String amount,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12.sp,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            count,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 18.sp,
            ),
          ),
          Text(
            amount,
            style: TextStyle(color: color, fontSize: 10.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل إجراءات الفواتير',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _errorMessage,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _loadActions,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long, size: 64.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            message,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16.sp,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
