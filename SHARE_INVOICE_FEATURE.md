# ميزة مشاركة الفاتورة كصورة

## نظرة عامة
تم إضافة ميزة جديدة لمشاركة تفاصيل الفاتورة كصورة مع خيارات مشاركة متعددة عبر واتساب ورسالة هاتف.

## الميزات المضافة

### 1. زر مشاركة كصورة
- يقوم بإنشاء صورة من تفاصيل الفاتورة
- يفتح نافذة المشاركة العامة
- يمكن مشاركة الصورة عبر أي تطبيق

### 2. زر واتساب + صورة
- يقوم بإنشاء صورة من الفاتورة
- يفتح واتساب تلقائياً
- يضيف رقم العميل تلقائياً
- يضيف نص الفاتورة تلقائياً

### 3. زر رسالة + صورة
- يقوم بإنشاء صورة من الفاتورة
- يفتح تطبيق الرسائل تلقائياً
- يضيف رقم العميل تلقائياً
- يضيف نص الفاتورة تلقائياً

## كيفية الاستخدام

### الخطوة 1: اختيار تحصيل
- اختر تحصيل من القائمة
- تأكد من تحميل تفاصيل الفاتورة والعميل

### الخطوة 2: اختيار نوع المشاركة
- **مشاركة كصورة**: مشاركة عامة عبر أي تطبيق
- **واتساب + صورة**: مشاركة مباشرة عبر واتساب
- **رسالة + صورة**: مشاركة مباشرة عبر رسائل الهاتف

### الخطوة 3: تأكيد المشاركة
- سيتم إنشاء صورة من الفاتورة تلقائياً
- ستفتح نافذة المشاركة المطلوبة
- يمكن إرسال الصورة مع النص

## محتوى الصورة

### رأس الفاتورة
- عنوان "فاتورة تحصيل"
- رقم الفاتورة

### معلومات العميل
- اسم العميل
- رقم الهاتف
- العنوان

### تفاصيل الفاتورة
- تاريخ الفاتورة
- المبلغ الإجمالي
- المبلغ المحصل
- نوع التحصيل
- تاريخ التحصيل

### ملاحظات
- ملاحظات الفاتورة (إن وجدت)

### تذييل الفاتورة
- رسالة "تم إنشاء هذه الفاتورة بواسطة نظام التحصيل"

## المتطلبات التقنية

### المكتبات المطلوبة
```yaml
dependencies:
  share_plus: ^7.2.1
  path_provider: ^2.1.4
```

### الأذونات المطلوبة
- **Android**: `WRITE_EXTERNAL_STORAGE` (لحفظ الصور المؤقتة)
- **iOS**: لا توجد أذونات إضافية مطلوبة

## الملفات المعدلة

### `customer_collection_reminder_screen.dart`
- إضافة دوال إنشاء الصور
- إضافة دوال المشاركة
- إضافة أزرار المشاركة في الواجهة
- إضافة Widget لبناء صورة الفاتورة

## ملاحظات مهمة

### جودة الصورة
- يتم إنشاء الصورة بدقة عالية (pixelRatio: 3.0)
- حجم الصورة: 400 بكسل عرض
- تنسيق PNG للحصول على أفضل جودة

### التخزين
- يتم حفظ الصور في مجلد مؤقت
- يتم حذف الصور تلقائياً عند إعادة تشغيل التطبيق
- اسم الملف: `invoice_[رقم_الفاتورة].png`

### التوافق
- يعمل على جميع أحجام الشاشات
- تخطيط متجاوب للهاتف والتابلت
- دعم اللغة العربية والاتجاه RTL

## استكشاف الأخطاء

### مشكلة: فشل في إنشاء الصورة
**الحل**: تأكد من اختيار تحصيل صحيح مع تفاصيل كاملة

### مشكلة: فشل في فتح واتساب
**الحل**: سيتم فتح نافذة المشاركة العامة كبديل

### مشكلة: فشل في فتح الرسائل
**الحل**: سيتم فتح نافذة المشاركة العامة كبديل

## التطوير المستقبلي

### ميزات مقترحة
- إضافة خيارات تخصيص الصورة
- دعم تنسيقات إضافية (PDF, JPG)
- إضافة علامة مائية
- دعم التوقيع الإلكتروني

### تحسينات تقنية
- تحسين جودة الصورة
- إضافة خيارات ضغط
- دعم الطباعة المباشرة
- مشاركة متعددة في وقت واحد
