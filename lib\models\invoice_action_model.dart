import 'dart:convert';

enum InvoiceActionType { payment }

class InvoiceActionModel {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String governorate;
  final InvoiceActionType actionType;
  final double amount;
  final DateTime actionDate;
  final String? notes;
  final String? createdBy;

  const InvoiceActionModel({
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.governorate,
    required this.actionType,
    required this.amount,
    required this.actionDate,
    this.notes,
    this.createdBy,
  });

  factory InvoiceActionModel.fromJson(Map<String, dynamic> json) {
    return InvoiceActionModel(
      id: json['id'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      governorate: json['governorate'] ?? '',
      actionType: _parseActionType(json['actionType']),
      amount: (json['amount'] ?? 0.0).toDouble(),
      actionDate: DateTime.parse(
        json['actionDate'] ?? DateTime.now().toIso8601String(),
      ),
      notes: json['notes'],
      createdBy: json['createdBy'],
    );
  }

  factory InvoiceActionModel.fromMap(Map<String, dynamic> map) {
    return InvoiceActionModel(
      id: map['id'] ?? '',
      invoiceId: map['invoiceId'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      governorate: map['governorate'] ?? '',
      actionType: _parseActionType(map['actionType']),
      amount: (map['amount'] ?? 0.0).toDouble(),
      actionDate: DateTime.parse(
        map['actionDate'] ?? DateTime.now().toIso8601String(),
      ),
      notes: map['notes'],
      createdBy: map['createdBy'],
    );
  }

  static InvoiceActionType _parseActionType(dynamic value) {
    if (value == null) return InvoiceActionType.payment;

    final String typeString = value.toString().toLowerCase();
    switch (typeString) {
      case 'payment':
      case '0':
      default:
        return InvoiceActionType.payment;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'governorate': governorate,
      'actionType': actionType.index,
      'amount': amount,
      'actionDate': actionDate.toIso8601String(),
      'notes': notes,
      'createdBy': createdBy,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'governorate': governorate,
      'actionType': actionType.index,
      'amount': amount,
      'actionDate': actionDate.toIso8601String(),
      'notes': notes,
      'createdBy': createdBy,
    };
  }

  InvoiceActionModel copyWith({
    String? id,
    String? invoiceId,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? governorate,
    InvoiceActionType? actionType,
    double? amount,
    DateTime? actionDate,
    String? notes,
    String? createdBy,
  }) {
    return InvoiceActionModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      governorate: governorate ?? this.governorate,
      actionType: actionType ?? this.actionType,
      amount: amount ?? this.amount,
      actionDate: actionDate ?? this.actionDate,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  // الحصول على نوع الإجراء بالعربية
  String get actionTypeDisplayName {
    switch (actionType) {
      case InvoiceActionType.payment:
        return 'دفع';
    }
  }

  // الحصول على أيقونة نوع الإجراء
  String get actionTypeIcon {
    switch (actionType) {
      case InvoiceActionType.payment:
        return 'payment';
    }
  }

  // الحصول على لون نوع الإجراء
  String get actionTypeColor {
    switch (actionType) {
      case InvoiceActionType.payment:
        return '#4CAF50'; // أخضر
    }
  }

  // الحصول على تاريخ الإجراء المنسق
  String get formattedDate {
    return '${actionDate.day}/${actionDate.month}/${actionDate.year}';
  }

  // الحصول على وقت الإجراء المنسق
  String get formattedTime {
    final hour = actionDate.hour;
    final minute = actionDate.minute;
    final period = hour >= 12 ? 'م' : 'ص'; // م = PM, ص = AM
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  // الحصول على المبلغ المنسق
  String get formattedAmount {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  @override
  String toString() {
    return 'InvoiceActionModel(\n'
        '  id: "$id",\n'
        '  invoiceId: "$invoiceId",\n'
        '  invoiceNumber: "$invoiceNumber",\n'
        '  customerName: "$customerName",\n'
        '  governorate: "$governorate",\n'
        '  actionType: ${actionType.toString().split('.').last},\n'
        '  amount: $amount,\n'
        '  actionDate: ${actionDate.toIso8601String()},\n'
        '  notes: "$notes",\n'
        ')';
  }
}
