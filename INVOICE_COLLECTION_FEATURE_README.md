# ميزة تحصيل الفواتير السابقة

## نظرة عامة

تم إضافة ميزة تحصيل الفواتير السابقة التي تسمح بتحصيل مبالغ من الفواتير التي لم يتم دفعها بالكامل بعد مرور 15 دقيقة من إنشائها. هذه الميزة تساعد في إدارة المدفوعات الجزئية والمتأخرة بشكل فعال.

## الميزات الرئيسية

### 1. زر التحصيل الذكي
- **ظهور مشروط**: يظهر زر التحصيل فقط للفواتير المؤهلة
- **شروط الظهور**:
  - الفاتورة ليست ملغية
  - الفاتورة لم يتم دفعها بالكامل
  - مرور 15 دقيقة على الأقل من إنشاء الفاتورة

### 2. شاشة تحصيل متكاملة
- **معلومات الفاتورة**: عرض تفاصيل الفاتورة والعميل
- **حالة الدفع**: عرض المبلغ المتبقي ونسبة الدفع
- **حقل المبلغ**: مع التحقق من صحة المبلغ المدخل
- **حقل الملاحظات**: لتسجيل تفاصيل التحصيل
- **حساب تلقائي**: المبلغ المتبقي يتم حسابه تلقائياً

### 3. التحقق من صحة البيانات
- **التحقق من المبلغ**: التأكد من أن المبلغ لا يتجاوز المبلغ المتبقي
- **التحقق من القيمة**: التأكد من إدخال مبلغ صحيح
- **رسائل خطأ واضحة**: إرشادات للمستخدم في حالة الخطأ

## كيفية الاستخدام

### 1. الوصول لزر التحصيل
- **من شاشة الفواتير الرئيسية**: زر التحصيل يظهر في بطاقة الفاتورة
- **من شاشة فواتير العميل**: زر التحصيل يظهر في بطاقة الفاتورة
- **من شاشة فواتير المحافظة**: زر التحصيل يظهر في بطاقة الفاتورة

### 2. عملية التحصيل
1. **الضغط على زر التحصيل**: يفتح شاشة تحصيل الفاتورة
2. **مراجعة المعلومات**: عرض تفاصيل الفاتورة وحالة الدفع
3. **إدخال المبلغ**: المبلغ المتبقي يتم تعيينه تلقائياً
4. **إضافة ملاحظات**: اختياري لتسجيل تفاصيل التحصيل
5. **تأكيد التحصيل**: الضغط على زر "تحصيل المبلغ"

### 3. النتائج
- **تحديث الفاتورة**: يتم تحديث المبلغ المدفوع تلقائياً
- **تحديث الحالة**: تحديث حالة الفاتورة حسب المبلغ المدفوع
- **تسجيل التحصيل**: إنشاء سجل تحصيل في قاعدة البيانات
- **تحديث الواجهة**: تحديث جميع الشاشات المعنية

## الملفات المضافة والمعدلة

### ملفات جديدة
1. **`lib/features/invoices/screens/collect_invoice_screen.dart`**
   - شاشة تحصيل الفاتورة الرئيسية
   - نموذج إدخال المبلغ والملاحظات
   - التحقق من صحة البيانات
   - واجهة مستخدم متجاوبة

### ملفات معدلة
1. **`lib/features/invoices/widgets/invoice_card.dart`**
   - إضافة زر التحصيل
   - إضافة callback للتحصيل
   - منطق ظهور الزر المشروط

2. **`lib/features/invoices/screens/customer_invoices_screen.dart`**
   - إضافة دالة التحصيل
   - ربط زر التحصيل بالوظيفة
   - تحديث القائمة بعد التحصيل

3. **`lib/features/invoices/screens/invoices_screen.dart`**
   - إضافة دالة التحصيل
   - ربط زر التحصيل بالوظيفة
   - تحديث القائمة بعد التحصيل

4. **`lib/features/invoices/screens/governorate_invoices_screen.dart`**
   - إضافة دالة التحصيل
   - ربط زر التحصيل بالوظيفة
   - تحديث القائمة بعد التحصيل

## الميزات التقنية

### 1. حساب المبالغ
```dart
// حساب المبلغ المتبقي
double calculateRemainingAmount() {
  return total - paidAmount;
}

// حساب نسبة الدفع
double calculatePaymentPercentage() {
  if (total > 0) {
    return (paidAmount / total) * 100;
  }
  return 0.0;
}
```

### 2. التحقق من الأهلية
```dart
bool _shouldShowCollectButton() {
  // التحقق من أن الفاتورة ليست ملغية
  if (invoice.status == InvoiceStatus.cancelled) {
    return false;
  }
  
  // التحقق من أن الفاتورة لم يتم دفعها بالكامل
  if (invoice.isFullyPaid) {
    return false;
  }
  
  // التحقق من أن الفاتورة سابقة (أكثر من 15 دقيقة)
  final now = DateTime.now();
  final timeDifference = now.difference(invoice.createdAt);
  final fifteenMinutes = const Duration(minutes: 15);
  
  return timeDifference >= fifteenMinutes;
}
```

### 3. التحقق من صحة المبلغ
```dart
validator: (value) {
  if (value == null || value.isEmpty) {
    return 'يرجى إدخال مبلغ التحصيل';
  }
  
  final amount = double.tryParse(value);
  if (amount == null || amount <= 0) {
    return 'يرجى إدخال مبلغ صحيح';
  }
  
  final remainingAmount = widget.invoice.calculateRemainingAmount();
  if (amount > remainingAmount) {
    return 'مبلغ التحصيل يتجاوز المبلغ المتبقي (${remainingAmount.toStringAsFixed(2)} ج.م)';
  }
  
  return null;
}
```

## الأمان والتحقق

### 1. حماية البيانات
- **التحقق من المبلغ**: منع تحصيل مبالغ تتجاوز المبلغ المتبقي
- **التحقق من الوقت**: منع تحصيل الفواتير الحديثة (أقل من 15 دقيقة)
- **التحقق من الحالة**: منع تحصيل الفواتير الملغية

### 2. تسجيل العمليات
- **سجل التحصيل**: كل عملية تحصيل يتم تسجيلها في قاعدة البيانات
- **تتبع التغييرات**: تحديث المبلغ المدفوع وحالة الفاتورة
- **تاريخ التحصيل**: تسجيل وقت التحصيل

### 3. تحديث الواجهة
- **تحديث فوري**: تحديث جميع الشاشات بعد التحصيل
- **تحديث الحالة**: تغيير حالة الفاتورة حسب المبلغ المدفوع
- **تحديث الأزرار**: إخفاء زر التحصيل بعد الدفع الكامل

## الاختبار

### 1. اختبارات الوظائف
- [ ] ظهور زر التحصيل للفواتير المؤهلة
- [ ] عدم ظهور الزر للفواتير المدفوعة بالكامل
- [ ] عدم ظهور الزر للفواتير الملغية
- [ ] عدم ظهور الزر للفواتير الحديثة (أقل من 15 دقيقة)

### 2. اختبارات التحقق
- [ ] التحقق من صحة المبلغ المدخل
- [ ] منع تحصيل مبالغ تتجاوز المبلغ المتبقي
- [ ] التحقق من إدخال مبالغ صحيحة
- [ ] عرض رسائل خطأ واضحة

### 3. اختبارات التحديث
- [ ] تحديث المبلغ المدفوع في الفاتورة
- [ ] تحديث حالة الفاتورة
- [ ] تحديث الواجهة في جميع الشاشات
- [ ] تسجيل عملية التحصيل

## التوافق

هذه الميزة متوافقة مع:
- **نظام الفواتير الحالي**: لا يؤثر على الوظائف الموجودة
- **نظام التحصيل**: يستخدم نفس نظام التحصيل الموجود
- **قاعدة البيانات**: يستخدم نفس الجداول والعلاقات
- **جميع الشاشات**: يعمل في جميع شاشات عرض الفواتير

## ملاحظات مهمة

1. **الوقت المحدد**: 15 دقيقة هي الحد الأدنى قبل إمكانية التحصيل
2. **المبلغ المتبقي**: يتم حساب المبلغ المتبقي تلقائياً
3. **التحديث التلقائي**: جميع الشاشات تتحدث تلقائياً بعد التحصيل
4. **الملاحظات**: اختيارية ولكن مفيدة لتتبع عمليات التحصيل
5. **الأمان**: التحقق من صحة البيانات في جميع المراحل

## الدعم والمساعدة

في حالة مواجهة أي مشاكل أو أسئلة حول ميزة تحصيل الفواتير:
1. تأكد من أن الفاتورة مؤهلة للتحصيل
2. تحقق من صحة المبلغ المدخل
3. تأكد من وجود اتصال بالإنترنت
4. راجع سجلات التحصيل للتأكد من نجاح العملية
