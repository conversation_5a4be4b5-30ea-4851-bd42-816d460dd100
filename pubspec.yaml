name: atlas_medical_supplies
description: "Atlas Medical Supplies - نظام إدارة توزيع المستلزمات الطبية"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Icons - المستخدمة فعلياً
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  
  # State Management - المستخدمة فعلياً
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.2
  
  # Utilities - المستخدمة فعلياً
  intl: ^0.20.2
  shared_preferences: ^2.3.2
  path_provider: ^2.1.4
  
  # Local Database - المستخدمة فعلياً
  sqflite: ^2.4.0
  sqflite_common_ffi: ^2.3.6
  uuid: ^4.5.1
  
  # QR & Barcode - المستخدمة فعلياً
  qr_flutter: ^4.1.0
  
  # Charts - المستخدمة فعلياً
  fl_chart: ^0.69.0
  
  # Arabic Fonts & RTL
  flutter_localizations:
    sdk: flutter

  # Location - المستخدمة فعلياً
  geolocator: ^13.0.1
  
  # File Handling - المستخدمة فعلياً
  file_picker: ^8.1.2
  permission_handler: ^11.3.1
  
  # URL Launcher - المستخدمة فعلياً
  url_launcher: ^6.3.1
  
  # Share & Export - للمشاركة والتصدير
  share_plus: ^7.2.1
  
  # Animation - المستخدمة فعلياً
  lottie: ^3.1.2
  
  # Date & Time - المستخدمة فعلياً
  flutter_datetime_picker_plus: ^2.2.0
  
  # Responsive Design - المستخدمة فعلياً
  responsive_builder: ^0.7.1

  # Biometrics - المستخدمة فعلياً
  local_auth: ^2.3.0

  # PDF Generation & Printing
  pdf: ^3.11.1
  printing: ^5.13.2

  # تم إزالة المكتبات غير المستخدمة:
  # - http: ^1.2.2 (غير مستخدمة)
  # - dio: ^5.7.0 (غير مستخدمة)
  # - mobile_scanner: ^5.2.3 (غير مستخدمة)

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see
# the following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true
  # Disable deferred components to avoid Google Play Core dependency issues
  # deferred-components: []

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets - تم الاحتفاظ فقط بالأصول المستخدمة
  assets:
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Arabic Fonts - المستخدمة فعلياً في الكود
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
