import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../models/invoice_action_model.dart';
import '../../../models/invoice_model.dart';
import '../../../services/invoice_action_service.dart';
import '../../../services/invoice_service.dart';
import '../../../widgets/back_button.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_text_field.dart';

class RecordInvoiceActionScreen extends StatefulWidget {
  final InvoiceModel invoice;

  const RecordInvoiceActionScreen({super.key, required this.invoice});

  @override
  State<RecordInvoiceActionScreen> createState() =>
      _RecordInvoiceActionScreenState();
}

class _RecordInvoiceActionScreenState extends State<RecordInvoiceActionScreen> {
  final InvoiceActionService _actionService = InvoiceActionService();
  final _formKey = GlobalKey<FormState>();

  InvoiceActionType _selectedActionType = InvoiceActionType.payment;
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  String _errorMessage = '';
  String _successMessage = '';

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.invoice
        .calculateRemainingAmount()
        .toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _recordAction() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _successMessage = '';
    });

    try {
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim();

      final action = await _actionService.recordPayment(
        invoiceId: widget.invoice.id,
        amount: amount,
        notes: notes,
        createdBy: 'user', // يمكن تغييرها لاحقاً
      );

      setState(() {
        _successMessage = 'تم تسجيل ${action.actionTypeDisplayName} بنجاح';
        _isLoading = false;
      });

      // إعادة تعيين النموذج
      _formKey.currentState!.reset();
      _amountController.text = widget.invoice
          .calculateRemainingAmount()
          .toStringAsFixed(2);
      _notesController.clear();

      // العودة للشاشة السابقة بعد ثانيتين
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.of(context).pop(true); // true يعني تم تحديث البيانات
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'تسجيل دفع',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: const CustomBackButton(),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الفاتورة
              _buildInvoiceInfoCard(),

              SizedBox(height: 24.h),

              // نوع الإجراء
              _buildActionTypeSelector(),

              SizedBox(height: 24.h),

              // نموذج تسجيل الإجراء
              _buildActionForm(),

              SizedBox(height: 24.h),

              // رسائل الخطأ والنجاح
              if (_errorMessage.isNotEmpty) _buildErrorMessage(),
              if (_successMessage.isNotEmpty) _buildSuccessMessage(),

              SizedBox(height: 24.h),

              // زر التسجيل
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 16.h),

            _buildInfoRow('رقم الفاتورة:', widget.invoice.invoiceNumber),
            _buildInfoRow('العميل:', widget.invoice.customerName),
            _buildInfoRow(
              'إجمالي الفاتورة:',
              '${widget.invoice.total.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'المدفوع:',
              '${widget.invoice.paidAmount.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'المتبقي:',
              '${widget.invoice.calculateRemainingAmount().toStringAsFixed(2)} ر.س',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12.sp,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.grey[800],
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTypeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع الإجراء',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 16.h),

            Row(
              children: [
                Expanded(
                  child: _buildActionTypeOption(
                    InvoiceActionType.payment,
                    'دفع',
                    Icons.payment,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTypeOption(
    InvoiceActionType actionType,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedActionType == actionType;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedActionType = actionType;
        });
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[600],
              size: 24.sp,
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14.sp,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الإجراء',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
            SizedBox(height: 16.h),

            // حقل المبلغ
            CustomTextField(
              controller: _amountController,
              labelText: 'المبلغ',
              hintText: 'أدخل المبلغ',
              prefixIcon: Icons.attach_money,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'المبلغ مطلوب';
                }

                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'المبلغ يجب أن يكون رقم موجب';
                }

                final remainingAmount = widget.invoice
                    .calculateRemainingAmount();
                if (amount > remainingAmount) {
                  return 'المبلغ يتجاوز المتبقي (${remainingAmount.toStringAsFixed(2)} ر.س)';
                }

                return null;
              },
            ),

            SizedBox(height: 16.h),

            // حقل الملاحظات
            CustomTextField(
              controller: _notesController,
              labelText: 'ملاحظات (اختياري)',
              hintText: 'أدخل ملاحظات إضافية',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 20.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              _errorMessage,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 12.sp,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[300]!),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle_outline, color: Colors.green, size: 20.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              _successMessage,
              style: TextStyle(
                color: Colors.green[700],
                fontSize: 12.sp,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: _isLoading ? null : _recordAction,
        text: _isLoading
            ? 'جاري التسجيل...'
            : 'تسجيل ${_selectedActionType == InvoiceActionType.payment ? 'الدفع' : 'التحصيل'}',
        backgroundColor: _selectedActionType == InvoiceActionType.payment
            ? Colors.green
            : Colors.blue,
        textColor: Colors.white,
        height: 50.h,
      ),
    );
  }
}
