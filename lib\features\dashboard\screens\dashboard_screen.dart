import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../../../utils/screen_utils.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/quick_actions_grid.dart';
import '../../customers/screens/customers_screen.dart';

import '../../sales/screens/sales_screen.dart';

import '../../../widgets/app_drawer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardHome(),
    const CustomersScreen(),

    const SalesScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // إضافة خلفية بيضاء صريحة
      drawer: const AppDrawer(),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        backgroundColor: AppColors.surface,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.people), label: 'العملاء'),

          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'المبيعات',
          ),
        ],
      ),
    );
  }
}

class DashboardHome extends StatefulWidget {
  const DashboardHome({super.key});

  @override
  State<DashboardHome> createState() => _DashboardHomeState();
}

class _DashboardHomeState extends State<DashboardHome> {
  DateTime _lastUpdate = DateTime.now();

  @override
  void initState() {
    super.initState();
    // تم إلغاء التحديث التلقائي
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _refreshData() async {
    // تحديث البيانات من الخدمات
    // يمكن إضافة استدعاءات للخدمات هنا
    // مثال: await _productService.refreshProducts();
    // مثال: await _customerService.refreshCustomers();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ScreenUtils.isSmallScreen(context);

    return Column(
      children: [
        // AppBar مخصص
        Container(
          color: AppColors.primary,
          padding: EdgeInsets.only(
            top:
                MediaQuery.of(context).padding.top +
                (isSmallScreen ? 6.h : 8.h),
            bottom: isSmallScreen ? 6.h : 8.h,
            left: isSmallScreen ? 12.w : 16.w,
            right: isSmallScreen ? 12.w : 16.w,
          ),
          child: Row(
            children: [
              Builder(
                builder: (context) => Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(
                      isSmallScreen ? 6.r : 8.r,
                    ),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                    tooltip: 'القائمة الرئيسية',
                    iconSize: ScreenUtils.getResponsiveIconSize(
                      context,
                      smallSize: 20.sp,
                      mediumSize: 22.sp,
                      largeSize: 24.sp,
                    ),
                    padding: EdgeInsets.all(isSmallScreen ? 8.w : 12.w),
                    constraints: BoxConstraints(
                      minWidth: isSmallScreen ? 36.w : 48.w,
                      minHeight: isSmallScreen ? 36.h : 48.h,
                    ),
                  ),
                ),
              ),
              SizedBox(width: isSmallScreen ? 8.w : 12.w),
              Flexible(
                child: Text(
                  'لوحة التحكم',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: ScreenUtils.getResponsiveFontSize(
                      context,
                      smallSize: 16.sp,
                      mediumSize: 17.sp,
                      largeSize: 18.sp,
                    ),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Spacer(),
            ],
          ),
        ),

        // المحتوى
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              // تحديث البيانات
              await Future.delayed(const Duration(seconds: 1));
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: ScreenUtils.getResponsiveHorizontalPadding(context)
                  .copyWith(
                    top: isSmallScreen ? 12.h : 16.h,
                    bottom: isSmallScreen ? 12.h : 16.h,
                  ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الرأس مع المعلومات الشخصية
                  const DashboardHeader(),

                  SizedBox(height: isSmallScreen ? 20.h : 24.h),

                  // الإجراءات السريعة
                  Text(
                    'الإجراءات السريعة',
                    style: TextStyle(
                      fontSize: ScreenUtils.getResponsiveFontSize(
                        context,
                        smallSize: 16.sp,
                        mediumSize: 17.sp,
                        largeSize: 18.sp,
                      ),
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),

                  SizedBox(height: isSmallScreen ? 12.h : 16.h),

                  const QuickActionsGrid(),

                  SizedBox(height: isSmallScreen ? 20.h : 24.h),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
