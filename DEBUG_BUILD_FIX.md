# إصلاح مشكلة إنشاء نسخ منفصلة للتطبيق

## المشكلة
كان التطبيق يقوم بإنشاء نسخة منفصلة عند البناء في وضع debug بسبب الإعدادات التالية في `android/app/build.gradle.kts`:

```kotlin
debug {
    applicationIdSuffix = ".debug"        // يضيف .debug لاسم التطبيق
    versionNameSuffix = "-debug"          // يضيف -debug لرقم الإصدار
}
```

## الحل
تم إزالة هذه الإعدادات لضمان أن التطبيق يعمل على النسخة المثبتة على الهاتف دون إنشاء نسخة أخرى.

### التغييرات المطبقة:

1. **إزالة `applicationIdSuffix`**: 
   - كان: `applicationIdSuffix = ".debug"`
   - أصبح: `// applicationIdSuffix = ".debug"`

2. **إزالة `versionNameSuffix`**:
   - كان: `versionNameSuffix = "-debug"`
   - أصبح: `// versionNameSuffix = "-debug"`

## النتيجة
- ✅ التطبيق الآن يعمل على النسخة المثبتة على الهاتف
- ✅ لا يتم إنشاء نسخة منفصلة عند البناء في وضع debug
- ✅ يمكن تشغيل التطبيق مباشرة من Android Studio أو Flutter CLI
- ✅ يحتفظ التطبيق بنفس البيانات والإعدادات

## كيفية الاستخدام
1. قم ببناء التطبيق في وضع debug:
   ```bash
   flutter build apk --debug
   ```
   
2. أو قم بتشغيله مباشرة:
   ```bash
   flutter run --debug
   ```

3. التطبيق سيعمل على النسخة المثبتة على الهاتف دون إنشاء نسخة جديدة.

## ملاحظات مهمة
- تأكد من إزالة النسخة القديمة من الهاتف قبل البناء الجديد
- إذا كنت تريد نسخة منفصلة للتطوير، يمكنك إعادة تفعيل هذه الإعدادات
- هذا التغيير يؤثر فقط على وضع debug، ولا يؤثر على وضع release
