# دليل حسابات الفواتير والمدفوعات - Atlas Medical Supplies

## نظرة عامة
هذا الدليل يوضح كيفية عمل نظام حسابات الفواتير والمدفوعات في التطبيق.

## الحسابات الأساسية

### 1. حساب إجمالي الفاتورة
```
المجموع الفرعي = مجموع (سعر الوحدة × الكمية) لكل منتج
قيمة الخصم = المجموع الفرعي × نسبة الخصم / 100 (أو مبلغ ثابت)
الإجمالي النهائي = المجموع الفرعي - قيمة الخصم
```

### 2. حساب المبلغ المتبقي
```
المبلغ المتبقي = الإجمالي النهائي - المبلغ المدفوع
```

### 3. حساب نسبة الدفع
```
نسبة الدفع = (المبلغ المدفوع / الإجمالي النهائي) × 100
```

## أمثلة عملية

### مثال 1: فاتورة بدون خصم
- المنتج أ: 100 ج.م × 2 قطعة = 200 ج.م
- المنتج ب: 50 ج.م × 1 قطعة = 50 ج.م
- **المجموع الفرعي**: 250 ج.م
- **قيمة الخصم**: 0 ج.م
- **الإجمالي النهائي**: 250 ج.م
- **المبلغ المدفوع**: 100 ج.م
- **المبلغ المتبقي**: 150 ج.م
- **نسبة الدفع**: 40%

### مثال 2: فاتورة مع خصم
- المنتج أ: 100 ج.م × 2 قطعة = 200 ج.م
- المنتج ب: 50 ج.م × 1 قطعة = 50 ج.م
- **المجموع الفرعي**: 250 ج.م
- **قيمة الخصم**: 25 ج.م (10%)
- **الإجمالي النهائي**: 225 ج.م
- **المبلغ المدفوع**: 225 ج.م
- **المبلغ المتبقي**: 0 ج.م
- **نسبة الدفع**: 100%

## حالات الدفع

### 1. دفع كامل
- **الشرط**: المبلغ المدفوع >= الإجمالي النهائي
- **الحالة**: "تم الدفع بالكامل"
- **اللون**: أخضر

### 2. دفع جزئي
- **الشرط**: 0 < المبلغ المدفوع < الإجمالي النهائي
- **الحالة**: "دفع جزئي"
- **اللون**: برتقالي

### 3. لم يتم الدفع
- **الشرط**: المبلغ المدفوع = 0
- **الحالة**: "لم يتم الدفع"
- **اللون**: رمادي

## التحسينات المضافة

### 1. عرض محسن للمبالغ
- إضافة قسم منفصل "معلومات الدفع"
- عرض المبلغ المدفوع والمتبقي ونسبة الدفع
- استخدام ألوان مميزة لكل حالة

### 2. وضوح الحسابات
- عرض جميع المكونات بوضوح
- إظهار خطوات الحساب
- تمييز الألوان للمبالغ المختلفة

### 3. تحسينات الواجهة
- إضافة أيقونات مميزة
- استخدام ألوان متسقة
- تحسين التخطيط والمسافات

## ملاحظات مهمة

1. **المبلغ المدفوع لا يمكن أن يتجاوز الإجمالي النهائي**
2. **المبلغ المتبقي لا يمكن أن يكون سالباً**
3. **نسبة الدفع تتراوح من 0% إلى 100%**
4. **حالة الفاتورة تتحدد تلقائياً بناءً على المبلغ المدفوع**

## الكود المستخدم

### حساب المبلغ المتبقي
```dart
double calculateRemainingAmount() {
  return total - paidAmount;
}
```

### حساب نسبة الدفع
```dart
double calculatePaymentPercentage() {
  if (total > 0) {
    return (paidAmount / total) * 100;
  }
  return 0.0;
}
```

### التحقق من اكتمال الدفع
```dart
bool get isFullyPaid => paidAmount >= total;
```

## استنتاج

نظام الحسابات في التطبيق يعمل بشكل صحيح ويخصم المبلغ المدفوع من إجمالي الفاتورة كما هو مطلوب. التحسينات المضافة تجعل الحسابات أكثر وضوحاً للمستخدم.
