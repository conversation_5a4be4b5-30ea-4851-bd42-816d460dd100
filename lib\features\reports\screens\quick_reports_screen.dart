import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_colors.dart';
import '../../../services/collection_service.dart';
import '../../../widgets/back_button.dart';

class QuickReportsScreen extends StatefulWidget {
  const QuickReportsScreen({super.key});

  @override
  State<QuickReportsScreen> createState() => _QuickReportsScreenState();
}

class _QuickReportsScreenState extends State<QuickReportsScreen> {
  final CollectionService _collectionService = CollectionService();
  double _totalPendingCollections = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingCollections();
  }

  // إضافة دالة جديدة لحساب مبالغ التحصيل المستحقة
  Future<void> _loadPendingCollections() async {
    try {
      final pendingInvoices = await _collectionService.getPendingInvoices();
      double totalAmount = 0.0;

      for (final invoice in pendingInvoices) {
        totalAmount += invoice.calculateRemainingAmount();
      }

      if (mounted) {
        setState(() {
          _totalPendingCollections = totalAmount;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 1,
        title: const Text(
          'التقارير السريعة',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        centerTitle: true,
        leading: CustomBackButton(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _loadPendingCollections,
            icon: Icon(Icons.refresh, color: AppColors.primary, size: 24.sp),
            tooltip: 'تحديث',
          ),
        ],
      ),
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await _loadPendingCollections();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التقارير السريعة',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 20.h),

                // بطاقة مبالغ التحصيل المستحقة
                if (!_isLoading && _totalPendingCollections > 0)
                  Container(
                    margin: EdgeInsets.only(bottom: 16.h),
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.orange.shade50, Colors.orange.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.orange.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.shade200.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8.w),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade200,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Icon(
                            Icons.payment,
                            color: Colors.orange.shade800,
                            size: 20.sp,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مبالغ التحصيل المستحقة',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade800,
                                ),
                              ),
                              Text(
                                'تحتاج إلى متابعة',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 12.sp,
                                  color: Colors.orange.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 6.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade200,
                            borderRadius: BorderRadius.circular(16.r),
                            border: Border.all(color: Colors.orange.shade300),
                          ),
                          child: Text(
                            NumberFormat.currency(
                              locale: 'ar_SA',
                              symbol: 'ر.س ',
                              decimalDigits: 0,
                            ).format(_totalPendingCollections),
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // تقرير المبيعات اليومية
                _buildReportCard(
                  title: 'تقرير المبيعات اليومية',
                  subtitle: 'عرض مبيعات اليوم',
                  icon: Icons.today,
                  color: AppColors.primary,
                  onTap: () => _showReport(context, 'المبيعات اليومية'),
                ),

                SizedBox(height: 12.h),

                // تقرير العملاء
                _buildReportCard(
                  title: 'تقرير العملاء',
                  subtitle: 'قائمة العملاء النشطين',
                  icon: Icons.people,
                  color: AppColors.customers,
                  onTap: () => _showReport(context, 'العملاء'),
                ),

                SizedBox(height: 12.h),

                // تقرير المخزون
                _buildReportCard(
                  title: 'تقرير المخزون',
                  subtitle: 'حالة المخزون الحالية',
                  icon: Icons.inventory,
                  color: AppColors.products,
                  onTap: () => _showReport(context, 'المخزون'),
                ),

                SizedBox(height: 12.h),

                // تقرير المرتجعات
                _buildReportCard(
                  title: 'تقرير المرتجعات',
                  subtitle: 'المرتجعات الشهرية',
                  icon: Icons.keyboard_return,
                  color: AppColors.returns,
                  onTap: () => _showReport(context, 'المرتجعات'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(icon, color: color, size: 24.sp),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textSecondary,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReport(BuildContext context, String reportType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تقرير $reportType', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'سيتم تطوير هذا التقرير قريباً',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );
  }
}
