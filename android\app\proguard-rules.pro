# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep R classes
-keep class **.R$* {
    public static <fields>;
}

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep annotation classes
-keep @interface * {
    *;
}

# Keep classes with annotations
-keep @* class * {
    *;
}

# Keep classes in specific packages
-keep class com.example.atlas2.** { *; }

# Keep SQLite classes
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Keep SharedPreferences
-keep class android.content.SharedPreferences { *; }

# Keep Security classes
-keep class androidx.security.** { *; }
-keep class java.security.** { *; }
-keep class javax.crypto.** { *; }

# Keep Material Design classes
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# Keep ConstraintLayout classes
-keep class androidx.constraintlayout.** { *; }
-dontwarn androidx.constraintlayout.**

# Keep Multidex classes
-keep class androidx.multidex.** { *; }

# Remove debug logs in release
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
}

# Optimize string operations
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# Remove unused code
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Keep important classes
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep public class * extends android.app.Fragment

# Keep JavaScript interface methods
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep custom views
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Keep onClick methods
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# Keep Room database classes
-keep class * extends androidx.room.RoomDatabase {
    public static <fields>;
}

# Keep Room entities
-keep @androidx.room.Entity class * {
    *;
}

# Keep Room DAOs
-keep @androidx.room.Dao interface * {
    *;
}

# Keep Room TypeConverters
-keep @androidx.room.TypeConverter class * {
    *;
}

# Keep path_provider classes
-keep class io.flutter.plugins.pathprovider.** { *; }

# Keep shared_preferences classes
-keep class io.flutter.plugins.sharedpreferences.** { *; }

# Keep sqflite classes
-keep class io.flutter.plugins.sqflite.** { *; }

# Keep flutter_screenutil classes
-keep class io.flutter.plugins.flutter_screenutil.** { *; }

# Keep flutter_svg classes
-keep class io.flutter.plugins.flutter_svg.** { *; }

# Keep fl_chart classes
-keep class com.github.PhilJay.** { *; }

# Keep geolocator classes
-keep class com.baseflow.geolocator.** { *; }

# Keep file_picker classes
-keep class com.mr.flutter.plugin.filepicker.** { *; }

# Keep permission_handler classes
-keep class com.baseflow.permissionhandler.** { *; }

# Keep url_launcher classes
-keep class io.flutter.plugins.urllauncher.** { *; }

# Keep lottie classes
-keep class com.airbnb.lottie.** { *; }

# Keep flutter_datetime_picker_plus classes
-keep class com.jaumard.flutter_datetime_picker_plus.** { *; }

# Keep responsive_builder classes
-keep class com.felixblaschke.responsive_builder.** { *; }

# Keep local_auth classes
-keep class io.flutter.plugins.localauth.** { *; }

# Keep provider classes
-keep class provider.** { *; }

# Keep riverpod classes
-keep class riverpod.** { *; }
-keep class flutter_riverpod.** { *; }

# Keep intl classes
-keep class intl.** { *; }

# Keep uuid classes
-keep class com.example.uuid.** { *; }

# Exclude unnecessary files
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**

# Google Play Core - suppress warnings for missing classes
-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.SplitInstallException
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManager
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManagerFactory
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest$Builder
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest
-dontwarn com.google.android.play.core.splitinstall.SplitInstallSessionState
-dontwarn com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task
