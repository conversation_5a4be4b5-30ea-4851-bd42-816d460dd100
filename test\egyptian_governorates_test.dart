import 'package:flutter_test/flutter_test.dart';
import '../lib/constants/egyptian_governorates.dart';

void main() {
  group('Egyptian Governorates Tests', () {
    test('should have correct number of governorates', () {
      final governorates = EgyptianGovernorates.getAllGovernorates();
      expect(governorates.length, greaterThan(0));
      expect(governorates.contains('القاهرة'), isTrue);
      expect(governorates.contains('الإسكندرية'), isTrue);
      expect(governorates.contains('الجيزة'), isTrue);
    });

    test('should have correct cities for Alexandria', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate('الإسكندرية');
      expect(cities.length, greaterThan(0));
      expect(cities.contains('الأزاريطة'), isTrue);
      expect(cities.contains('سيدي جابر'), isTrue);
      expect(cities.contains('سموحة'), isTrue);

      // التحقق من عدم وجود تكرار
      final uniqueCities = cities.toSet();
      expect(
        uniqueCities.length,
        equals(cities.length),
        reason: 'يجب ألا يكون هناك تكرار في المدن',
      );
    });

    test('should have correct cities for Cairo', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate('القاهرة');
      expect(cities.length, greaterThan(0));
      expect(cities.contains('المعادي'), isTrue);
      expect(cities.contains('مدينة نصر'), isTrue);
      expect(cities.contains('مصر الجديدة'), isTrue);

      // التحقق من عدم وجود تكرار
      final uniqueCities = cities.toSet();
      expect(
        uniqueCities.length,
        equals(cities.length),
        reason: 'يجب ألا يكون هناك تكرار في المدن',
      );
    });

    test('should validate governorate existence', () {
      expect(EgyptianGovernorates.hasGovernorate('القاهرة'), isTrue);
      expect(EgyptianGovernorates.hasGovernorate('الإسكندرية'), isTrue);
      expect(EgyptianGovernorates.hasGovernorate('الجيزة'), isTrue);
      expect(EgyptianGovernorates.hasGovernorate('محافظة غير موجودة'), isFalse);
    });

    test('should validate city in governorate', () {
      expect(
        EgyptianGovernorates.hasCityInGovernorate('الإسكندرية', 'الأزاريطة'),
        isTrue,
      );
      expect(
        EgyptianGovernorates.hasCityInGovernorate('الإسكندرية', 'سيدي جابر'),
        isTrue,
      );
      expect(
        EgyptianGovernorates.hasCityInGovernorate('القاهرة', 'المعادي'),
        isTrue,
      );
      expect(
        EgyptianGovernorates.hasCityInGovernorate(
          'الإسكندرية',
          'مدينة غير موجودة',
        ),
        isFalse,
      );
    });

    test('should not have duplicate cities in Alexandria', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate('الإسكندرية');
      final cityCounts = <String, int>{};

      for (final city in cities) {
        cityCounts[city] = (cityCounts[city] ?? 0) + 1;
      }

      // التحقق من أن كل مدينة تظهر مرة واحدة فقط
      for (final entry in cityCounts.entries) {
        expect(
          entry.value,
          equals(1),
          reason:
              'المدينة ${entry.key} تظهر ${entry.value} مرات بدلاً من مرة واحدة',
        );
      }

      // التحقق من أن الأزاريطة تظهر مرة واحدة فقط
      expect(
        cityCounts['الأزاريطة'],
        equals(1),
        reason: 'الأزاريطة يجب أن تظهر مرة واحدة فقط',
      );
    });

    test('should not have duplicate cities in any governorate', () {
      final governorates = EgyptianGovernorates.getAllGovernorates();

      for (final governorate in governorates) {
        final cities = EgyptianGovernorates.getCitiesForGovernorate(
          governorate,
        );
        final uniqueCities = cities.toSet();

        expect(
          uniqueCities.length,
          equals(cities.length),
          reason: 'محافظة $governorate تحتوي على مدن مكررة',
        );
      }
    });

    test('should have reasonable number of cities per governorate', () {
      final governorates = EgyptianGovernorates.getAllGovernorates();

      for (final governorate in governorates) {
        final cities = EgyptianGovernorates.getCitiesForGovernorate(
          governorate,
        );

        // التحقق من أن كل محافظة تحتوي على عدد معقول من المدن
        expect(
          cities.length,
          greaterThan(0),
          reason: 'محافظة $governorate لا تحتوي على مدن',
        );
        expect(
          cities.length,
          lessThan(100),
          reason:
              'محافظة $governorate تحتوي على عدد كبير جداً من المدن: ${cities.length}',
        );
      }
    });

    test('should handle empty and null values correctly', () {
      expect(EgyptianGovernorates.hasGovernorate(''), isFalse);
      expect(EgyptianGovernorates.hasCityInGovernorate('', ''), isFalse);
      expect(EgyptianGovernorates.hasCityInGovernorate('القاهرة', ''), isFalse);
    });

    test('should return empty list for non-existent governorate', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate(
        'محافظة غير موجودة',
      );
      expect(cities, isEmpty);
    });

    test('should have correct total counts', () {
      final totalGovernorates =
          EgyptianGovernorates.getTotalGovernoratesCount();
      final totalCities = EgyptianGovernorates.getTotalCitiesCount();

      expect(totalGovernorates, greaterThan(0));
      expect(totalCities, greaterThan(0));
      expect(totalCities, greaterThan(totalGovernorates));
    });

    test('should search cities correctly', () {
      final results = EgyptianGovernorates.searchCitiesByName('الأزاريطة');
      expect(results.length, equals(1));
      expect(results.first.value, equals('الأزاريطة'));
      expect(results.first.key, equals('الإسكندرية'));
    });

    test('should get governorates by city count', () {
      final governorateCounts =
          EgyptianGovernorates.getGovernoratesByCityCount();
      expect(governorateCounts.length, greaterThan(0));

      // التحقق من أن القائمة مرتبة تنازلياً
      for (int i = 0; i < governorateCounts.length - 1; i++) {
        expect(
          governorateCounts[i].value,
          greaterThanOrEqualTo(governorateCounts[i + 1].value),
        );
      }
    });
  });

  group('Specific Governorate Tests', () {
    test('Alexandria should have specific cities', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate('الإسكندرية');
      final expectedCities = [
        'سيدي جابر',
        'سموحة',
        'العجمي',
        'المنتزه',
        'محطة الرمل',
        'برج العرب',
        'المعمورة',
        'أبو قير',
        'باكوس',
        'سيدي بشر',
        'العطارين',
        'المنشية',
        'محرم بك',
        'الإبراهيمية',
        'جناكليس',
        'كامب شيزار',
        'ستانلي',
        'سابا باشا',
        'أجيون',
        'ميامي',
        'المندرة',
        'سيدي كرير',
        'برج العرب الجديدة',
        'العلمين',
        'مرسى مطروح',
        'الأزاريطة',
      ];

      for (final expectedCity in expectedCities) {
        expect(
          cities.contains(expectedCity),
          isTrue,
          reason: 'المدينة $expectedCity غير موجودة في الإسكندرية',
        );
      }
    });

    test('Cairo should have specific cities', () {
      final cities = EgyptianGovernorates.getCitiesForGovernorate('القاهرة');
      final expectedCities = [
        'المعادي',
        'حلوان',
        'مدينة نصر',
        'مصر الجديدة',
        'شبرا',
        'السيدة زينب',
        'التجمع الخامس',
        'الزمالك',
        'وسط البلد',
      ];

      for (final expectedCity in expectedCities) {
        expect(
          cities.contains(expectedCity),
          isTrue,
          reason: 'المدينة $expectedCity غير موجودة في القاهرة',
        );
      }
    });
  });
}
