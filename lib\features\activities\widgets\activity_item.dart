import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../constants/app_colors.dart';
import '../models/activity_model.dart';

class ActivityItem extends StatelessWidget {
  final Activity activity;
  final VoidCallback? onTap;

  const ActivityItem({super.key, required this.activity, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // الأيقونة
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: activity.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    activity.icon,
                    color: activity.color,
                    size: 24.sp,
                  ),
                ),

                SizedBox(width: 16.w),

                // المحتوى
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                          fontFamily: 'Cairo',
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: 4.h),

                      Text(
                        activity.subtitle,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                          fontFamily: 'Cairo',
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: 8.h),

                      Text(
                        activity.timeAgo,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textHint,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(width: 12.w),

                // المبلغ والسهم
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (activity.amount.isNotEmpty)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getAmountBackgroundColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                        child: Text(
                          activity.amount,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: _getAmountTextColor(),
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ),

                    SizedBox(height: 8.h),

                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16.sp,
                      color: AppColors.textHint,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getAmountBackgroundColor() {
    switch (activity.type) {
      case ActivityType.returns:
        return AppColors.error;
      default:
        return activity.color;
    }
  }

  Color _getAmountTextColor() {
    switch (activity.type) {
      case ActivityType.returns:
        return AppColors.error;
      default:
        return activity.color;
    }
  }
}
