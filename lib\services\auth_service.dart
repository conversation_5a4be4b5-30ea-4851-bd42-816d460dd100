import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import 'database_service.dart';

class AuthService extends ChangeNotifier {
  UserModel? _currentUser;
  UserModel? get currentUser => _currentUser;

  final _authStateController = StreamController<UserModel?>.broadcast();
  Stream<UserModel?> get authStateChanges => _authStateController.stream;

  final DatabaseService _databaseService = DatabaseService();

  Future<UserModel?> signInWithPhoneAndPassword({
    required String phone,
    required String password,
    bool keepSignedIn = false,
    bool enableAutoLogin = true,
  }) async {
    try {
      final user = await _databaseService.getUserByPhone(phone);
      if (user == null) throw 'لا يوجد مستخدم بهذا الرقم';
      final ok = await _databaseService.verifyUserPassword(phone, password);
      if (!ok) throw 'الرقم السري غير صحيح';
      if (!user.isActive) throw 'تم تعطيل هذا الحساب';

      await _databaseService.updateUserLastLogin(user.id);
      _currentUser = user;
      _authStateController.add(user);
      notifyListeners();

      // حفظ بيانات الجلسة
      await _persistSession(
        phone: phone,
        password: password,
        remember: keepSignedIn,
        autoLogin: enableAutoLogin,
      );
      return user;
    } catch (e) {
      if (e is String) rethrow;
      throw 'حدث خطأ في تسجيل الدخول';
    }
  }

  // إدارة OTP المبسطة محلياً (نسخة تجريبية بدون مزود رسائل)
  String? _pendingOtpPhone;
  String? _pendingOtpCode; // يخزن رمزاً مؤقتاً
  DateTime? _otpExpiresAt;

  Future<void> sendPasswordResetOtp(String phone) async {
    // تحقق من وجود المستخدم
    final user = await _databaseService.getUserByPhone(phone);
    if (user == null) throw 'لا يوجد مستخدم بهذا الرقم';

    // إنشاء رمز OTP (6 أرقام)
    final now = DateTime.now();
    final code = (now.millisecondsSinceEpoch % 1000000).toString().padLeft(
      6,
      '0',
    );

    _pendingOtpPhone = phone;
    _pendingOtpCode = code;
    _otpExpiresAt = now.add(const Duration(minutes: 5));

    if (kDebugMode) {
      debugPrint('OTP for $phone is $code (valid 5 min)');
    }

    // ملاحظة: لبيئة الإنتاج، اربط مزود SMS هنا لإرسال الكود فعلياً
  }

  Future<void> verifyPasswordResetOtp({
    required String phone,
    required String code,
  }) async {
    if (_pendingOtpPhone != phone)
      throw 'انتهت صلاحية الطلب. أرسل الكود مرة أخرى.';
    if (_otpExpiresAt == null || DateTime.now().isAfter(_otpExpiresAt!)) {
      _clearOtpState();
      throw 'انتهت صلاحية الكود، يرجى المحاولة مرة أخرى.';
    }
    if (_pendingOtpCode != code) throw 'كود غير صحيح';
  }

  Future<void> resetPasswordWithOtp({
    required String phone,
    required String code,
    required String newPassword,
  }) async {
    await verifyPasswordResetOtp(phone: phone, code: code);
    final updated = await _databaseService.updateUserPasswordByPhone(
      phone,
      newPassword,
    );
    if (updated == 0) throw 'تعذر تحديث كلمة المرور';
    _clearOtpState();
  }

  void _clearOtpState() {
    _pendingOtpPhone = null;
    _pendingOtpCode = null;
    _otpExpiresAt = null;
  }



  // Google / Microsoft SSO: أماكن جاهزة للربط لاحقًا
  Future<UserModel?> signInWithGoogle({bool keepSignedIn = true}) async {
    throw 'تسجيل الدخول عبر Google غير مفعّل حالياً في هذه النسخة';
  }

  Future<UserModel?> signInWithMicrosoft({bool keepSignedIn = true}) async {
    throw 'تسجيل الدخول عبر Microsoft غير مفعّل حالياً في هذه النسخة';
  }

  Future<void> signOut() async {
    try {
      await _clearSavedCredentials();
      _currentUser = null;
      _authStateController.add(null);
      notifyListeners();
    } catch (e) {
      throw 'حدث خطأ في تسجيل الخروج';
    }
  }

  Future<void> _persistSession({
    required String phone,
    required String password,
    required bool remember,
    required bool autoLogin,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (remember || autoLogin) {
        // حفظ العلامات في SharedPreferences
        await prefs.setBool('remember_me', remember);
        await prefs.setBool('auto_login', autoLogin);
        // وتخزين بيانات الاعتماد في قاعدة البيانات
        await _databaseService.saveCredentials(
          phone: phone,
          password: password,
        );
      } else {
        await _clearSavedCredentials();
      }
    } catch (e) {
      debugPrint('Error persisting session: $e');
    }
  }

  Future<void> _clearSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('remember_me');
      await prefs.remove('auto_login');
      await _databaseService.clearSavedCredentials();
    } catch (e) {
      debugPrint('Error clearing saved credentials: $e');
    }
  }

  bool get isSignedIn => _currentUser != null;
  String? get currentUserId => _currentUser?.id;
  String? get currentUserName => _currentUser?.name;
  String? get currentUserRole => _currentUser?.role;
  bool hasPermission(String permission) =>
      _currentUser?.hasPermission(permission) ?? false;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isAccountant => _currentUser?.isAccountant ?? false;
  bool get isSalesRep => _currentUser?.isSalesRep ?? false;
  bool get isSupervisor => _currentUser?.isSupervisor ?? false;

  Future<UserModel?> getCurrentUserData() async => _currentUser;

  Future<bool> hasSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool('remember_me') ?? false;
      final autoLogin = prefs.getBool('auto_login') ?? false;
      if (!rememberMe && !autoLogin) return false;
      final creds = await _databaseService.getSavedCredentials();
      return creds != null;
    } catch (e) {
      return false;
    }
  }

  Future<UserModel?> autoSignIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool('remember_me') ?? false;
      final autoLogin = prefs.getBool('auto_login') ?? false;

      if (rememberMe || autoLogin) {
        final creds = await _databaseService.getSavedCredentials();
        if (creds == null) return null;
        return await signInWithPhoneAndPassword(
          phone: creds.phone,
          password: creds.password,
          keepSignedIn: true,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> enableAutoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_login', true);
    } catch (e) {
      debugPrint('Error enabling auto login: $e');
    }
  }

  Future<void> disableAutoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_login', false);
    } catch (e) {
      debugPrint('Error disabling auto login: $e');
    }
  }

  Future<bool> isAutoLoginEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('auto_login') ?? false;
    } catch (e) {
      return false;
    }
  }

  @override
  void dispose() {
    _authStateController.close();
    super.dispose();
  }
}
