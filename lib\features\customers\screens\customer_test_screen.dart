import 'package:flutter/material.dart';
import '../../../test/simple_customer_test.dart';
import '../../../constants/app_colors.dart';

/// شاشة اختبار قاعدة بيانات العملاء
class CustomerTestScreen extends StatefulWidget {
  const CustomerTestScreen({super.key});

  @override
  State<CustomerTestScreen> createState() => _CustomerTestScreenState();
}

class _CustomerTestScreenState extends State<CustomerTestScreen> {
  final List<String> _logMessages = [];
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _addLogMessage('شاشة اختبار قاعدة بيانات العملاء جاهزة');
  }

  void _addLogMessage(String message) {
    setState(() {
      _logMessages.add('${DateTime.now().toString().substring(11, 19)}: $message');
      if (_logMessages.length > 50) {
        _logMessages.removeAt(0);
      }
    });
  }

  Future<void> _runSimpleTest() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _logMessages.clear();
    });

    _addLogMessage('بدء الاختبار البسيط...');

    try {
      await SimpleCustomerTest.runSimpleTest();
      _addLogMessage('✅ الاختبار اكتمل بنجاح!');
    } catch (e) {
      _addLogMessage('❌ فشل في الاختبار: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _runQuickTest() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _logMessages.clear();
    });

    _addLogMessage('بدء الاختبار السريع...');

    try {
      await SimpleCustomerTest.quickTest();
      _addLogMessage('✅ الاختبار السريع اكتمل!');
    } catch (e) {
      _addLogMessage('❌ فشل في الاختبار السريع: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  void _clearLogs() {
    setState(() {
      _logMessages.clear();
    });
    _addLogMessage('تم مسح السجلات');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة بيانات العملاء'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearLogs,
            tooltip: 'مسح السجلات',
          ),
        ],
      ),
      body: Column(
        children: [
          // أزرار الاختبار
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRunning ? null : _runSimpleTest,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('اختبار شامل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRunning ? null : _runQuickTest,
                    icon: const Icon(Icons.flash_on),
                    label: const Text('اختبار سريع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // مؤشر التحميل
          if (_isRunning)
            Container(
              padding: const EdgeInsets.all(16),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('جاري تشغيل الاختبار...'),
                ],
              ),
            ),
          
          // السجلات
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.list_alt, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'سجلات الاختبار (${_logMessages.length})',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _logMessages.isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد سجلات',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                              ),
                            ),
                          )
                        : ListView.builder(
                            itemCount: _logMessages.length,
                            itemBuilder: (context, index) {
                              final message = _logMessages[index];
                              final isError = message.contains('❌');
                              final isSuccess = message.contains('✅');
                              final isWarning = message.contains('⚠️');
                              
                              Color textColor = Colors.black87;
                              if (isError) textColor = Colors.red[700]!;
                              if (isSuccess) textColor = Colors.green[700]!;
                              if (isWarning) textColor = Colors.orange[700]!;
                              
                              return Container(
                                margin: const EdgeInsets.only(bottom: 4),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: isError
                                      ? Colors.red[50]
                                      : isSuccess
                                          ? Colors.green[50]
                                          : isWarning
                                              ? Colors.orange[50]
                                              : Colors.white,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  message,
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 12,
                                    fontFamily: 'Cairo',
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
