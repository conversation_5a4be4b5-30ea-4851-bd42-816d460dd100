import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  static const String _settingsKey = 'app_settings';

  // إعدادات عامة
  bool _isDarkMode = false;
  String _language = 'ar';
  String _currency = 'SAR';
  String _dateFormat = 'dd/MM/yyyy';
  String _timeFormat = '24';

  // إعدادات الإشعارات
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _lowStockAlerts = true;
  bool _expiryAlerts = true;

  // إعدادات الأمان
  bool _biometricAuth = false;
  bool _pinAuth = false;
  String _pinCode = '';
  bool _autoLock = false;
  int _autoLockTimeout = 5;

  // إعدادات النسخ الاحتياطي
  bool _autoBackup = false;
  String _backupFrequency = 'weekly';
  bool _backupToCloud = false;

  // إعدادات الطباعة
  String _printerType = 'thermal';
  bool _autoPrint = false;
  String _paperSize = '80mm';

  // إعدادات التقارير
  bool _autoGenerateReports = false;
  String _reportFormat = 'pdf';
  bool _emailReports = false;

  // Getters
  bool get isDarkMode => _isDarkMode;
  String get language => _language;
  String get currency => _currency;
  String get dateFormat => _dateFormat;
  String get timeFormat => _timeFormat;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get lowStockAlerts => _lowStockAlerts;
  bool get expiryAlerts => _expiryAlerts;
  bool get biometricAuth => _biometricAuth;
  bool get pinAuth => _pinAuth;
  String get pinCode => _pinCode;
  bool get autoLock => _autoLock;
  int get autoLockTimeout => _autoLockTimeout;
  bool get autoBackup => _autoBackup;
  String get backupFrequency => _backupFrequency;
  bool get backupToCloud => _backupToCloud;
  String get printerType => _printerType;
  bool get autoPrint => _autoPrint;
  String get paperSize => _paperSize;
  bool get autoGenerateReports => _autoGenerateReports;
  String get reportFormat => _reportFormat;
  bool get emailReports => _emailReports;

  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      if (settingsJson != null) {
        final settings = json.decode(settingsJson) as Map<String, dynamic>;

        _isDarkMode = settings['isDarkMode'] ?? false;
        _language = settings['language'] ?? 'ar';
        _currency = settings['currency'] ?? 'SAR';
        _dateFormat = settings['dateFormat'] ?? 'dd/MM/yyyy';
        _timeFormat = settings['timeFormat'] ?? '24';
        _notificationsEnabled = settings['notificationsEnabled'] ?? true;
        _soundEnabled = settings['soundEnabled'] ?? true;
        _vibrationEnabled = settings['vibrationEnabled'] ?? true;
        _lowStockAlerts = settings['lowStockAlerts'] ?? true;
        _expiryAlerts = settings['expiryAlerts'] ?? true;
        _biometricAuth = settings['biometricAuth'] ?? false;
        _pinAuth = settings['pinAuth'] ?? false;
        _pinCode = settings['pinCode'] ?? '';
        _autoLock = settings['autoLock'] ?? false;
        _autoLockTimeout = settings['autoLockTimeout'] ?? 5;
        _autoBackup = settings['autoBackup'] ?? false;
        _backupFrequency = settings['backupFrequency'] ?? 'weekly';
        _backupToCloud = settings['backupToCloud'] ?? false;
        _printerType = settings['printerType'] ?? 'thermal';
        _autoPrint = settings['autoPrint'] ?? false;
        _paperSize = settings['paperSize'] ?? '80mm';
        _autoGenerateReports = settings['autoGenerateReports'] ?? false;
        _reportFormat = settings['reportFormat'] ?? 'pdf';
        _emailReports = settings['emailReports'] ?? false;
      }
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settings = {
        'isDarkMode': _isDarkMode,
        'language': _language,
        'currency': _currency,
        'dateFormat': _dateFormat,
        'timeFormat': _timeFormat,
        'notificationsEnabled': _notificationsEnabled,
        'soundEnabled': _soundEnabled,
        'vibrationEnabled': _vibrationEnabled,
        'lowStockAlerts': _lowStockAlerts,
        'expiryAlerts': _expiryAlerts,
        'biometricAuth': _biometricAuth,
        'pinAuth': _pinAuth,
        'pinCode': _pinCode,
        'autoLock': _autoLock,
        'autoLockTimeout': _autoLockTimeout,
        'autoBackup': _autoBackup,
        'backupFrequency': _backupFrequency,
        'backupToCloud': _backupToCloud,
        'printerType': _printerType,
        'autoPrint': _autoPrint,
        'paperSize': _paperSize,
        'autoGenerateReports': _autoGenerateReports,
        'reportFormat': _reportFormat,
        'emailReports': _emailReports,
      };

      await prefs.setString(_settingsKey, json.encode(settings));
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  Future<void> updateSetting(String key, dynamic value) async {
    switch (key) {
      case 'isDarkMode':
        _isDarkMode = value;
        break;
      case 'language':
        _language = value;
        break;
      case 'currency':
        _currency = value;
        break;
      case 'dateFormat':
        _dateFormat = value;
        break;
      case 'timeFormat':
        _timeFormat = value;
        break;
      case 'notificationsEnabled':
        _notificationsEnabled = value;
        break;
      case 'soundEnabled':
        _soundEnabled = value;
        break;
      case 'vibrationEnabled':
        _vibrationEnabled = value;
        break;
      case 'lowStockAlerts':
        _lowStockAlerts = value;
        break;
      case 'expiryAlerts':
        _expiryAlerts = value;
        break;
      case 'biometricAuth':
        _biometricAuth = value;
        break;
      case 'pinAuth':
        _pinAuth = value;
        break;
      case 'pinCode':
        _pinCode = value;
        break;
      case 'autoLock':
        _autoLock = value;
        break;
      case 'autoLockTimeout':
        _autoLockTimeout = value;
        break;
      case 'autoBackup':
        _autoBackup = value;
        break;
      case 'backupFrequency':
        _backupFrequency = value;
        break;
      case 'backupToCloud':
        _backupToCloud = value;
        break;
      case 'printerType':
        _printerType = value;
        break;
      case 'autoPrint':
        _autoPrint = value;
        break;
      case 'paperSize':
        _paperSize = value;
        break;
      case 'autoGenerateReports':
        _autoGenerateReports = value;
        break;
      case 'reportFormat':
        _reportFormat = value;
        break;
      case 'emailReports':
        _emailReports = value;
        break;
    }

    await saveSettings();
  }

  Future<void> resetSettings() async {
    _isDarkMode = false;
    _language = 'ar';
    _currency = 'SAR';
    _dateFormat = 'dd/MM/yyyy';
    _timeFormat = '24';
    _notificationsEnabled = true;
    _soundEnabled = true;
    _vibrationEnabled = true;
    _lowStockAlerts = true;
    _expiryAlerts = true;
    _biometricAuth = false;
    _pinAuth = false;
    _pinCode = '';
    _autoLock = false;
    _autoLockTimeout = 5;
    _autoBackup = false;
    _backupFrequency = 'weekly';
    _backupToCloud = false;
    _printerType = 'thermal';
    _autoPrint = false;
    _paperSize = '80mm';
    _autoGenerateReports = false;
    _reportFormat = 'pdf';
    _emailReports = false;

    await saveSettings();
  }
}
