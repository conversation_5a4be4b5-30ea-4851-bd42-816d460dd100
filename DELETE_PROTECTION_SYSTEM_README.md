# نظام حماية الحذف - ATLAS2

## نظرة عامة

تم إضافة نظام حماية شامل لحذف العناصر المهمة في التطبيق (المنتجات، العملاء، الفواتير) يتطلب إدخال كلمة المرور الخاصة بالمستخدم.

## الميزات

### 🔒 حماية شاملة
- ✅ حماية حذف المنتجات
- ✅ حماية حذف العملاء  
- ✅ حماية حذف الفواتير
- ✅ حماية حذف البيانات العامة

### 🛡️ نظام مصادقة آمن
- كلمة مرور مخصصة للحذف
- تشفير كلمة المرور
- تخزين آمن في التخزين المحلي
- التحقق من صحة كلمة المرور

### ⚙️ إدارة سهلة
- تعيين كلمة المرور من شاشة الإعدادات
- تغيير كلمة المرور
- إعادة تعيين كلمة المرور
- واجهة مستخدم بسيطة وواضحة

## كيفية الاستخدام

### 1. تعيين كلمة المرور

1. انتقل إلى **الإعدادات** → **الأمان والحماية**
2. ابحث عن قسم **"كلمة المرور للحذف"**
3. اضغط على **"تعيين كلمة المرور"**
4. أدخل كلمة المرور الجديدة (6 أحرف على الأقل)
5. أكد كلمة المرور
6. اضغط **"حفظ"**

### 2. تغيير كلمة المرور

1. انتقل إلى **الإعدادات** → **الأمان والحماية**
2. ابحث عن قسم **"كلمة المرور للحذف"**
3. اضغط على **"تغيير كلمة المرور"**
4. أدخل كلمة المرور الحالية
5. أدخل كلمة المرور الجديدة
6. أكد كلمة المرور الجديدة
7. اضغط **"تغيير"**

### 3. حذف العناصر

عند محاولة حذف أي من العناصر المحمية:

1. سيظهر حوار **"تأكيد الحذف"**
2. أدخل كلمة المرور الخاصة بك
3. اضغط **"تأكيد الحذف"**
4. سيتم تنفيذ عملية الحذف

## الشاشات المحدثة

### 📱 شاشة تفاصيل العميل
- **الموقع**: `lib/features/customers/screens/customer_details_screen.dart`
- **التحديث**: إضافة زر "حذف العميل" مع نظام الحماية
- **الوظيفة**: حذف العميل بعد إدخال كلمة المرور

### 📱 شاشة تفاصيل الفاتورة
- **الموقع**: `lib/features/invoices/screens/invoice_details_screen.dart`
- **التحديث**: تحديث زر "حذف الفاتورة" لاستخدام نظام الحماية
- **الوظيفة**: حذف الفاتورة بعد إدخال كلمة المرور

### 📱 شاشة تفاصيل المنتج
- **الموقع**: `lib/features/products/screens/product_details_screen.dart`
- **التحديث**: تحديث زر "حذف المنتج" لاستخدام نظام الحماية
- **الوظيفة**: حذف المنتج بعد إدخال كلمة المرور

### 📱 شاشة أدوات المنتجات
- **الموقع**: `lib/features/products/screens/product_tools_screen.dart`
- **التحديث**: إضافة أزرار حذف محمية للمنتجات
- **الوظيفة**: حذف منتج واحد أو جميع المنتجات بعد إدخال كلمة المرور

## الملفات المضافة/المعدلة

### ملفات جديدة
- `lib/widgets/delete_protection_dialog.dart` - حوار حماية الحذف

### ملفات معدلة
- `lib/core/settings/app_settings.dart` - إضافة دوال إدارة كلمة المرور
- `lib/features/settings/screens/settings_screen.dart` - إضافة قسم كلمة المرور
- `lib/features/customers/screens/customer_details_screen.dart` - إضافة زر حذف محمي
- `lib/features/invoices/screens/invoice_details_screen.dart` - تحديث زر الحذف
- `lib/features/products/screens/product_details_screen.dart` - تحديث زر الحذف
- `lib/features/products/screens/product_tools_screen.dart` - إضافة أزرار حذف محمية

## الأمان

### تشفير البيانات
- كلمة المرور مشفرة في التخزين المحلي
- استخدام SharedPreferences مع تشفير إضافي
- حماية من الوصول غير المصرح به

### التحقق من الصحة
- التحقق من طول كلمة المرور (6 أحرف على الأقل)
- التحقق من تطابق كلمة المرور عند التأكيد
- التحقق من كلمة المرور الحالية عند التغيير

## رسائل الخطأ

### كلمة المرور غير صحيحة
```
كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.
```

### كلمة المرور غير معينة
```
لم يتم تعيين كلمة مرور للمستخدم. يرجى تعيين كلمة مرور أولاً.
```

### خطأ في التحقق
```
حدث خطأ في التحقق من كلمة المرور: [تفاصيل الخطأ]
```

## متطلبات النظام

- Flutter 3.0+
- Dart 2.17+
- shared_preferences package
- flutter_screenutil package

## التثبيت والإعداد

1. تأكد من تحديث جميع التبعيات
2. أعد تشغيل التطبيق
3. انتقل إلى الإعدادات لتعيين كلمة المرور الأولى
4. ابدأ في استخدام نظام الحماية

## استكشاف الأخطاء

### مشكلة: لا يمكن تعيين كلمة المرور
- تأكد من أن التطبيق لديه صلاحيات الكتابة
- تحقق من اتصال قاعدة البيانات
- أعد تشغيل التطبيق

### مشكلة: كلمة المرور لا تعمل
- تأكد من إدخال كلمة المرور الصحيحة
- تحقق من عدم وجود مسافات إضافية
- جرب إعادة تعيين كلمة المرور

### مشكلة: حوار الحماية لا يظهر
- تأكد من استيراد `delete_protection_dialog.dart`
- تحقق من استخدام `showDeleteProtectionDialog` بشكل صحيح
- تأكد من تعيين كلمة المرور أولاً

## الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك أسئلة:

1. تحقق من رسائل الخطأ في console
2. تأكد من تحديث جميع التبعيات
3. تحقق من صلاحيات التطبيق
4. راجع ملفات السجل للحصول على تفاصيل إضافية

## التطوير المستقبلي

### ميزات مقترحة
- دعم البصمة للحذف
- مستويات أمان متعددة
- سجل عمليات الحذف
- نسخ احتياطي تلقائي قبل الحذف
- تأكيد متعدد المستويات

### تحسينات الأمان
- تشفير أقوى لكلمة المرور
- انتهاء صلاحية كلمة المرور
- قفل التطبيق بعد محاولات فاشلة متعددة
- تشفير قاعدة البيانات بالكامل

## ملاحظات التطوير

### كيفية إضافة الحماية لشاشة جديدة

1. أضف import للـ delete protection dialog:
```dart
import '../../../widgets/delete_protection_dialog.dart';
```

2. استخدم `showDeleteProtectionDialog` بدلاً من `showDialog`:
```dart
showDeleteProtectionDialog(
  context: context,
  title: 'حذف العنصر',
  message: 'رسالة التحذير',
  itemName: 'اسم العنصر',
  onConfirm: () async {
    // منطق الحذف هنا
  },
);
```

### مثال على الاستخدام
```dart
void _showDeleteDialog(BuildContext context) {
  showDeleteProtectionDialog(
    context: context,
    title: 'حذف العميل',
    message: 'سيتم حذف العميل نهائياً من قاعدة البيانات.',
    itemName: customer.name ?? 'العميل المحدد',
    onConfirm: () async {
      try {
        final success = await customerService.deleteCustomer(customer.id!);
        if (success) {
          // نجح الحذف
          Navigator.of(context).pop();
        }
      } catch (e) {
        // معالجة الخطأ
      }
    },
  );
}
```

---

**ملاحظة**: هذا النظام يوفر حماية أساسية للحذف. للحصول على أمان أعلى، يُنصح بتفعيل جميع ميزات الأمان المتاحة في التطبيق.
