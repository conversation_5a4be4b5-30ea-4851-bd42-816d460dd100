import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/features/products/screens/products_list_screen.dart';
import '../lib/features/products/widgets/product_card.dart';
import '../lib/features/products/widgets/product_search_bar.dart';
import '../lib/features/products/widgets/add_product_dialog.dart';
import '../lib/features/products/widgets/product_actions_dialog.dart';
import '../lib/models/product_model.dart';

void main() {
  group('Products List Screen Tests', () {
    testWidgets('should display products list screen', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: const ProductsListScreen()));

      // التحقق من وجود عنوان الصفحة
      expect(find.text('المنتجات'), findsOneWidget);

      // التحقق من وجود زر الإضافة
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });

    testWidgets('should display search bar', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const ProductsListScreen()));

      // التحقق من وجود شريط البحث
      expect(find.byType(ProductSearchBar), findsOneWidget);
    });

    testWidgets('should display product card correctly', (
      WidgetTester tester,
    ) async {
      final testProduct = ProductModel(
        id: '1',
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'devices',
        code: 'TEST001',
        price: 100.0,
        cost: 80.0,
        quantity: 50,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductCard(product: testProduct, onAction: (action) {}),
          ),
        ),
      );

      // التحقق من عرض اسم المنتج
      expect(find.text('منتج تجريبي'), findsOneWidget);

      // التحقق من عرض الكود
      expect(find.text('TEST001'), findsOneWidget);

      // التحقق من عرض السعر
      expect(find.text('100.0 ر.س'), findsOneWidget);
    });

    testWidgets('should show add product dialog', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const AddProductDialog(),
                ),
                child: const Text('إضافة منتج'),
              ),
            ),
          ),
        ),
      );

      // الضغط على زر إضافة منتج
      await tester.tap(find.text('إضافة منتج'));
      await tester.pumpAndSettle();

      // التحقق من ظهور النافذة
      expect(find.text('إضافة منتج جديد'), findsOneWidget);
      expect(find.text('اسم المنتج *'), findsOneWidget);
      expect(find.text('كود المنتج *'), findsOneWidget);
    });

    testWidgets('should show product actions dialog', (
      WidgetTester tester,
    ) async {
      final testProduct = ProductModel(
        id: '1',
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'devices',
        code: 'TEST001',
        price: 100.0,
        cost: 80.0,
        quantity: 50,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) =>
                      ProductActionsDialog(product: testProduct),
                ),
                child: const Text('إجراءات المنتج'),
              ),
            ),
          ),
        ),
      );

      // الضغط على زر الإجراءات
      await tester.tap(find.text('إجراءات المنتج'));
      await tester.pumpAndSettle();

      // التحقق من ظهور النافذة
      expect(find.text('إجراءات سريعة'), findsOneWidget);
      expect(find.text('تفاصيل المنتج'), findsOneWidget);
      expect(find.text('تعديل المنتج'), findsOneWidget);
    });
  });

  group('Product Search Bar Tests', () {
    testWidgets('should filter products by category', (
      WidgetTester tester,
    ) async {
      final searchController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductSearchBar(
              searchController: searchController,
              selectedCategory: 'all',
              sortBy: 'name',
              sortAscending: true,
              showLowStockOnly: false,
              onSearchChanged: (query) {},
              onCategoryChanged: (category) {},
              onSortChanged: (sortBy) {},
              onSortDirectionChanged: () {},
              onLowStockFilterChanged: (value) {},
            ),
          ),
        ),
      );

      // التحقق من وجود أزرار التصفية
      expect(find.text('الكل'), findsOneWidget);
      expect(find.text('أجهزة طبية'), findsOneWidget);
      expect(find.text('مستهلكات'), findsOneWidget);
    });
  });

  group('Product Model Tests', () {
    test('should calculate profit margin correctly', () {
      final product = ProductModel(
        id: '1',
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'devices',
        code: 'TEST001',
        price: 100.0,
        cost: 80.0,
        quantity: 50,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // التحقق من حساب هامش الربح
      expect(product.profitMargin, 25.0); // (100-80)/80 * 100 = 25%
    });

    test('should determine stock status correctly', () {
      final product1 = ProductModel(
        id: '1',
        name: 'منتج متوفر',
        description: 'وصف المنتج',
        category: 'devices',
        code: 'TEST001',
        price: 100.0,
        cost: 80.0,
        quantity: 50,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final product2 = ProductModel(
        id: '2',
        name: 'منتج منخفض المخزون',
        description: 'وصف المنتج',
        category: 'devices',
        code: 'TEST002',
        price: 100.0,
        cost: 80.0,
        quantity: 5,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final product3 = ProductModel(
        id: '3',
        name: 'منتج نفذ المخزون',
        description: 'وصف المنتج',
        category: 'devices',
        code: 'TEST003',
        price: 100.0,
        cost: 80.0,
        quantity: 0,
        minQuantity: 10,
        piecesPerCarton: 1,
        unit: 'piece',
        barcode: '123456789',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // التحقق من حالة المخزون
      expect(product1.stockStatus, 'متوفر');
      expect(product2.stockStatus, 'منخفض');
      expect(product3.stockStatus, 'نفذ');
    });
  });
}
